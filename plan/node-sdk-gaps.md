# Gaps and Misimplementations in @packages/node-sdk

After analyzing the codebase, I've identified several gaps and misimplementations between the current implementation in `@packages/node-sdk/` and the expected structure shown in `@sample-structures/`. Here's a detailed breakdown:

## 1. Field Builders

### 1.1 Missing Field Types

- **File Field Type**: The `file()` field builder is used in sample structures (e.g., in `FileOperations.md`) but is not implemented in the SDK.
  ```typescript
  // Used in sample but not implemented
  fileUpload: file()
    .description('File to upload')
    .optional()
    .showIf((inputs) => inputs.operation === 'write'),
  ```

### 1.2 Missing Field Builder Methods

- **`validate()`**: Used in sample structures but not implemented in field builders:
  ```typescript
  filePath: string()
    .description('Path to the file')
    .validate((value) => {
      if (!value) throw new Error('File path is required');
    }),
  ```

- **`fromEnv()`**: Used in config fields but not implemented:
  ```typescript
  defaultDirectory: string()
    .description('Default directory for file operations')
    .default('./files')
    .fromEnv('FILE_OPERATIONS_DEFAULT_DIR'),
  ```

- **`transform()`**: Used in outputs but not implemented:
  ```typescript
  response: object({})
    .description('Response data')
    .transform((value, inputs) => {
      // Transform based on content type
      return inputs.parseResponse ? JSON.parse(value) : value;
    }),
  ```

### 1.3 Options Field Enhancement

- **Icon Support**: The sample structures show that options can have icons, but this is not implemented:
  ```typescript
  operation: options([
    {label: 'Read', value: 'read', icon: '🔍'}, // Icon support missing
    {label: 'Write', value: 'write', icon: '✍️'},
    // ...
  ])
  ```

## 2. Node Definition

### 2.1 Category Implementation

- **Multiple Categories**: In the sample structures, nodes can have multiple categories:
  ```typescript
  category: ['Storage'], // Array of categories
  ```
  But in the current implementation, only a single category is supported:
  ```typescript
  category: string; // Single category
  ```

### 2.2 Auth Implementation

- **Structured Auth**: The sample structures show a more structured auth implementation:
  ```typescript
  auth: ({apiKey}) => apiKey({
    provider: 'openai',
    place: 'header',
    name: 'Authorization',
  }),
  ```
  But the current implementation only supports a generic Record:
  ```typescript
  auth?: Record<string, unknown>;
  ```

## 3. Trigger Definition

### 3.1 ID Field

- **Missing ID Field**: The sample trigger has an ID field:
  ```typescript
  id: 'custom-trigger', // unique id for this trigger
  ```
  But the current implementation doesn't include an ID in the TriggerDefinition interface.

## 4. Server Implementation

### 4.1 Missing Endpoints

The sample structures show several endpoints that are not implemented in the current server:

- **Node Status Endpoint**: `GET /node/id/status`
- **Node Logs Endpoint**: `GET /node/id/logs`
- **Node Config Endpoint**: `GET /node/id/config`
- **Node Inputs Endpoint**: `GET /node/id/inputs`
- **Node Outputs Endpoint**: `GET /node/id/outputs`

### 4.2 Response Format

The sample endpoints show a different response format than what's currently implemented:

```typescript
// Sample structure
app.get('/nodes', (c) => {
  return c.json({
    nodes: {
      'node-id': {
        name: 'Node Name',
        description: 'Node Description',
        categories: ['category1', 'category2'],
        tags: ['tag1', 'tag2'],
      },
    },
  });
});

// Current implementation
this.app.get(`${basePath}/nodes`, (c) => {
  const nodeList = Array.from(this.nodes.values()).map((node) => ({
    id: node.definition.id,
    name: node.definition.name,
    description: node.definition.description,
    version: node.definition.version,
    category: node.definition.category,
    tags: node.definition.tags,
  }));
  return c.json(nodeList);
});
```

### 4.3 Execute Endpoint Response

The sample structures show additional fields in the execute endpoint response:

```typescript
// Sample structure includes these fields that are missing in implementation
delay: null, 
reExecute: false,
tryAgain: false,
retryDelay: null,
```

## 5. Project Structure

### 5.1 Index Files

The sample structures show index files for organizing nodes, executors, and triggers, but there's no clear implementation for handling these in the SDK:

```typescript
// Node index file example
import x from './x';
export default {
  'node-id': x,
}
```

## 6. CLI Integration

The CLI package has references to functions like `getServer` that don't exist in the current SDK implementation:

```typescript
// Apply the ezWorkflow server middleware
// This will use the imported components instead of scanning directories
app.route('/api/ezw', getServer({
  nodes,
  executors,
  triggers,
  handlers
}));
```

## Recommendations

1. Implement missing field builders and methods
2. Update the NodeDefinition interface to support multiple categories
3. Implement a structured auth system
4. Add ID field to TriggerDefinition
5. Implement missing server endpoints
6. Update response formats to match sample structures
7. Add support for index files
8. Implement the getServer middleware function
