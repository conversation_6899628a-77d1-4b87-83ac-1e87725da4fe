# Complete Rewrite Plan for node-sdk & cli Packages

## Overview

This document outlines the plan for a complete rewrite of the `@ezworkflow/node-sdk` and `@ezworkflow/cli` packages to match the sample structures exactly. The goal is to simplify the API, improve developer experience, and ensure consistency across all components.

## Core Principles

1. **Simplicity**: Minimize boilerplate and complexity
2. **Type Safety**: Provide strong typing without verbose type declarations
3. **Consistency**: Ensure all components follow the same patterns
4. **Developer Experience**: Make the API intuitive and easy to use

## Package: @ezworkflow/node-sdk

### 1. Core Types and Interfaces

#### 1.1 Basic Types
- `Logger`: For logging operations
- `NodeType`: Enum for different node types
- `DataType`: Enum for data types (string, number, boolean, etc.)
- `FieldType`: Enum for field types (text, select, etc.)

#### 1.2 Node-related Types
- `NodeDefinition`: Core structure for node definitions
- `NodeInput`: Structure for node inputs
- `NodeOutput`: Structure for node outputs
- `NodeConfig`: Structure for node configuration

#### 1.3 Execution Types
- `ExecutionContext`: Context provided to executors
- `ExecutionResult`: Result returned from executors

#### 1.4 Trigger Types
- `TriggerDefinition`: Structure for trigger definitions
- `TriggerType`: Enum for trigger types (http, socket, etc.)

### 2. Core Functions

#### 2.1 defineNode
Implement according to sample-structures/nodes/*.md:

```typescript
export default defineNode({
  id: 'unique-id',
  name: 'Node Name',
  description: 'Node description',
  version: '1.0.0',
  category: 'Category',
  tags: ['tag1', 'tag2'],

  inputs: ({string, options, object, boolean, number}) => ({
    // Input definitions
  }),

  outputs: ({object, array, stream, boolean, string}) => ({
    // Output definitions
  }),

  config: ({boolean, number, string, object}) => ({
    // Configuration definitions
  })
});
```

#### 2.2 defineExecutor
Implement according to sample-structures/executors/DatabaseConnection.md:
- config will be provided if used added it in defineNode
- auth will be provided if used added it in defineNode

```typescript
export default defineExecutor('node-id', async ({inputs, config, logger, auth}) => {
  // Executor logic
});
```

#### 2.3 defineTrigger
Implement according to sample-structures/trigger/customTrigger.md:

```typescript
export default defineTrigger({
  name: 'Trigger Name',
  version: '1.0.0',
  categories: ['category'],
  tags: ['tag1', 'tag2'],
  type: 'http', // or 'socket'
  registarUrl: 'https://example.com',

  expect: ({object, array}) => ({
    // Expected data structure
    messages: array(string()).required() // array(number()) | array(object()) and so on
    contact: object({
      name: string(),
      email: string().required(),
      phone: string(),
    }).required(),
    tags: array(string()) // this is optional as here don't have the required()
  })
});
```

### 3. Field Builders

Create builder functions for defining fields with a fluent API:

- `string()`: For string fields
- `number()`: For number fields
- `boolean()`: For boolean fields
- `options()`: For select fields
- `object()`: For object fields
- `array()`: For array fields
- `stream()`: For stream fields

Each builder should support methods like:
- `.description()`
- `.default()`
- `.required()`
- `.optional()`
- `.showIf()`
- `.group()`
- `.collapsible()`
- etc.

### 4. Server Implementation

- Create a simplified server implementation
- Support for registering nodes, executors, and triggers
- Automatic route generation based on node definitions like `GET /node/id` to get information about the node, `POST /node/id` to trigger the node and get the response with defined outputs and all logs, `GET /node/id/status` to get the status of the node, `GET /node/id/logs` to get the execution logs of the node, `GET /node/id/config` to get the config of the node, `GET /node/id/inputs` to get the inputs of the node, `GET /node/id/outputs` to get the outputs of the node. For all this routes end user shouldn't interact with the routes directly, they will be used by the ezWorkflow platform to get information about the node.
- Proper error handling and logging

## Package: @ezworkflow/cli

### 1. Commands

#### 1.1 create-project
Generate a new project with the exact structure from samples:
- Run Hono.js's project creation command and let the user interact directly with its prompts
- After Hono project creation is complete, ask for author, categories, tags, and version
- Store project configuration in a separate config file (ezworkflow.config.json)
- Add all ezWorkflow node routes to the Hono.js app

#### 1.2 create node
Generate a new node with the exact structure from samples:
- Node definition file
- Executor file
- Tests

#### 1.3 create trigger
Generate a new trigger with the exact structure from samples

### 2. Templates

Create templates for:
- Node definitions
- Executors
- Triggers
- Tests

All templates should follow the exact structure from the samples.

## Implementation Tasks

### Phase 1: Core SDK Implementation ✅

1. [x] Set up project structure for node-sdk
2. [x] Implement core types and interfaces
3. [x] Implement defineNode function
4. [x] Implement defineExecutor function
5. [x] Implement defineTrigger function
6. [x] Implement field builders
7. [ ] Create basic tests for core functions

### Phase 2: Server Implementation ✅

1. [x] Implement server for local development
2. [x] Add support for node registration
3. [x] Add support for executor registration
4. [x] Add support for trigger registration
5. [x] Implement route generation
6. [x] Add error handling and logging
7. [ ] Create tests for server functionality

### Phase 3: CLI Implementation ✅

1. [x] Set up project structure for CLI
2. [x] Create templates for nodes, executors, and triggers
3. [x] Implement `npx ezw create-project` command:
   - Integrate with Hono.js project creation
   - Collect project metadata (author, categories, tags, version)
   - Configure Hono.js app with ezWorkflow routes
   - Set up project structure for nodes, executors, and triggers
4. [x] Implement `npx ezw create-node` command
5. [x] Implement `npx ezw create-trigger` command
6. [ ] Create tests for CLI commands

### Phase 4: Documentation and Examples 🔄

1. [x] Create comprehensive documentation
2. [ ] Create example nodes
3. [ ] Create example triggers
4. [ ] Create tutorials for common use cases


## Success Criteria

1. ✅ All functions match the sample structures exactly
2. ✅ Strong typing without verbose type declarations
3. ✅ Simplified API with minimal boilerplate
4. 🔄 Comprehensive documentation and examples
5. ❌ All tests passing

## Current Status

The initial implementation of the rewrite for both packages is complete. The core functionality is implemented according to the sample structures, with a focus on simplicity, type safety, and developer experience.

### Completed
- Core SDK implementation (types, functions, field builders)
- Server implementation (node registration, route generation)
- CLI implementation (create-project, create-node, create-trigger commands)
- Basic documentation

### Remaining Tasks
- Create tests for all components
- Create example nodes and triggers
- Create tutorials for common use cases

### Important Notes
- All implementations follow the sample structures exactly
- No packages that can't run in Cloudflare Worker are used
- The create-project command integrates with Hono.js's project creation
