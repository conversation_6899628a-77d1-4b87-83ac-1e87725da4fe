{"name": "ezworkflow", "private": true, "scripts": {"build": "turbo run build", "dev": "clear && turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "check-types": "turbo run check-types"}, "devDependencies": {"prettier": "^3.5.3", "turbo": "^2.5.3", "typescript": "5.8.3"}, "engines": {"node": ">=18"}, "packageManager": "bun@1.2.12", "workspaces": ["packages/*", "examples/*"]}