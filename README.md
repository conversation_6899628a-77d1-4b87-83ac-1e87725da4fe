# ezWorkflow Platform

A comprehensive workflow builder platform that allows users to create powerful API workflows by connecting specialized nodes from various sources, all within an intuitive visual interface.

## Overview

The ezWorkflow platform functions like a component registry but doesn't download user source code. Instead, it connects to the user's own deployed services to discover and use workflow nodes.

## Project Structure

This monorepo contains the following packages and examples:

### Packages

- `packages/node-sdk`: SDK for creating and exposing workflow nodes through a Hono.js server

### Examples

- `examples/basic-math-server`: Example server with basic math operation nodes
- `examples/openai-server`: Example server with OpenAI integration nodes

## Getting Started

### Prerequisites

- Node.js 18+
- Bun 1.0+

### Installation

```bash
# Clone the repository
git clone https://github.com/yourusername/ezWorkflow.git
cd ezWorkflow

# Install dependencies
bun install
```

### Building the Packages

```bash
# Build all packages
bun run build

# Build a specific package
cd packages/node-sdk
bun run build
```

### Running the Examples

```bash
# Run the basic math server example
cd examples/basic-math-server
bun run dev

# Run the OpenAI server example
cd examples/openai-server
bun run dev
```

## Using the Node SDK

The Node SDK allows you to create and expose workflow nodes through a Hono.js server. Here's a simple example:

```typescript
import { serve } from "@hono/node-server";
import {
  ezwServer,
  defineNode,
  string,
  number,
  boolean,
} from "@ezworkflow/node-sdk";

// Define a simple node
const helloWorldNode = defineNode((builder) =>
  builder
    .id("hello-world")
    .name("Hello World")
    .description("A simple hello world node")
    .version("1.0.0")
    .category("Basic")
    .inputs((fields) => ({
      name: fields
        .string("name", "Name")
        .description("Your name")
        .default("World"),
    }))
    .outputs((fields) => ({
      message: fields
        .string("message", "Message")
        .description("Greeting message"),
    }))
    .execute(async ({ inputs }) => {
      const name = inputs.name || "World";
      return {
        success: true,
        outputs: {
          message: `Hello, ${name}!`,
        },
      };
    })
);

// Create a server
const server = new ezwServer({
  name: "Basic Nodes",
  version: "1.0.0",
  description: "Basic node examples",
});

// Register the node
server.registerNode(helloWorldNode);

// Start the server
serve({
  fetch: server.getApp().fetch,
  port: 3000,
});
```

## API Documentation

For detailed API documentation, see the README files in each package:

- [Node SDK Documentation](packages/node-sdk/README.md)

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.
