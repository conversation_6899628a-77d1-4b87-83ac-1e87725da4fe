
```typescript
export default defineNode({
  id: 'random uinque id', // Here we have to generate 32 char unique ID
  name: 'File Operations',
  description: 'Perform operations on files such as read, write, append, and delete',
  version: '1.0.0',
  category: ['Storage'],
  tags: ['file', 'storage', 'io'],

  inputs: ({string, file, options, boolean, object}) => ({
    // Operation selection
    operation: options([
      {label: 'Read', value: 'read', icon: '🔍'}, // We should be able to use icon from iconify.design and it's a string and optional
      {label: 'Write', value: 'write', icon: '✍️'},
      {label: 'Append', value: 'append', icon: '➕'},
      {label: 'Delete', value: 'delete', icon: '🗑️'},
      {label: 'List', value: 'list', icon: '📄'},
      {label: 'Copy', value: 'copy', icon: '📋'},
      {label: 'Move', value: 'move', icon: '🔄'},
    ])
      .description('Operation to perform on the file')
      .default('read'),

    // File path for operations
    filePath: string()
      .description('Path to the file')
      .validate((value) => {
        if (!value) throw new Error('File path is required');
      }),

    // Content for write/append operations
    content: string()
      .description('Content to write to the file')
      .optional()
      .showIf((inputs) => ['write', 'append'].includes(inputs.operation)),

    // File upload for write operations
    fileUpload: file()
      .description('File to upload')
      .optional()
      .showIf((inputs) => inputs.operation === 'write'),

    // Destination for copy/move operations
    destination: string()
      .description('Destination path')
      .optional()
      .showIf((inputs) => ['copy', 'move'].includes(inputs.operation)),

    // Format options
    format: options([
      {label: 'Text', value: 'text'},
      {label: 'JSON', value: 'json'},
      {label: 'Binary', value: 'binary'},
      {label: 'Base64', value: 'base64'},
    ])
      .description('Format to read/write the file')
      .default('text')
      .showIf((inputs) => ['read', 'write', 'append'].includes(inputs.operation)),

    // Advanced options
    options: object({
      createDirectory: boolean().description('Create directory if it doesn\'t exist').default(false),
      overwrite: boolean().description('Overwrite existing file').default(true),
      encoding: options(['utf8', 'ascii', 'binary']).description('File encoding').default('utf8'),
      recursive: boolean().description('Recursive operation').default(false),
    })
      .description('Advanced options')
      .optional()
      .collapsible(true),
  }),

  outputs: ({string, boolean, object, array}) => ({
    // Success indicator
    success: boolean()
      .description('Whether the operation was successful'),

    // Content for read operations
    content: string()
      .description('File content')
      .showIf((inputs) => inputs.operation === 'read'),

    // File information
    fileInfo: object({
      name: string().description('File name'),
      path: string().description('File path'),
      size: string().description('File size'),
      type: string().description('File type'),
      lastModified: string().description('Last modified date'),
    })
      .description('File information')
      .showIf((inputs) => ['read', 'write', 'append', 'copy', 'move'].includes(inputs.operation)),

    // File list for list operations
    files: array(object({
      name: string().description('File name'),
      path: string().description('File path'),
      size: string().description('File size'),
      type: string().description('File type'),
      lastModified: string().description('Last modified date'),
    }))
      .description('List of files')
      .showIf((inputs) => inputs.operation === 'list'),

    // Error information
    error: string()
      .description('Error message if operation failed')
      .optional(),
  }),

  config: ({boolean, number, string}) => ({
    // Default directory
    defaultDirectory: string()
      .description('Default directory for file operations')
      .default('./files')
      .fromEnv('FILE_OPERATIONS_DEFAULT_DIR'),

    // Security settings
    security: object({
      allowAbsolutePaths: boolean().description('Allow absolute paths').default(false),
      allowParentDirectoryTraversal: boolean().description('Allow parent directory traversal').default(false),
      allowedExtensions: array(string()).description('Allowed file extensions').default(['txt', 'json', 'csv']),
    })
      .description('Security settings')
      .collapsible(true),

    // Performance settings
    performance: object({
      maxFileSize: number().description('Maximum file size in bytes').default(10 * 1024 * 1024), // 10MB
      bufferSize: number().description('Buffer size for read/write operations').default(64 * 1024), // 64KB
      timeout: number().description('Timeout for operations in ms').default(30000), // 30 seconds
    })
      .description('Performance settings')
      .collapsible(true),
  })
});
```