```typescript
export default defineNode({
  id: 'random uinque id', // Here we have to generate 32 char unique ID
  name: 'Streaming Data Processor',
  description: 'Process streaming data with support for various protocols and transformations',
  version: '1.0.0',
  category: 'Data Processing',
  tags: ['stream', 'realtime', 'data', 'processing'],

  inputs: ({string, options, object, boolean, number}) => ({
    // Stream source selection
    sourceType: options([
      {label: 'WebSocket', value: 'websocket'},
      {label: 'Server-Sent Events (SSE)', value: 'sse'},
      {label: 'MQTT', value: 'mqtt'},
      {label: 'Kafka', value: 'kafka'},
      {label: 'Redis Pub/Sub', value: 'redis'},
      {label: 'HTTP Stream', value: 'http'},
    ]).label('Stream Source')
      .description('Type of stream source')
      .default('websocket'),

    // Connection parameters
    connection: object({
      // Common parameters
      url: string()
        .label('Connection URL')
        .description('Connection URL or endpoint')
        .showIf((parent, inputs) => ['websocket', 'sse', 'http'].includes(inputs.sourceType)),

      // MQTT specific
      mqttOptions: object({
        host: string()
          .label('MQTT Broker Host')
          .description('MQTT broker host')
          .default('localhost'),
        port: number()
          .label('MQTT Broker Port')
          .description('MQTT broker port')
          .default(1883),
        clientId: string()
          .label('MQTT Client ID')
          .description('MQTT client ID')
          .default(() => `mqtt-client-${Date.now()}`),
        username: string()
          .label('MQTT Username')
          .description('MQTT username')
          .optional(),
        password: string()
          .label('MQTT Password')
          .description('MQTT password')
          .secret(true)
          .optional(),
        topic: string()
          .label('MQTT Topic')
          .description('MQTT topic to subscribe to'),
      })
        .description('MQTT connection options')
        .showIf((parent, inputs) => inputs.sourceType === 'mqtt'),

      // Kafka specific
      kafkaOptions: object({
        brokers: array(string()).description('Kafka brokers').label('Kafka Brokers'),
        clientId: string()
          .label('Kafka Client ID')
          .description('Kafka client ID')
          .default(() => `kafka-client-${Date.now()}`),
        topic: string()
          .label('Kafka Topic')
          .description('Kafka topic to subscribe to'),
        groupId: string()
          .label('Consumer Group ID')
          .description('Consumer group ID').default(() => `group-${Date.now()}`),
      })
        .description('Kafka connection options')
        .group('Kafka Connection')
        .collapsible(true)
        .showIf((parent, inputs) => inputs.sourceType === 'kafka'),

      // Redis specific
      redisOptions: object({
        host: string()
          .label('Redis Host')
          .description('Redis host')
          .default('localhost'),
        port: number()
          .label('Redis Port')
          .description('Redis port')
          .default(6379),
        password: string().description('Redis password').secret(true).optional(),
        channel: string().description('Redis channel to subscribe to'),
      })
        .description('Redis connection options')
        .showIf((parent, inputs) => inputs.sourceType === 'redis'),
    })
      .description('Connection parameters')
      .group('Connection'),

    // Processing options
    processing: object({
      // Data format
      format: options([
        {label: 'JSON', value: 'json'},
        {label: 'Text', value: 'text'},
        {label: 'Binary', value: 'binary'},
        {label: 'CSV', value: 'csv'},
      ])
        .label('Data Format')
        .description('Expected data format')
        .default('json'),

      // Transformation
      transform: string()
        .description('JavaScript transformation function')
        .optional()
        .multiline(true)
        .showIf((parent) => parent.format !== 'binary')
        .placeholder('(data) => { return data; }'),

      // Filtering
      filter: string()
        .description('JavaScript filter function')
        .optional()
        .multiline(true)
        .showIf((parent) => parent.format !== 'binary')
        .placeholder('(data) => { return true; }'),

      // Batching
      batchSize: number()
        .description('Number of messages to batch')
        .default(1)
        .min(1)
        .max(1000),

      batchTimeWindow: number()
        .description('Time window for batching in ms')
        .default(1000)
        .min(100)
        .showIf((parent) => parent.batchSize > 1),
    })
      .description('Processing options')
      .group('Processing'),

    // Error handling
    errorHandling: object({
      retryOnError: boolean()
        .description('Retry on connection error')
        .default(true),

      maxRetries: number()
        .description('Maximum retry attempts')
        .default(5)
        .showIf((parent) => parent.retryOnError),

      retryDelay: number()
        .description('Delay between retries in ms')
        .default(1000)
        .showIf((parent) => parent.retryOnError),

      errorAction: options([
        {label: 'Log and Continue', value: 'continue'},
        {label: 'Skip Message', value: 'skip'},
        {label: 'Stop Processing', value: 'stop'},
      ])
        .description('Action to take on processing error')
        .default('continue'),
    })
      .description('Error handling options')
      .group('Error Handling')
      .collapsible(true),
  }),

  outputs: ({object, array, stream, boolean, string}) => ({
    // Processed data stream
    dataStream: stream()
      .label('Data Stream')
      .description('Stream of processed data')
      .format((inputs) => inputs.processing.format)
      .chunkSize(1024),

    // Latest processed item
    latestItem: object({})
      .label('Latest Processed Item')
      .description('Latest processed item'),

    // Batch of processed items
    batch: array(object({}))
      .label('Batch of Processed Items')
      .description('Batch of processed items')
      .showIf((inputs) => inputs.processing.batchSize > 1),

    // Connection status
    connectionStatus: object({
      connected: boolean()
        .label('Connected')
        .description('Whether connected to the stream source'),
      source: string()
        .label('Stream Source')
        .description('Stream source'),
      uptime: number()
        .label('Uptime')
        .description('Connection uptime in seconds'),
    })
      .label('Connection Status')
      .description('Connection status'),

    // Error information
    error: object({
      message: string()
        .label('Error Message')
        .description('Error message'),
      code: string()
        .label('Error Code')
        .description('Error code'),
      timestamp: string()
        .label('Error Timestamp')
        .description('Error timestamp'),
    })
      .description('Error information')
      .optional(),

    // Metrics
    metrics: object({
      messagesReceived: number().description('Number of messages received'),
      messagesProcessed: number().description('Number of messages processed'),
      messageRate: number().description('Messages per second'),
      errors: number().description('Number of errors'),
      avgProcessingTime: number().description('Average processing time in ms'),
    })
      .description('Stream processing metrics')
      .metadata({
        displayFormat: 'chart',
        chartType: 'line',
        refreshRate: 1000,
      }),
  }),

  config: ({boolean, number, string, object}) => ({
    // Connection settings
    connection: object({
      reconnectAutomatically: boolean()
        .label('Reconnect Automatically')
        .description('Reconnect automatically on disconnect')
        .default(true),
      keepAlive: boolean()
        .label('Keep Alive')
        .description('Send keep-alive messages')
        .default(true),
      keepAliveInterval: number()
        .label('Keep-Alive Interval')
        .description('Keep-alive interval in ms').default(30000)
        .showIf((parent) => parent.keepAlive),
      timeout: number()
        .label('Timeout')
        .description('Connection timeout in ms')
        .default(10000),
    })
      .label('Connection Settings')
      .description('Connection settings')
      .collapsible(true),

    // Processing settings
    processing: object({
      maxConcurrency: number()
        .label('Max Concurrency')
        .description('Maximum concurrent processing')
        .default(1),
      bufferSize: number()
        .label('Buffer Size')
        .description('Buffer size for incoming messages')
        .default(1000),
      processInOrder: boolean()
        .label('Process In Order')
        .description('Process messages in order')
        .default(true),
      dropOnBufferFull: boolean()
        .label('Drop On Buffer Full')
        .description('Drop messages when buffer is full')
        .default(false),
    })
      .description('Processing settings')
      .collapsible(true),

    // Backpressure settings
    backpressure: object({
      enabled: boolean()
        .label('Enabled')
        .description('Enable backpressure handling')
        .default(true),
      highWatermark: number()
        .label('High Watermark')
        .description('High watermark for backpressure')
        .default(1000),
      lowWatermark: number().description('Low watermark for backpressure').default(100),
    })
      .description('Backpressure settings')
      .collapsible(true),

    // Logging settings
    logging: object({
      logLevel: options(['error', 'warn', 'info', 'debug'])
        .label('Log Level')
        .description('Log level')
        .default('info'),
      logMessageContent: boolean()
        .label('Log Message Content')
        .description('Log message content')
        .default(false),
      logMetrics: boolean()
        .label('Log Metrics')
        .description('Log metrics periodically')
        .default(true),
      metricsInterval: number()
        .label('Metrics Interval')
        .description('Metrics logging interval in ms')
        .default(60000)
        .showIf((parent) => parent.logMetrics),
    })
      .description('Logging settings')
      .label('Logging Settings')
      .collapsible(true),
  })
});
```