
```typescript
export default defineNode({
  id: 'random uinque id', // Here we have to generate 32 char unique ID
  name: 'OpenAI Chat',
  description: 'Generate text using OpenAI chat models',
  version: '1.0.0',
  category: 'AI',

  auth: ({api<PERSON>ey}) => apiKey({
    provider: 'openai',
    place: 'header',
    name: 'Authorization',
  }),

  inputs: ({string, number, options, array}) => ({
    model: options([
      {label: 'GPT-4', value: 'gpt-4'},
      {label: 'GPT-4 Turbo', value: 'gpt-4-turbo'},
      {label: 'GPT-3.5 Turbo', value: 'gpt-3.5-turbo'},
    ])
      .description('The model to use')
      .default('gpt-3.5-turbo'),

    messages: array()
      .description('The messages to send to the model')
      .validate((value) => {
        if (!Array.isArray(value) || value.length === 0) {
          throw new Error('Messages must be a non-empty array');
        }
      }),

    temperature: number().range()
      .description('Controls randomness (0-2)')
      .default(0.7)
      .validate((value) => {
        if (value < 0 || value > 2) {
          throw new Error('Temperature must be between 0 and 2');
        }
      }),

    maxTokens: number()
      .description('Maximum number of tokens to generate')
      .optional(),
  }),

  outputs: ({string, object, array}) => ({
    text: string().description('Generated text'),
    fullResponse: object({}).description('Full API response'),
    usage: object({}).description('Token usage information'),
  })
});
```