
```typescript
export default defineNode({
  id: 'random uinque id', // Here we have to generate 32 char unique ID
  name: 'HTTP Request',
  description: 'Make HTTP requests to external APIs',
  version: '1.0.0',
  category: 'API',
  tags: ['http', 'api', 'request', 'fetch'],

  inputs: ({string, options, object, boolean, number, array}) => ({
    url: string()
      .description('URL to make the request to')
      .validate((value) => {
        try {
          new URL(value);
        } catch (e) {
          throw new Error('Invalid URL format');
        }
      }),

    method: options([
      {label: 'GET', value: 'get'},
      {label: 'POST', value: 'post'},
      {label: 'PUT', value: 'put'},
      {label: 'DELETE', value: 'delete'},
      {label: 'PATCH', value: 'patch'},
      {label: 'HEAD', value: 'head'},
      {label: 'OPTIONS', value: 'options'},
    ])
      .description('HTTP method')
      .default('get'),

    headers: object({})
      .description('HTTP headers')
      .optional(),

    body: string()
      .description('Request body')
      .optional()
      .showIf((inputs) => ['post', 'put', 'patch'].includes(inputs.method)),

    jsonBody: object({})
      .description('JSON request body')
      .optional()
      .showIf((inputs) => ['post', 'put', 'patch'].includes(inputs.method) && !inputs.body),

    params: object({})
      .description('Query parameters')
      .optional(),

    timeout: number()
      .description('Request timeout in milliseconds')
      .default(5000)
      .min(100)
      .max(60000),

    retryOptions: object({
      enabled: boolean().description('Enable retry on failure').default(false),
      maxRetries: number().description('Maximum number of retries').default(3).min(1).max(10)
        .showIf((parent) => parent.enabled),
      retryDelay: number().description('Delay between retries in ms').default(1000).min(100)
        .showIf((parent) => parent.enabled),
      retryStatusCodes: array(number()).description('Status codes to retry on').default([429, 503])
        .showIf((parent) => parent.enabled),
    })
      .description('Retry configuration')
      .group('Advanced Options')
      .collapsible(true),

    streaming: boolean()
      .description('Stream the response')
      .default(false)
      .group('Advanced Options'),

    followRedirects: boolean()
      .description('Automatically follow redirects')
      .default(true)
      .group('Advanced Options'),
  }),

  outputs: ({object, number, string, stream, array}) => ({
    // Basic response data
    response: object({})
      .description('Response data')
      .transform((value, inputs) => {
        // Transform based on content type
        return inputs.parseResponse ? JSON.parse(value) : value;
      }),

    // Status information
    status: number()
      .description('HTTP status code'),

    statusText: string()
      .description('HTTP status text'),

    headers: object({})
      .description('Response headers'),

    // Streaming response
    responseStream: stream()
      .description('Response as a stream')
      .format('binary')
      .showIf((inputs) => inputs.streaming === true),

    // Timing information
    timing: object({
      total: number().description('Total request time (ms)'),
      dns: number().description('DNS lookup time (ms)'),
      tcp: number().description('TCP connection time (ms)'),
      tls: number().description('TLS handshake time (ms)'),
      request: number().description('Request send time (ms)'),
      firstByte: number().description('Time to first byte (ms)'),
      download: number().description('Download time (ms)'),
    })
      .description('Request timing information')
      .metadata({
        displayFormat: 'chart',
        chartType: 'bar'
      }),

    // Error information
    error: object({
      message: string().description('Error message'),
      code: string().description('Error code'),
      stack: string().description('Error stack trace')
        .showIf((inputs, config) => config.debug.includeStackTraces),
    })
      .description('Error details if request failed')
      .optional(),

    // Retry information
    retryInfo: object({
      attempts: number().description('Number of retry attempts made'),
      success: boolean().description('Whether retry was successful'),
      lastError: string().description('Last error before success')
    })
      .description('Retry information')
      .showIf((inputs) => inputs.retryOptions.enabled),
  }),

  config: ({boolean, number, object, options}) => ({
    // Debug settings
    debug: object({
      logRequests: boolean().description('Log all requests').default(false),
      logResponses: boolean().description('Log all responses').default(false),
      includeStackTraces: boolean().description('Include stack traces in errors').default(false),
      logLevel: options(['error', 'warn', 'info', 'debug']).description('Log level').default('error')
    })
      .description('Debug settings')
      .collapsible(true),

    // Performance settings
    performance: object({
      keepAlive: boolean().description('Use HTTP keep-alive').default(true),
      timeout: number().description('Default timeout (ms)').default(30000).fromEnv('HTTP_TIMEOUT'),
      maxRedirects: number().description('Maximum redirects to follow').default(5),
      maxContentLength: number().description('Maximum content length (bytes)').default(10485760) // 10MB
    })
      .description('Performance settings')
      .collapsible(true),

    // Security settings
    security: object({
      validateSSL: boolean().description('Validate SSL certificates').default(true),
      allowInsecureRedirects: boolean().description('Allow redirects to insecure URLs').default(false)
    })
      .description('Security settings')
      .collapsible(true),
  }),

  auth: ({apiKey, basic, bearer, oauth}) => ({
    // Support multiple auth types
    apiKey: apiKey({
      name: 'X-API-Key',
      place: 'header'
    }),

    basic: basic(),

    bearer: bearer(),

    oauth: oauth({
      provider: 'generic',
      scopes: ['read', 'write']
    })
  })
});
```