
```typescript
export default defineNode({
  id: 'random uinque id', // Here we have to generate 32 char unique ID
  name: 'Database Connection',
  description: 'Connect to and query various database systems',
  version: '1.0.0',
  category: ['Database'],
  tags: ['database', 'sql', 'nosql', 'query'],

  inputs: ({string, options, object, boolean, number}) => ({
    // Database type selection
    databaseType: options([
      {label: 'PostgreSQL', value: 'postgres'},
      {label: 'MySQL', value: 'mysql'},
      {label: 'SQLite', value: 'sqlite'},
      {label: 'MongoDB', value: 'mongodb'},
      {label: 'Redis', value: 'redis'},
      {label: 'Elasticsearch', value: 'elasticsearch'},
    ])
      .description('Type of database to connect to')
      .default('postgres'),

    // Connection parameters
    connection: object({
      // Common parameters
      host: string().description('Database host').showIf((parent) => parent.databaseType !== 'sqlite'),
      port: number().description('Database port').showIf((parent) => parent.databaseType !== 'sqlite'),
      username: string().description('Database username').showIf((parent) => parent.databaseType !== 'sqlite'),
      password: string().description('Database password').secret(true).showIf((parent) => parent.databaseType !== 'sqlite'),
      database: string().description('Database name'),

      // SQLite specific
      filePath: string().description('Path to SQLite database file').showIf((parent) => parent.databaseType === 'sqlite'),

      // MongoDB specific
      connectionString: string().description('MongoDB connection string').showIf((parent) => parent.databaseType === 'mongodb'),

      // Redis specific
      redisOptions: object({
        keyPrefix: string().description('Key prefix').optional(),
        password: string().description('Redis password').secret(true).optional(),
        db: number().description('Redis database number').default(0),
      }).description('Redis specific options').showIf((parent) => parent.databaseType === 'redis'),
    })
      .description('Database connection parameters')
      .group('Connection'),

    // Operation selection
    operation: options([
      {label: 'Query', value: 'query'},
      {label: 'Execute', value: 'execute'},
      {label: 'Insert', value: 'insert'},
      {label: 'Update', value: 'update'},
      {label: 'Delete', value: 'delete'},
      {label: 'List Tables', value: 'listTables'},
      {label: 'Describe Table', value: 'describeTable'},
    ])
      .description('Operation to perform')
      .default('query')
      .group('Operation'),

    // Query for SQL databases
    query: string()
      .description('SQL query to execute')
      .optional()
      .showIf((inputs) =>
        ['query', 'execute'].includes(inputs.operation) &&
        ['postgres', 'mysql', 'sqlite'].includes(inputs.databaseType)
      )
      .group('Operation'),

    // Collection/table name
    table: string()
      .description('Table or collection name')
      .optional()
      .showIf((inputs) =>
        ['insert', 'update', 'delete', 'describeTable'].includes(inputs.operation) ||
        (inputs.operation === 'query' && ['mongodb', 'elasticsearch'].includes(inputs.databaseType))
      )
      .group('Operation'),

    // Data for insert/update operations
    data: object({})
      .description('Data to insert or update')
      .optional()
      .showIf((inputs) => ['insert', 'update'].includes(inputs.operation))
      .group('Operation'),

    // Where clause for update/delete operations
    where: object({})
      .description('Filter criteria')
      .optional()
      .showIf((inputs) => ['update', 'delete'].includes(inputs.operation) ||
        (inputs.operation === 'query' && ['mongodb', 'elasticsearch'].includes(inputs.databaseType))
      )
      .group('Operation'),

    // Advanced options
    options: object({
      // Common options
      timeout: number().description('Query timeout in milliseconds').default(30000),

      // SQL specific
      parameterized: boolean().description('Use parameterized queries').default(true)
        .showIf((parent, inputs) => ['postgres', 'mysql', 'sqlite'].includes(inputs.databaseType)),

      // MongoDB specific
      projection: object({}).description('MongoDB projection').optional()
        .showIf((parent, inputs) => inputs.databaseType === 'mongodb'),
      sort: object({}).description('MongoDB sort').optional()
        .showIf((parent, inputs) => inputs.databaseType === 'mongodb'),
      limit: number().description('Result limit').optional()
        .showIf((parent, inputs) => inputs.databaseType === 'mongodb'),

      // Connection pool options
      useConnectionPool: boolean().description('Use connection pooling').default(true),
      maxConnections: number().description('Maximum connections in pool').default(10)
        .showIf((parent) => parent.useConnectionPool),
      idleTimeoutMillis: number().description('Connection idle timeout').default(30000)
        .showIf((parent) => parent.useConnectionPool),
    })
      .description('Advanced options')
      .optional()
      .collapsible(true)
      .group('Advanced'),
  }),

  outputs: ({object, array, number, boolean, string}) => ({
    // Query results
    results: array(object({}))
      .description('Query results')
      .showIf((inputs) => ['query', 'listTables', 'describeTable'].includes(inputs.operation)),

    // Execution results
    affectedRows: number()
      .description('Number of affected rows')
      .showIf((inputs) => ['execute', 'insert', 'update', 'delete'].includes(inputs.operation)),

    // Insert ID for insert operations
    insertId: string()
      .description('ID of inserted record')
      .optional()
      .showIf((inputs) => inputs.operation === 'insert'),

    // Success indicator
    success: boolean()
      .description('Whether the operation was successful'),

    // Connection status
    connectionInfo: object({
      connected: boolean().description('Whether connected to the database'),
      database: string().description('Connected database name'),
      version: string().description('Database version'),
    })
      .description('Connection information'),

    // Error information
    error: object({
      message: string().description('Error message'),
      code: string().description('Error code'),
      sqlState: string().description('SQL state').optional(),
    })
      .description('Error information if operation failed')
      .optional(),

    // Performance metrics
    metrics: object({
      executionTime: number().description('Query execution time in ms'),
      connectionTime: number().description('Connection establishment time in ms'),
      rowsProcessed: number().description('Number of rows processed'),
    })
      .description('Performance metrics')
      .metadata({
        displayFormat: 'chart',
        chartType: 'bar'
      }),
  }),

  config: ({boolean, number, string, object}) => ({
    // Connection pooling
    connectionPool: object({
      enabled: boolean().description('Enable connection pooling').default(true),
      min: number().description('Minimum connections').default(2),
      max: number().description('Maximum connections').default(10),
      idleTimeout: number().description('Idle timeout in ms').default(10000),
    })
      .description('Connection pooling configuration')
      .collapsible(true),

    // Query settings
    query: object({
      timeout: number().description('Query timeout in ms').default(30000),
      maxRows: number().description('Maximum rows to return').default(1000),
      logQueries: boolean().description('Log queries').default(false),
      preparedStatements: boolean().description('Use prepared statements').default(true),
    })
      .description('Query settings')
      .collapsible(true),

    // Security settings
    security: object({
      validateQueries: boolean().description('Validate queries for SQL injection').default(true),
      allowedTables: array(string()).description('Allowed tables').optional(),
      readOnly: boolean().description('Read-only mode').default(false),
    })
      .description('Security settings')
      .collapsible(true),

    // Retry settings
    retry: object({
      enabled: boolean().description('Enable retry on failure').default(true),
      maxRetries: number().description('Maximum retry attempts').default(3),
      retryDelay: number().description('Delay between retries in ms').default(1000),
    })
      .description('Retry settings')
      .collapsible(true),
  }),

  auth: ({apiKey, basic, oauth}) => ({
    // Different auth methods for different database types
    apiKey: apiKey({
      name: 'X-API-Key',
      place: 'header',
    }),

    basic: basic(),

    oauth: oauth({
      provider: 'generic',
      scopes: ['read', 'write']
    })
  })
});
```