# ezWorkflow endpoints

This is an example of the endpoints that ezWorkflow will register in the project.

```typescript


app.get('/health', (c) => {
    return c.json({
        status: 'ok',
    });
});

app.get('/nodes', (c) => {
    return c.json({
        nodes: {
            'node-id': {
                name: 'Node Name',
                description: 'Node Description',
                categories: ['category1', 'category2'],
                tags: ['tag1', 'tag2'],
            },
        },
    });
});

app.get('/nodes/:id', (c) => {
    return c.json({
        inputs: [],
        outputs: [],
        config: {},
        auth: {},
        version: '0.0.1',
        categories: ['category1', 'category2'],
        tags: ['tag1', 'tag2'],
    })
});

app.post('/nodes/:id/execute', (c) => {
    return c.json({
        status: 'ok',
        output: {},
        error: null,
        logs: [],
        delay: null, // Let's say we'll have to make a long data processing so instead of keep the request pending we should send a response immediately and continue the process in the background, the workflow engine will send a status request to check the status of the node after the delay and get the actual response or again delay.
        reExecute: false, // If we need to re-execute the node then we'll return true
        tryAgain: false, // If we need to try again the node then we'll return true
        retryDelay: null, // If we need to retry the node then we'll return the retry delay in milliseconds
        continueBranch: null, // If this node has multiple branch as outout we have to mention which branch to continue after this response. (It should be any of the output name)
    });
});
```
## Example response for the node execution
 
### Delay response

```json
{
     "status": "ok",
     "output": {},
     "error": null,
     "logs": [],
     "delay": 1000,
     "reExecute": false,
     "tryAgain": false,
     "retryDelay": null,
     "continueBranch": null
 }
```

### Success response with 2 branch 

```json
{
     "status": "ok",
     "output": {},
     "error": null,
     "logs": [],
     "delay": null,
     "reExecute": false,
     "tryAgain": false,
     "retryDelay": null,
     "continueBranch": "branch1"
 }
```

### Success response with 1 branch

```json
{
     "status": "ok",
     "output": {},
     "error": null,
     "logs": [],
     "delay": null,
     "reExecute": false,
     "tryAgain": false,
     "retryDelay": null,
     "continueBranch": null
 }
```

### Error response

```json
{
     "status": "error",
     "output": {},
     "error": "Error message",
     "logs": [],
     "delay": null,
     "reExecute": false,
     "tryAgain": false,
     "retryDelay": null,
     "continueBranch": null
 }
```

