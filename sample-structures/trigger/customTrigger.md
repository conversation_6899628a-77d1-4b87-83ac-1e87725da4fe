```typescript
export default defineTrigger({
    id: 'custom-trigger', // unique id for this trigger (crypto.randomUUID())
    name: 'My Custom Trigger', // name of the trigger
    version: '0.0.1', // version of the trigger
    categories: ['database'], // categories of the trigger
    tags: [ 'database' ] // tags of the trigger
    type: 'http', // or 'socket',
    registarUrl: 'https://mydomain.com', // We'll send a http request to this url when someone add this trigger to the workflow builder

    // When the user send us a request to trigger the workflow we'll expect all this data as body json or in socket data
    expect: ({object, array}) => ({
    // Expected data structure
    messages: array(string()).required() // array(number()) | array(object()) and so on
    contact: object({
      name: string(),
      email: string().required(),
      phone: string(),
    }).required(),
    tags: array(string()) // this is optional as here don't have the required()
  })
})