import path from 'path';
import fs from 'fs';
import {
  execCommandWithOutput,
  readFile,
  writeFile,
  readJsonFile,
  promptForAdditionalInfo,
  promptForRuntimeTemplate,
  getPackageManager,
  logger
} from '../utils';
import inquirer from 'inquirer';
import { setupEzWorkflow } from '../utils/ezw-config';
import { getRuntimeImplementation } from '../runtimes';
import { createBanner, createBox, text, colors, icons, createDivider } from '../utils/theme';
import chalk from 'chalk';

// Create a modern welcome banner
const createWelcomeBanner = () => {
  return createBox(
    `${text.header('WELCOME TO EZWORKFLOW!')}

    ${text.sectionTitle('The next generation workflow automation platform')}`,
    {
      padding: 1,
      margin: 1,
      borderStyle: 'round',
      borderColor: colors.primary.main,
      backgroundColor: colors.neutral.black
    }
  );
};

/**
 * Create a new project
 * @param name Project name
 * @param options Command options
 */
/**
 * Validates the package manager and checks compatibility with the selected runtime
 * @param packageManager The selected package manager
 * @param runtime The selected runtime
 * @returns The validated package manager
 */
const validatePackageManager = async (packageManager: string, runtime: string): Promise<string> => {
  // Validate package manager choice
  const validPackageManagers = ['npm', 'bun', 'deno', 'pnpm', 'yarn'];
  if (!validPackageManagers.includes(packageManager)) {
    logger.error(`Invalid package manager: ${packageManager}`);
    logger.info('Valid package managers are: npm, bun, deno, pnpm, yarn');
    process.exit(1);
  }

  // Warn if package manager doesn't align with runtime
  if (packageManager === 'deno' && runtime !== 'deno') {
    logger.warn(`You selected 'deno' as the package manager, but the runtime is '${runtime}'.`);
    logger.info('This might cause compatibility issues. Consider using npm, yarn, pnpm, or bun instead.');
    // Ask for confirmation
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Do you want to continue with this package manager?',
        default: false,
      },
    ]);
    if (!confirm) {
      logger.info('Please restart the command with a different package manager.');
      process.exit(0);
    }
  } else if (packageManager !== 'deno' && runtime === 'deno') {
    logger.warn(`You selected '${packageManager}' as the package manager, but the runtime is 'deno'.`);
    logger.info('Deno projects typically use deno commands for dependency management.');
    // Ask for confirmation
    const { confirm } = await inquirer.prompt([
      {
        type: 'confirm',
        name: 'confirm',
        message: 'Do you want to continue with this package manager?',
        default: false,
      },
    ]);
    if (!confirm) {
      logger.info('Please restart the command with a different package manager.');
      process.exit(0);
    }
  }

  return packageManager;
};

/**
 * Installs runtime-specific CLI tools
 * @param runtime The selected runtime
 * @param installCommand The base install command (npm, yarn, etc.)
 * @param installArgs The base install arguments
 * @param projectDir The project directory
 */
const installRuntimeCLI = async (
  runtime: string,
  installCommand: string,
  installArgs: string[],
  projectDir: string
): Promise<void> => {
  const cliTools = {
    'netlify': { name: 'Netlify CLI', package: 'netlify-cli' },
    'cloudflare-workers': { name: 'Wrangler CLI', package: 'wrangler' },
    'vercel': { name: 'Vercel CLI', package: 'vercel' }
  };

  const tool = cliTools[runtime as keyof typeof cliTools];
  if (tool) {
    try {
      await execCommandWithOutput(installCommand, [...installArgs, tool.package, '--save-dev'], { cwd: projectDir });
      logger.spinnerSuccess(`${tool.name} installed successfully!`);
    } catch (error) {
      logger.warn(`Could not install ${tool.name}: ${error instanceof Error ? error.message : String(error)}`);
      logger.info(`You may need to install ${tool.name} manually using "${installCommand} ${installArgs.join(' ')} ${tool.package} --save-dev".`);
    }
  } else {
    // No runtime-specific CLI tools needed for this runtime
    logger.spinnerSuccess('No runtime-specific CLI tools needed.');
  }
};

/**
 * Retrieves project metadata from configuration files
 * @param projectDir The project directory
 * @param runtime The selected runtime
 * @returns Object containing project name and description
 */
const getProjectMetadata = async (
  projectDir: string,
  runtime: string
): Promise<{ projectName: string; projectDescription: string }> => {
  const defaultName = 'ezWorkflow Project';
  const defaultDescription = 'A project for creating workflow nodes';

  try {
    if (runtime === 'deno') {
      // For Deno projects, use the name from deno.json
      try {
        const denoJsonPath = path.join(projectDir, 'deno.json');
        const denoJson = await readJsonFile(denoJsonPath);
        return {
          projectName: denoJson.name || defaultName,
          projectDescription: denoJson.description || defaultDescription
        };
      } catch (error) {
        // Use default values if deno.json can't be read
      }
    } else {
      // For non-Deno projects, use the name from package.json
      try {
        const packageJsonPath = path.join(projectDir, 'package.json');
        const packageJson = await readJsonFile(packageJsonPath);
        return {
          projectName: packageJson.name || defaultName,
          projectDescription: packageJson.description || defaultDescription
        };
      } catch (error) {
        // Use default values if package.json can't be read
      }
    }
  } catch (error) {
    // Fallback to defaults in case of any errors
  }

  return { projectName: defaultName, projectDescription: defaultDescription };
};

export const createProject = async (
  name?: string,
  _options: {
    template?: string;
    directory?: string;
    pm?: string;
  } = {}
): Promise<void> => {
  try {
    // Display welcome banner
    console.log(createWelcomeBanner());
    logger.info('🚀 Starting project creation...\n');

    // =============================================
    // PHASE 1: COLLECT ALL USER INPUTS UP FRONT
    // =============================================

    // Step 1: Prompt for Hono.js runtime
    const runtime = await promptForRuntimeTemplate();

    // Step 2: Validate package manager if specified
    let packageManager = _options.pm;
    if (packageManager) {
      packageManager = await validatePackageManager(packageManager, runtime);
    } else {
      packageManager = await getPackageManager();
    }

    // Step 3: Prompt for additional project information
    logger.info('⚙️  Collecting project information...\n');
    const additionalInfo = await promptForAdditionalInfo();

    // Store project metadata for later use
    const projectMetadata = {
      author: {
        name: additionalInfo.authorName,
        email: additionalInfo.authorEmail
        // Optional fields completely removed
      },
      categories: additionalInfo.categories,
      tags: additionalInfo.tags.split(',').map(tag => tag.trim()),
      appType: additionalInfo.appType
    };

    // Determine project directory
    const projectDir = name || '.'; // If no name was provided, assume current directory

    // =============================================
    // PHASE 2: PROJECT CREATION AND SETUP
    // =============================================

    logger.info('\n🚀 Starting project creation with all collected information...\n');

    // Step 1: Create the base project with Hono.js
    logger.spin('Creating project structure...');

    // Build the create-hono command
    const createHonoCommand = 'npx';
    const createHonoArgs = ['create-hono@latest'];
    createHonoArgs.push(name || 'ezworkflow-app');
    createHonoArgs.push('--template', runtime);
    createHonoArgs.push('--pm', packageManager);
    createHonoArgs.push('-i');

    // Execute create-hono command with hidden output
    try {
      // Execute the command with stdio set to ignore to prevent hanging on interactive prompts
      await execCommandWithOutput(createHonoCommand, createHonoArgs, { stdio: 'ignore' });
      logger.spinnerSuccess('Project structure created successfully!');
    } catch (error) {
      // If there's an error, log it but try to continue with the process
      logger.warn(`Project creation encountered an issue: ${error instanceof Error ? error.message : String(error)}`);
      logger.spinnerSuccess('Attempting to proceed with project setup...');
    }

    // Verify that the project directory exists before proceeding
    if (!fs.existsSync(projectDir)) {
      logger.error(`Project directory '${projectDir}' does not exist. Project creation may have failed.`);
      logger.info('Please check for any errors and try again.');
      process.exit(1);
    }

    // Step 2: Install runtime-specific dependencies

    if (runtime === 'deno') {
      // For Deno projects, use deno cache to pre-cache dependencies
      logger.spin('📦 Caching Deno dependencies...');
      try {
        await execCommandWithOutput('deno', ['cache', 'main.ts'], { cwd: projectDir });
        logger.spinnerSuccess('Deno dependencies cached successfully!');
      } catch (error) {
        logger.error(`Failed to cache Deno dependencies: ${error instanceof Error ? error.message : String(error)}`);
        logger.info('You may need to cache dependencies manually using "deno cache main.ts".');
      }
    } else {
      // For non-Deno projects, install only runtime-specific CLI tools
      // Skip general dependency installation since create-hono already did that with the -i flag

      // Build the package manager command for CLI tool installation
      let installCommand: string;
      let installArgs: string[] = [];

      switch (packageManager) {
        case 'npm':
          installCommand = 'npm';
          installArgs = ['install'];
          break;
        case 'yarn':
          installCommand = 'yarn';
          break;
        case 'pnpm':
          installCommand = 'pnpm';
          installArgs = ['install'];
          break;
        case 'bun':
          installCommand = 'bun';
          installArgs = ['install'];
          break;
        default:
          installCommand = 'npm';
          installArgs = ['install'];
      }

      // Install runtime-specific CLI tools if needed
      if (packageManager !== 'deno') {
        logger.spin('📦 Installing runtime-specific CLI tools...');
        await installRuntimeCLI(runtime, installCommand, installArgs, projectDir);
      }
    }
    // Step 3: Set up ezWorkflow in the project
    logger.spin('🔧 Setting up ezWorkflow in the project...');

    // Get the runtime implementation
    const runtimeImpl = getRuntimeImplementation(runtime);

    // Setup runtime-specific configuration
    await runtimeImpl.setupRuntime(projectDir, projectMetadata);

    try {
      // Pass the API key and metadata to setupEzWorkflow
      const success = await setupEzWorkflow(
        projectDir,
        runtime,
        additionalInfo.apiKey,
        {
          author: projectMetadata.author,
          categories: projectMetadata.categories,
          tags: projectMetadata.tags,
          appType: projectMetadata.appType
        }
      );

      if (!success) {
        logger.warn('Could not automatically configure ezWorkflow. You will need to manually add it to your app.');
      } else {
        logger.spinnerSuccess('ezWorkflow setup completed successfully!');

        // If API key was provided, show a success message
        if (additionalInfo.apiKey) {
          logger.success('API key has been stored in ezw.json');
        }
      }
    } catch (error) {
      if (error instanceof Error && error.message.includes('No index file found')) {
        logger.error(`❌ ${error.message}`);
        logger.info('Please make sure you are in a valid project directory.');
        process.exit(1);
      } else {
        logger.error(`❌ Error: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    // Step 4: Update README.md with ezWorkflow specific information
    logger.spin('📚 Updating documentation...');

    const readmePath = path.join(projectDir, 'README.md');
    let readmeContent = '';

    // Get project name and description
    const { projectName, projectDescription } = await getProjectMetadata(projectDir, runtime);

    try {
      readmeContent = await readFile(readmePath);
    } catch (error) {
      // If README doesn't exist, create a basic one
      readmeContent = `# ${projectName}\n\n${projectDescription}\n`;
    }

    // Get runtime-specific README content from the runtime implementation
    const ezwReadmeContent = runtimeImpl.getReadmeContent();

    // Append ezWorkflow content to README
    await writeFile(readmePath, readmeContent + ezwReadmeContent);

    logger.spinnerSuccess('Documentation updated successfully!');

    // Create a modern success message
    const displayName = name || 'current directory';
    const appType = projectMetadata.appType || 'partner';

    // Create a colorful header
    const header = text.header(`✨ ezWorkflow ${appType.toUpperCase()} APP CREATED SUCCESSFULLY! ✨`);

    // Create a welcome message
    const welcomeMsg = text.sectionTitle(`🎉 Welcome to the next era of automation with ezWorkflow!`);

    // Create next steps with colorful bullets
    const nextSteps = [
      `${chalk.hex(colors.status.warning)('1.')} ${text.body('Login to your ezWorkflow account', true)}`,
      `${chalk.hex(colors.status.warning)('2.')} ${text.body('Create a new app at')} ${text.url('https://marketplace.ezworkflow.ai/my-app')}`,
      `${chalk.hex(colors.status.warning)('3.')} ${text.body('Get your app ID and configure it in your project')}`
    ].join('\n');

    // Create helpful tips
    const helpfulTips = [
      `${chalk.hex(colors.status.success)('🛠️')}  ${text.body('Create nodes:', true)} ${text.command('ezw create node <node-name>')}`,
      `${chalk.hex(colors.status.success)('🔌')}  ${text.body('Create triggers:', true)} ${text.command('ezw create trigger <trigger-name>')}`,
      `${chalk.hex(colors.status.success)('🎨')}  ${text.body('Create UI components:', true)} ${text.command('ezw create ui <ui-name>')}`
    ].join('\n');

    // Create a footer
    const footer = chalk.hex(colors.primary.dark)(`💻 Happy coding! Your project in ${chalk.bold(displayName)} is ready to go!`);

    // Combine all sections
    const message = [
      header,
      '',
      welcomeMsg,
      '',
      chalk.hex(colors.neutral.darkGray).bold('📋 NEXT STEPS:'),
      nextSteps,
      '',
      chalk.hex(colors.neutral.darkGray).bold('🚀 QUICK COMMANDS:'),
      helpfulTips,
      '',
      chalk.hex(colors.neutral.darkGray)('📖 Check the README.md file for more information'),
      '',
      footer
    ].join('\n');

    // Create a fancy box with the message
    const boxOptions = {
      padding: 1,
      margin: 1,
      borderStyle: 'round' as const,
      borderColor: colors.primary.main,
      backgroundColor: colors.neutral.black
    };

    // Display the fancy box
    console.log(createBox(message, boxOptions));
  } catch (error) {
    logger.error(`❌ Error: ${error instanceof Error ? error.message : String(error)}`);
    process.exit(1);
  }
};

