/**
 * @file Create Trigger Command
 *
 * This file implements the 'create trigger' command for the ezWorkflow CLI.
 * It handles the creation of new trigger files, including prompting for metadata,
 * generating unique IDs, and updating index files to register the new trigger.
 */

import path from 'path';
import {
  logger,
  ensureDirectory,
  readFile,
  writeFile,
  validateName
} from '../utils';
import { getEzwConfig } from '../utils/ezw-config';
import { v4 as uuidv4 } from 'uuid';
import inquirer from 'inquirer';

/**
 * Creates a new trigger file and registers it in the project.
 *
 * This function handles the entire process of creating a new trigger:
 * 1. Validates the trigger name
 * 2. Checks if the trigger already exists
 * 3. Prompts for additional metadata (category, tags, version)
 * 4. Generates a unique ID for the trigger
 * 5. Creates the trigger file with the appropriate content
 * 6. Updates the index file to register the new trigger
 *
 * @param name - Name of the trigger (will be normalized for file paths)
 * @param _options - Command options
 * @param _options.category - Optional predefined category for the trigger
 * @param _options.tags - Optional comma-separated list of tags
 * @returns Promise that resolves when the trigger has been created
 */
export const createTrigger = async (
  name?: string,
  _options: { category?: string; tags?: string } = {}
): Promise<void> => {
  try {
    // Name validation is now handled in the CLI command handler
    if (!name) {
      logger.error('Trigger name is required');
      process.exit(1);
    }

    // Validate trigger name and get normalized version
    const validationResult = validateName(name);
    if (validationResult === false) {
      process.exit(1);
    }

    // Use the normalized name (lowercase) for file paths
    const normalizedName = validationResult as string;

    // Keep original name for display purposes
    const displayName = name;

    // Get the current working directory
    const cwd = process.cwd();

    // Get the ezw configuration to determine the correct paths
    logger.spin('🔍 Reading ezWorkflow configuration...');
    const config = await getEzwConfig(cwd);
    logger.spinnerSuccess('Found ezWorkflow configuration');

    // Get the path for triggers
    const triggersDir = path.join(cwd, config.triggersDirPath);

    // Ensure the directory exists
    await ensureDirectory(triggersDir);

    // Check if the trigger already exists (using normalized name for files)
    const triggerFilePath = path.join(triggersDir, `${normalizedName}.ts`);

    try {
      await readFile(triggerFilePath);
      logger.error(`Trigger ${normalizedName} already exists at ${triggerFilePath}`);
      process.exit(1);
    } catch (error) {
      // File doesn't exist, which is what we want
    }

    // Generate a unique 32-character ID
    const triggerId = uuidv4();

    // Prompt for additional metadata
    logger.info('📝 Please provide additional information about your trigger:');

    // Predefined categories for organizing triggers
    // These categories help users classify their triggers for better organization and discovery
    const predefinedCategories = [
      'Analytics',
      'API Integration',
      'Authentication',
      'Calendar',
      'Cloud Storage',
      'CRM',
      'Custom',
      'Database',
      'E-commerce',
      'Email',
      'File System',
      'Form Submission',
      'IoT',
      'Messaging',
      'Mobile',
      'Monitoring',
      'Other',
      'Payment',
      'Scheduled',
      'Social Media',
      'User Activity',
      'Voice/Speech',
      'Webhooks'
    ];

    // Use provided options or prompt for them
    let category = _options.category;
    let tags = _options.tags;
    let version = '0.0.1'; // Default version

    if (!category || !tags) {
      const questions = [
        {
          type: 'list' as const,
          name: 'category',
          message: 'Select a category for your trigger:',
          choices: [
            ...predefinedCategories,
            new inquirer.Separator(),
            {
              name: 'Custom category',
              value: 'custom'
            }
          ],
          default: 'Webhooks',
          when: !category
        },
        {
          type: 'input' as const,
          name: 'customCategory',
          message: 'Enter your custom category:',
          when: (answers: any) => !category && answers.category === 'custom',
          validate: (input: any) => input.trim() !== '' ? true : 'Category cannot be empty'
        },
        {
          type: 'input' as const,
          name: 'tags',
          message: 'Enter tags (comma-separated):',
          when: !tags,
          default: 'webhook',
          validate: (input: any) => {
            const trimmedTags = input.split(',').map((tag: string) => tag.trim()).filter((tag: string) => tag !== '');
            return trimmedTags.length > 0 ? true : 'At least one tag is required';
          }
        },
        {
          type: 'input' as const,
          name: 'version',
          message: 'Enter version number:',
          default: '0.0.1',
          validate: (input: any) => {
            return /^\d+\.\d+\.\d+$/.test(input) ? true : 'Version must be in format x.y.z (e.g., 0.0.1)';
          }
        }
      ];

      const answers = await inquirer.prompt(questions as any);

      // Set values from answers
      if (!category) {
        category = answers.category === 'custom' ? answers.customCategory : answers.category;
      }
      if (!tags) {
        tags = answers.tags;
      }
      version = answers.version;
    }

    // Process tags
    const tagsList = tags ? tags.split(',').map((tag: string) => tag.trim()).filter((tag: string) => tag !== '') : ['webhook'];

    // Create the trigger file with enhanced content
    const triggerFileContent = `import { defineTrigger } from '@ezworkflow/node-sdk';

/**
 * ${displayName} trigger
 */
export default defineTrigger({
  id: 'trigger-${triggerId}',
  name: '${displayName}',
  description: 'What does this trigger do?',
  version: '${version}',
  category: '${category || 'Webhooks'}',
  tags: [${tagsList.map(tag => `'${tag}'`).join(', ')}],
  // Other properties will be added later
});
`;

    // Write the file
    await writeFile(triggerFilePath, triggerFileContent);

    logger.success(`Created trigger file at ${triggerFilePath}`);

    // Update the index file
    await updateTriggerIndexFile(triggersDir, normalizedName, triggerId);

    logger.success(`Trigger ${displayName} created successfully!`);
  } catch (error) {
    logger.error(`Failed to create trigger: ${error instanceof Error ? error.message : String(error)}`);
    process.exit(1);
  }
};

/**
 * Updates the triggers/index.ts file to include the new trigger.
 *
 * This function handles the process of updating the index file:
 * 1. Reads the existing index file or creates a new one if it doesn't exist
 * 2. Adds an import statement for the new trigger
 * 3. Updates the default export to include the new trigger
 *
 * The function is careful to maintain the structure of the existing file
 * and handles various edge cases like empty files or missing exports.
 *
 * @param triggersDir - Path to the triggers directory
 * @param triggerName - Name of the trigger (normalized)
 * @param triggerId - Unique ID of the trigger (used as the key in the export object)
 * @returns Promise that resolves when the index file has been updated
 */
async function updateTriggerIndexFile(triggersDir: string, triggerName: string, triggerId: string): Promise<void> {
  const indexFilePath = path.join(triggersDir, 'index.ts');
  let indexContent = '';

  try {
    indexContent = await readFile(indexFilePath);
  } catch (error) {
    // If the file doesn't exist, create a default one
    indexContent = `// This file is auto-generated and will be updated when new triggers are created
// Export all triggers from this directory

export default {};
`;
  }

  // Check if the trigger is already imported
  if (indexContent.includes(`import ${triggerName}Trigger from './${triggerName}'`)) {
    logger.warn(`Trigger ${triggerName} is already imported in the index file`);
    return;
  }

  // Add the import statement
  const importStatement = `// Import trigger definition
import ${triggerName}Trigger from './${triggerName}';\n`;

  // Find the position to insert the import
  const lastImportIndex = indexContent.lastIndexOf('import ');
  let newContent = '';

  if (lastImportIndex >= 0) {
    // Find the end of the last import statement
    const lastImportEndIndex = indexContent.indexOf('\n', lastImportIndex) + 1;
    newContent = indexContent.slice(0, lastImportEndIndex) + importStatement + indexContent.slice(lastImportEndIndex);
  } else {
    // No imports yet, add at the beginning after the comment
    const commentEndIndex = indexContent.indexOf('\n\n') + 2;
    newContent = indexContent.slice(0, commentEndIndex) + importStatement + indexContent.slice(commentEndIndex);
  }

  // Update the export statement
  if (newContent.includes('export default {};')) {
    newContent = newContent.replace('export default {};', `export default {
  '${triggerId}': ${triggerName}Trigger,
};`);
  } else {
    // Find the export default statement
    const exportStartIndex = newContent.indexOf('export default {');
    const exportEndIndex = newContent.indexOf('};', exportStartIndex) + 2;

    // Extract the current export object
    const exportObject = newContent.slice(exportStartIndex + 15, exportEndIndex - 2);

    // Add the new trigger to the export object
    const newExportObject = exportObject.trim().endsWith(',')
      ? `${exportObject}\n  '${triggerId}': ${triggerName}Trigger,\n`
      : `${exportObject}${exportObject.trim() ? ',\n' : ''}  '${triggerId}': ${triggerName}Trigger,\n`;

    // Replace the export object
    newContent = newContent.slice(0, exportStartIndex + 15) + newExportObject + newContent.slice(exportEndIndex - 2);
  }

  // Write the updated content
  await writeFile(indexFilePath, newContent);

  logger.success(`Updated triggers index file at ${indexFilePath}`);
}
