/**
 * @file Email Builder Block Handler
 *
 * This file implements the handler for creating email builder block components.
 * It handles the creation of new email builder block files, including prompting for metadata,
 * generating unique IDs, and updating index files to register the new block.
 */

import { logger } from "../../utils";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, UiComponentOptions } from "./index";
import inquirer from "inquirer";
import { v4 as uuidv4 } from "uuid";
import path from "path";
import { ensureDirectory, readFile, writeFile } from "../../utils";
import { getEzwConfig } from "../../utils/ezw-config";
import { validateName, toCamelCase } from "../../utils/validation";

/**
 * <PERSON><PERSON> for creating email builder block components
 */
export class EmailBuilderBlockHandler implements UiHandler {
  /**
   * Handles the creation of an email builder block component
   * @param name - Optional name of the block
   * @param options - UI component options
   */
  async handle(name: string | undefined, options: UiComponentOptions): Promise<void> {
    try {
      // If no name is provided, prompt for it
      if (!name) {
        const { blockName } = await inquirer.prompt([
          {
            type: "input",
            name: "blockName",
            message: "Enter block name:",
            validate: (input) => {
              const result = validateName(input);
              return result === true ? true : result;
            },
          },
        ]);
        name = blockName;
      }

      // Validate block name
      const validationResult = validateName(name as string);
      if (validationResult === false) {
        process.exit(1);
      }

      // Use the normalized name (lowercase) for file paths
      const normalizedName = validationResult as string;

      // Get the current working directory
      const cwd = process.cwd();

      // Get the ezw configuration to determine the correct paths
      logger.spin("🔍 Reading ezWorkflow configuration...");
      const config = await getEzwConfig(cwd);
      logger.spinnerSuccess("Found ezWorkflow configuration");

      if (!config.uiDirPath) {
        logger.error("UI directory path not found in configuration");
        process.exit(1);
      }

      // Get the path for UI components
      const uiDir = path.join(cwd, config.uiDirPath);
      const blockDir = path.join(uiDir, "email-builder-block");
      const visibilityDir = path.join(blockDir, options.visibility);

      // Ensure the directories exist
      await ensureDirectory(visibilityDir);

      // Check if the block already exists
      const blockFilePath = path.join(visibilityDir, `${normalizedName}.ts`);

      try {
        await readFile(blockFilePath);
        logger.error(`Block ${normalizedName} already exists at ${blockFilePath}`);
        process.exit(1);
      } catch (error) {
        // File doesn't exist, which is what we want
      }

      // Generate a unique ID
      const blockId = uuidv4();

      // Use default values for metadata
      const categories = ["email", "builder"];
      const tags = ["email", "builder", "block"];
      const description = `${name} email builder block`;
      const blockType = "content";

      // Create the block file
      const blockContent = this.generateBlockContent(
        blockId,
        name as string,
        description,
        categories,
        tags,
        options.visibility,
        blockType
      );

      await writeFile(blockFilePath, blockContent);
      logger.success(`Created block file: ${blockFilePath}`);

      // Update the index files
      await this.updateIndexFiles(uiDir, blockDir, visibilityDir, normalizedName);
      logger.success("Updated index files");

      logger.success(`✅ Successfully created email builder block: ${name}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Failed to create email builder block: ${errorMessage}`);
      process.exit(1);
    }
  }

  /**
   * Generates the content for an email builder block file
   */
  private generateBlockContent(
    id: string,
    name: string,
    description: string,
    categories: string[],
    tags: string[],
    visibility: 'partner' | 'account',
    blockType: string
  ): string {
    return `/**
 * @file ${name} Email Builder Block
 *
 * This file defines an email builder block component for the ezWorkflow platform.
 */

import { defineEmailBuilderBlock } from '@ezworkflow/node-sdk';

export default defineEmailBuilderBlock({
  id: '${id}',
  name: '${name}',
  description: '${description}',
  version: '1.0.0',
  categories: ${JSON.stringify(categories)},
  tags: ${JSON.stringify(tags)},
  visibility: '${visibility}',
  blockType: '${blockType}',
  metadata: {
    editable: true,
    draggable: true,
    resizable: true,
    deletable: true,
  },
});
`;
  }

  /**
   * Updates all necessary index files
   */
  private async updateIndexFiles(
    uiDir: string,
    blockDir: string,
    visibilityDir: string,
    normalizedName: string
  ): Promise<void> {
    // Update the visibility-specific index file
    const visibilityIndexPath = path.join(visibilityDir, "index.ts");
    let visibilityIndexContent = "";

    try {
      visibilityIndexContent = await readFile(visibilityIndexPath);
    } catch (error) {
      // If the file doesn't exist, create it with basic content
      visibilityIndexContent = `/**
 * @file Email Builder Block Components
 *
 * This file exports all email builder block components.
 */

`;
    }

    // Add the export for the new block
    const exportIdentifier = toCamelCase(normalizedName);
    const exportStatement = `export { default as ${exportIdentifier} } from './${normalizedName}';\n`;
    if (!visibilityIndexContent.includes(exportStatement)) {
      visibilityIndexContent += exportStatement;
      await writeFile(visibilityIndexPath, visibilityIndexContent);
    }

    // Update the block type index file
    const blockIndexPath = path.join(blockDir, "index.ts");
    let blockIndexContent = "";

    try {
      blockIndexContent = await readFile(blockIndexPath);
    } catch (error) {
      // If the file doesn't exist, create it with basic content
      blockIndexContent = `/**
 * @file Email Builder Block Components Index
 *
 * This file exports all email builder block components.
 */

// Export partner blocks
export * from './partner';

// Export account blocks
export * from './account';
`;
      await writeFile(blockIndexPath, blockIndexContent);
    }

    // Update the main UI index file
    const uiIndexPath = path.join(uiDir, "index.ts");
    let uiIndexContent = "";

    try {
      uiIndexContent = await readFile(uiIndexPath);
    } catch (error) {
      // If the file doesn't exist, create it with basic content
      uiIndexContent = `/**
 * @file UI Components Index
 *
 * This file exports all UI components for the ezWorkflow platform.
 */

// Export dashboard widgets
export * as dashboardWidgets from './dashboard-widget';

// Export modals
export * as modals from './modal';

// Export drawers
export * as drawers from './drawer';

// Export pages
export * as pages from './page';

// Export funnel builder blocks
export * as funnelBuilderBlocks from './funnel-builder-block';

// Export email builder blocks
export * as emailBuilderBlocks from './email-builder-block';
`;
      await writeFile(uiIndexPath, uiIndexContent);
    }

    // Check if the email builder blocks export exists
    if (!uiIndexContent.includes("export * as emailBuilderBlocks from './email-builder-block';")) {
      // If the email builder blocks export doesn't exist, add it
      uiIndexContent = uiIndexContent.replace(
        "/**\n * @file UI Components Index",
        "/**\n * @file UI Components Index\n *\n * This file exports all UI components for the ezWorkflow platform.\n */\n\n// Export email builder blocks\nexport * as emailBuilderBlocks from './email-builder-block';"
      );
      await writeFile(uiIndexPath, uiIndexContent);
    }
  }
}
