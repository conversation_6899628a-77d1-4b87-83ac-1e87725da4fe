/**
 * @file Page Handler
 *
 * This file implements the handler for creating page components.
 * It handles the creation of new page files, including prompting for metadata,
 * generating unique IDs, and updating index files to register the new page.
 */

import { logger } from "../../utils";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, UiComponentOptions } from "./index";
import inquirer from "inquirer";
import { v4 as uuidv4 } from "uuid";
import path from "path";
import { ensureDirectory, readFile, writeFile } from "../../utils";
import { getEzwConfig } from "../../utils/ezw-config";
import { validateName, toCamelCase } from "../../utils/validation";

/**
 * <PERSON><PERSON> for creating page components
 */
export class PageHandler implements UiHandler {
  /**
   * Handles the creation of a page component
   * @param name - Optional name of the page
   * @param options - UI component options
   */
  async handle(name: string | undefined, options: UiComponentOptions): Promise<void> {
    try {
      // If no name is provided, prompt for it
      if (!name) {
        const { pageName } = await inquirer.prompt([
          {
            type: "input",
            name: "pageName",
            message: "Enter page name:",
            validate: (input) => {
              const result = validateName(input);
              return result === true ? true : result;
            },
          },
        ]);
        name = pageName;
      }

      // Validate page name
      const validationResult = validateName(name as string);
      if (validationResult === false) {
        process.exit(1);
      }

      // Use the normalized name (lowercase) for file paths
      const normalizedName = validationResult as string;

      // Get the current working directory
      const cwd = process.cwd();

      // Get the ezw configuration to determine the correct paths
      logger.spin("🔍 Reading ezWorkflow configuration...");
      const config = await getEzwConfig(cwd);
      logger.spinnerSuccess("Found ezWorkflow configuration");

      if (!config.uiDirPath) {
        logger.error("UI directory path not found in configuration");
        process.exit(1);
      }

      // Get the path for UI components
      const uiDir = path.join(cwd, config.uiDirPath);
      const pageDir = path.join(uiDir, "page");
      const visibilityDir = path.join(pageDir, options.visibility);

      // Ensure the directories exist
      await ensureDirectory(visibilityDir);

      // Check if the page already exists
      const pageFilePath = path.join(visibilityDir, `${normalizedName}.ts`);

      try {
        await readFile(pageFilePath);
        logger.error(`Page ${normalizedName} already exists at ${pageFilePath}`);
        process.exit(1);
      } catch (error) {
        // File doesn't exist, which is what we want
      }

      // Generate a unique ID
      const pageId = uuidv4();

      // Use default values for metadata
      const categories = ["dashboard"];
      const tags = ["page", "dashboard"];
      const description = `${name} page`;
      const layout = "default";

      // Create the page file
      const pageContent = this.generatePageContent(
        pageId,
        name as string,
        description,
        categories,
        tags,
        options.visibility,
        layout
      );

      await writeFile(pageFilePath, pageContent);
      logger.success(`Created page file: ${pageFilePath}`);

      // Update the index files
      await this.updateIndexFiles(uiDir, pageDir, visibilityDir, normalizedName);
      logger.success("Updated index files");

      logger.success(`✅ Successfully created page: ${name}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Failed to create page: ${errorMessage}`);
      process.exit(1);
    }
  }

  /**
   * Generates the content for a page file
   */
  private generatePageContent(
    id: string,
    name: string,
    description: string,
    categories: string[],
    tags: string[],
    visibility: 'partner' | 'account',
    layout: string
  ): string {
    return `/**
 * @file ${name} Page
 *
 * This file defines a page component for the ezWorkflow platform.
 */

import { definePage } from '@ezworkflow/node-sdk';

export default definePage({
  id: '${id}',
  name: '${name}',
  description: '${description}',
  version: '1.0.0',
  categories: ${JSON.stringify(categories)},
  tags: ${JSON.stringify(tags)},
  visibility: '${visibility}',
  metadata: {
    layout: '${layout}',
    requiresAuth: true,
    showInNavigation: true,
    navigationGroup: 'main',
    navigationOrder: 0,
  },
});
`;
  }

  /**
   * Updates all necessary index files
   */
  private async updateIndexFiles(
    uiDir: string,
    pageDir: string,
    visibilityDir: string,
    normalizedName: string
  ): Promise<void> {
    // Update the visibility-specific index file
    const visibilityIndexPath = path.join(visibilityDir, "index.ts");
    let visibilityIndexContent = "";

    try {
      visibilityIndexContent = await readFile(visibilityIndexPath);
    } catch (error) {
      // If the file doesn't exist, create it with basic content
      visibilityIndexContent = `/**
 * @file Page Components
 *
 * This file exports all page components.
 */

`;
    }

    // Add the export for the new page
    const exportIdentifier = toCamelCase(normalizedName);
    const exportStatement = `export { default as ${exportIdentifier} } from './${normalizedName}';\n`;
    if (!visibilityIndexContent.includes(exportStatement)) {
      visibilityIndexContent += exportStatement;
      await writeFile(visibilityIndexPath, visibilityIndexContent);
    }

    // Update the page type index file
    const pageIndexPath = path.join(pageDir, "index.ts");
    let pageIndexContent = "";

    try {
      pageIndexContent = await readFile(pageIndexPath);
    } catch (error) {
      // If the file doesn't exist, create it with basic content
      pageIndexContent = `/**
 * @file Page Components Index
 *
 * This file exports all page components.
 */

// Export partner pages
export * from './partner';

// Export account pages
export * from './account';
`;
      await writeFile(pageIndexPath, pageIndexContent);
    }

    // Update the main UI index file
    const uiIndexPath = path.join(uiDir, "index.ts");
    let uiIndexContent = "";

    try {
      uiIndexContent = await readFile(uiIndexPath);
    } catch (error) {
      // If the file doesn't exist, create it with basic content
      uiIndexContent = `/**
 * @file UI Components Index
 *
 * This file exports all UI components for the ezWorkflow platform.
 */

// Export dashboard widgets
export * as dashboardWidgets from './dashboard-widget';

// Export modals
export * as modals from './modal';

// Export drawers
export * as drawers from './drawer';

// Export pages
export * as pages from './page';

// Export funnel builder blocks
export * as funnelBuilderBlocks from './funnel-builder-block';

// Export email builder blocks
export * as emailBuilderBlocks from './email-builder-block';
`;
      await writeFile(uiIndexPath, uiIndexContent);
    }

    // Check if the pages export exists
    if (!uiIndexContent.includes("export * as pages from './page';")) {
      // If the pages export doesn't exist, add it
      uiIndexContent = uiIndexContent.replace(
        "/**\n * @file UI Components Index",
        "/**\n * @file UI Components Index\n *\n * This file exports all UI components for the ezWorkflow platform.\n */\n\n// Export pages\nexport * as pages from './page';"
      );
      await writeFile(uiIndexPath, uiIndexContent);
    }
  }
}
