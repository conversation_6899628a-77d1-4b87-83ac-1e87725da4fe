/**
 * @file UI Handlers Index
 *
 * This file exports the UI type handlers and related types for the create-ui command.
 * Each UI type has its own handler that implements the specific logic for creating
 * that type of UI component.
 */

import inquirer from 'inquirer';
import { logger } from '../../utils';
import { DashboardWidgetHandler } from "./dashboard-widget";
import { <PERSON><PERSON><PERSON>and<PERSON> } from "./modal";
import { <PERSON><PERSON><PERSON>andler } from "./drawer";
import { <PERSON>Handler } from "./page";
import { FunnelBuilderBlockHandler } from "./funnel-builder-block";
import { EmailBuilderBlockHandler } from "./email-builder-block";

/**
 * Enum representing the available UI component types
 */
export enum UiType {
  DASHBOARD_WIDGET = "dashboard-widget",
  MODAL = "modal",
  DRAWER = "drawer",
  PAGE = "page",
  FUNNEL_BUILDER_BLOCK = "funnel-builder-block",
  EMAIL_BUILDER_BLOCK = "email-builder-block",
}

/**
 * Interface for UI component creation options
 */
export interface UiComponentOptions {
  /** Visibility type (partner/account) */
  visibility: 'partner' | 'account';
}

/**
 * Interface that all UI handlers must implement
 */
export interface UiHandler {
  /** Handles the creation of a UI component */
  handle(name: string | undefined, options: UiComponentOptions): Promise<void>;
}

/**
 * Map of UI types to their handlers
 */
export const UI_TYPE_HANDLERS: Record<UiType, UiHandler> = {
  [UiType.DASHBOARD_WIDGET]: new DashboardWidgetHandler(),
  [UiType.MODAL]: new ModalHandler(),
  [UiType.DRAWER]: new DrawerHandler(),
  [UiType.PAGE]: new PageHandler(),
  [UiType.FUNNEL_BUILDER_BLOCK]: new FunnelBuilderBlockHandler(),
  [UiType.EMAIL_BUILDER_BLOCK]: new EmailBuilderBlockHandler(),
};

/**
 * Prompts the user to select a UI type
 * @returns Promise that resolves to the selected UI type
 */
export const promptForUiType = async (): Promise<UiType> => {
  const { uiType } = await inquirer.prompt([
    {
      type: "list",
      name: "uiType",
      message: "Select UI component type:",
      choices: [
        { name: "Dashboard Widget", value: UiType.DASHBOARD_WIDGET },
        { name: "Modal Dialog", value: UiType.MODAL },
        { name: "Drawer", value: UiType.DRAWER },
        { name: "Page", value: UiType.PAGE },
        { name: "Funnel Builder Block", value: UiType.FUNNEL_BUILDER_BLOCK },
        { name: "Email Builder Block", value: UiType.EMAIL_BUILDER_BLOCK },
      ],
    },
  ]);

  return uiType;
};

/**
 * Prompt for UI component name
 *
 * @returns Promise that resolves to the UI component name
 */
export async function promptForUiName(): Promise<string> {
  const { name } = await inquirer.prompt([
    {
      type: 'input',
      name: 'name',
      message: 'Enter a name for your UI component:',
      validate: (input) => {
        if (!input.trim()) {
          return 'Name cannot be empty';
        }
        return true;
      }
    }
  ]);

  return name;
}

// No common prompts needed anymore
