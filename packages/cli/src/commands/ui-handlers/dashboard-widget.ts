/**
 * @file Dashboard Widget Handler
 *
 * This file implements the handler for creating dashboard widget components.
 * It handles the creation of new dashboard widget files, including prompting for metadata,
 * generating unique IDs, and updating index files to register the new widget.
 */

import { logger } from "../../utils";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, UiComponentOptions } from "./index";
import inquirer from "inquirer";
import { v4 as uuidv4 } from "uuid";
import path from "path";
import { ensureDirectory, readFile, writeFile } from "../../utils";
import { getEzwConfig } from "../../utils/ezw-config";
import { validateName, toCamelCase } from "../../utils/validation";

/**
 * <PERSON><PERSON> for creating dashboard widget components
 */
export class DashboardWidgetHandler implements UiHandler {
  /**
   * Handles the creation of a dashboard widget component
   * @param name - Optional name of the widget
   * @param options - UI component options
   */
  async handle(name: string | undefined, options: UiComponentOptions): Promise<void> {
    try {
      // If no name is provided, prompt for it
      if (!name) {
        const { widgetName } = await inquirer.prompt([
          {
            type: "input",
            name: "widgetName",
            message: "Enter widget name:",
            validate: (input) => {
              const result = validateName(input);
              return result === true ? true : result;
            },
          },
        ]);
        name = widgetName;
      }

      // Validate widget name
      const validationResult = validateName(name as string);
      if (validationResult === false) {
        process.exit(1);
      }

      // Use the normalized name (lowercase) for file paths
      const normalizedName = validationResult as string;

      // Get the current working directory
      const cwd = process.cwd();

      // Get the ezw configuration to determine the correct paths
      logger.spin("🔍 Reading ezWorkflow configuration...");
      const config = await getEzwConfig(cwd);
      logger.spinnerSuccess("Found ezWorkflow configuration");

      if (!config.uiDirPath) {
        logger.error("UI directory path not found in configuration");
        process.exit(1);
      }

      // Get the path for UI components
      const uiDir = path.join(cwd, config.uiDirPath);
      const widgetDir = path.join(uiDir, "dashboard-widget");
      const visibilityDir = path.join(widgetDir, options.visibility);

      // Ensure the directories exist
      await ensureDirectory(visibilityDir);

      // Check if the widget already exists
      const widgetFilePath = path.join(visibilityDir, `${normalizedName}.ts`);

      try {
        await readFile(widgetFilePath);
        logger.error(`Widget ${normalizedName} already exists at ${widgetFilePath}`);
        process.exit(1);
      } catch (error) {
        // File doesn't exist, which is what we want
      }

      // Generate a unique ID
      const widgetId = uuidv4();

      // Use default values for metadata
      const categories = ["dashboard"];
      const tags = ["widget", "dashboard"];
      const description = `${name} dashboard widget`;

      // Create the widget file
      const widgetContent = this.generateWidgetContent(
        widgetId,
        name as string,
        description,
        categories,
        tags,
        options.visibility
      );

      await writeFile(widgetFilePath, widgetContent);
      logger.success(`Created widget file: ${widgetFilePath}`);

      // Update the index files
      await this.updateIndexFiles(uiDir, widgetDir, visibilityDir, normalizedName);
      logger.success("Updated index files");

      logger.success(`✅ Successfully created dashboard widget: ${name}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Failed to create dashboard widget: ${errorMessage}`);
      process.exit(1);
    }
  }

  /**
   * Generates the content for a dashboard widget file
   */
  private generateWidgetContent(
    id: string,
    name: string,
    description: string,
    categories: string[],
    tags: string[],
    visibility: 'partner' | 'account'
  ): string {
    return `/**
 * @file ${name} Dashboard Widget
 *
 * This file defines a dashboard widget component for the ezWorkflow platform.
 */

import { defineDashboardWidget } from '@ezworkflow/node-sdk';

export default defineDashboardWidget({
  id: '${id}',
  name: '${name}',
  description: '${description}',
  version: '1.0.0',
  categories: ${JSON.stringify(categories)},
  tags: ${JSON.stringify(tags)},
  visibility: '${visibility}',
  widgetSize: 'medium',
  metadata: {
    refreshInterval: 300, // 5 minutes
    showRefreshButton: true,
    showExportButton: true,
    showFilterButton: true,
  },
});
`;
  }

  /**
   * Updates all necessary index files
   */
  private async updateIndexFiles(
    uiDir: string,
    widgetDir: string,
    visibilityDir: string,
    normalizedName: string
  ): Promise<void> {
    // Update the visibility-specific index file
    const visibilityIndexPath = path.join(visibilityDir, "index.ts");
    let visibilityIndexContent = "";

    try {
      visibilityIndexContent = await readFile(visibilityIndexPath);
    } catch (error) {
      // If the file doesn't exist, create it with basic content
      visibilityIndexContent = `/**
 * @file Dashboard Widget Components
 *
 * This file exports all dashboard widget components.
 */

`;
    }

    // Add the export for the new widget
    const exportIdentifier = toCamelCase(normalizedName);
    const exportStatement = `export { default as ${exportIdentifier} } from './${normalizedName}';\n`;
    if (!visibilityIndexContent.includes(exportStatement)) {
      visibilityIndexContent += exportStatement;
      await writeFile(visibilityIndexPath, visibilityIndexContent);
    }

    // Update the widget type index file
    const widgetIndexPath = path.join(widgetDir, "index.ts");
    let widgetIndexContent = "";

    try {
      widgetIndexContent = await readFile(widgetIndexPath);
    } catch (error) {
      // If the file doesn't exist, create it with basic content
      widgetIndexContent = `/**
 * @file Dashboard Widget Components Index
 *
 * This file exports all dashboard widget components for the ezWorkflow platform.
 */

// Export partner widgets
export * from './partner';

// Export account widgets
export * from './account';
`;
      await writeFile(widgetIndexPath, widgetIndexContent);
    }

    // Update the main UI index file
    const uiIndexPath = path.join(uiDir, "index.ts");
    let uiIndexContent = "";

    try {
      uiIndexContent = await readFile(uiIndexPath);
    } catch (error) {
      // If the file doesn't exist, create it with basic content
      uiIndexContent = `/**
 * @file UI Components Index
 *
 * This file exports all UI components for the ezWorkflow platform.
 */

// Export dashboard widgets
export * as dashboardWidgets from './dashboard-widget';

// Export modals
export * as modals from './modal';

// Export drawers
export * as drawers from './drawer';

// Export pages
export * as pages from './page';

// Export funnel builder blocks
export * as funnelBuilderBlocks from './funnel-builder-block';

// Export email builder blocks
export * as emailBuilderBlocks from './email-builder-block';
`;
      await writeFile(uiIndexPath, uiIndexContent);
    }

    // Check if the dashboard widgets export exists
    if (!uiIndexContent.includes("export * as dashboardWidgets from './dashboard-widget';")) {
      // If the dashboard widgets export doesn't exist, add it
      uiIndexContent = uiIndexContent.replace(
        "/**\n * @file UI Components Index",
        "/**\n * @file UI Components Index\n *\n * This file exports all UI components for the ezWorkflow platform.\n */\n\n// Export dashboard widgets\nexport * as dashboardWidgets from './dashboard-widget';"
      );
      await writeFile(uiIndexPath, uiIndexContent);
    }
  }
}
