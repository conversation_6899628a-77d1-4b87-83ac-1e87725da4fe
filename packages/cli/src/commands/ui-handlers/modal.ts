/**
 * @file <PERSON>dal Handler
 *
 * This file implements the handler for creating modal dialog components.
 * It handles the creation of new modal dialog files, including prompting for metadata,
 * generating unique IDs, and updating index files to register the new modal.
 */

import { logger } from "../../utils";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, UiComponentOptions } from "./index";
import inquirer from "inquirer";
import { v4 as uuidv4 } from "uuid";
import path from "path";
import { ensureDirectory, readFile, writeFile } from "../../utils";
import { getEzwConfig } from "../../utils/ezw-config";
import { validateName, toCamelCase } from "../../utils/validation";

/**
 * <PERSON><PERSON> for creating modal dialog components
 */
export class ModalHandler implements UiHandler {
  /**
   * Handles the creation of a modal dialog component
   * @param name - Optional name of the modal
   * @param options - UI component options
   */
  async handle(name: string | undefined, options: UiComponentOptions): Promise<void> {
    try {
      // If no name is provided, prompt for it
      if (!name) {
        const { modalName } = await inquirer.prompt([
          {
            type: "input",
            name: "modalName",
            message: "Enter modal name:",
            validate: (input) => {
              const result = validateName(input);
              return result === true ? true : result;
            },
          },
        ]);
        name = modalName;
      }

      // Validate modal name
      const validationResult = validateName(name as string);
      if (validationResult === false) {
        process.exit(1);
      }

      // Use the normalized name (lowercase) for file paths
      const normalizedName = validationResult as string;

      // Get the current working directory
      const cwd = process.cwd();

      // Get the ezw configuration to determine the correct paths
      logger.spin("🔍 Reading ezWorkflow configuration...");
      const config = await getEzwConfig(cwd);
      logger.spinnerSuccess("Found ezWorkflow configuration");

      if (!config.uiDirPath) {
        logger.error("UI directory path not found in configuration");
        process.exit(1);
      }

      // Get the path for UI components
      const uiDir = path.join(cwd, config.uiDirPath);
      const modalDir = path.join(uiDir, "modal");
      const visibilityDir = path.join(modalDir, options.visibility);

      // Ensure the directories exist
      await ensureDirectory(visibilityDir);

      // Check if the modal already exists
      const modalFilePath = path.join(visibilityDir, `${normalizedName}.ts`);

      try {
        await readFile(modalFilePath);
        logger.error(`Modal ${normalizedName} already exists at ${modalFilePath}`);
        process.exit(1);
      } catch (error) {
        // File doesn't exist, which is what we want
      }

      // Generate a unique ID
      const modalId = uuidv4();

      // Use default values for metadata
      const categories = ["forms"];
      const tags = ["modal", "dialog"];
      const description = `${name} modal dialog`;
      const modalSize = "medium";

      // Create the modal file
      const modalContent = this.generateModalContent(
        modalId,
        name as string,
        description,
        categories,
        tags,
        options.visibility,
        modalSize
      );

      await writeFile(modalFilePath, modalContent);
      logger.success(`Created modal file: ${modalFilePath}`);

      // Update the index files
      await this.updateIndexFiles(uiDir, modalDir, visibilityDir, normalizedName);
      logger.success("Updated index files");

      logger.success(`✅ Successfully created modal: ${name}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Failed to create modal: ${errorMessage}`);
      process.exit(1);
    }
  }

  /**
   * Generates the content for a modal dialog file
   */
  private generateModalContent(
    id: string,
    name: string,
    description: string,
    categories: string[],
    tags: string[],
    visibility: 'partner' | 'account',
    modalSize: string
  ): string {
    return `/**
 * @file ${name} Modal Dialog
 *
 * This file defines a modal dialog component for the ezWorkflow platform.
 */

import { defineModal } from '@ezworkflow/node-sdk';

export default defineModal({
  id: '${id}',
  name: '${name}',
  description: '${description}',
  version: '1.0.0',
  categories: ${JSON.stringify(categories)},
  tags: ${JSON.stringify(tags)},
  visibility: '${visibility}',
  modalSize: '${modalSize}',
  metadata: {
    closeOnClickOutside: true,
    showCloseButton: true,
    showBackdrop: true,
    closeOnEscape: true,
  },
});
`;
  }

  /**
   * Updates all necessary index files
   */
  private async updateIndexFiles(
    uiDir: string,
    modalDir: string,
    visibilityDir: string,
    normalizedName: string
  ): Promise<void> {
    // Update the visibility-specific index file
    const visibilityIndexPath = path.join(visibilityDir, "index.ts");
    let visibilityIndexContent = "";

    try {
      visibilityIndexContent = await readFile(visibilityIndexPath);
    } catch (error) {
      // If the file doesn't exist, create it with basic content
      visibilityIndexContent = `/**
 * @file Modal Dialog Components
 *
 * This file exports all modal dialog components.
 */

`;
    }

    // Add the export for the new modal
    const exportIdentifier = toCamelCase(normalizedName);
    const exportStatement = `export { default as ${exportIdentifier} } from './${normalizedName}';\n`;
    if (!visibilityIndexContent.includes(exportStatement)) {
      visibilityIndexContent += exportStatement;
      await writeFile(visibilityIndexPath, visibilityIndexContent);
    }

    // Update the modal type index file
    const modalIndexPath = path.join(modalDir, "index.ts");
    let modalIndexContent = "";

    try {
      modalIndexContent = await readFile(modalIndexPath);
    } catch (error) {
      // If the file doesn't exist, create it with basic content
      modalIndexContent = `/**
 * @file Modal Dialog Components Index
 *
 * This file exports all modal dialog components.
 */

// Export partner modals
export * from './partner';

// Export account modals
export * from './account';
`;
      await writeFile(modalIndexPath, modalIndexContent);
    }

    // Update the main UI index file
    const uiIndexPath = path.join(uiDir, "index.ts");
    let uiIndexContent = "";

    try {
      uiIndexContent = await readFile(uiIndexPath);
    } catch (error) {
      // If the file doesn't exist, create it with basic content
      uiIndexContent = `/**
 * @file UI Components Index
 *
 * This file exports all UI components for the ezWorkflow platform.
 */

// Export dashboard widgets
export * as dashboardWidgets from './dashboard-widget';

// Export modals
export * as modals from './modal';

// Export drawers
export * as drawers from './drawer';

// Export pages
export * as pages from './page';

// Export funnel builder blocks
export * as funnelBuilderBlocks from './funnel-builder-block';

// Export email builder blocks
export * as emailBuilderBlocks from './email-builder-block';
`;
      await writeFile(uiIndexPath, uiIndexContent);
    }

    // Check if the modals export exists
    if (!uiIndexContent.includes("export * as modals from './modal';")) {
      // If the modals export doesn't exist, add it
      uiIndexContent = uiIndexContent.replace(
        "/**\n * @file UI Components Index",
        "/**\n * @file UI Components Index\n *\n * This file exports all UI components for the ezWorkflow platform.\n */\n\n// Export modals\nexport * as modals from './modal';"
      );
      await writeFile(uiIndexPath, uiIndexContent);
    }
  }
}
