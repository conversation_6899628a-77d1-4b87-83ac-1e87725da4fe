/**
 * @file Drawer Handler
 *
 * This file implements the handler for creating drawer components.
 * It handles the creation of new drawer files, including prompting for metadata,
 * generating unique IDs, and updating index files to register the new drawer.
 */

import { logger } from "../../utils";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, UiComponentOptions } from "./index";
import inquirer from "inquirer";
import { v4 as uuidv4 } from "uuid";
import path from "path";
import { ensureDirectory, readFile, writeFile } from "../../utils";
import { getEzwConfig } from "../../utils/ezw-config";
import { validateName, toCamelCase } from "../../utils/validation";

/**
 * <PERSON><PERSON> for creating drawer components
 */
export class DrawerHandler implements UiHandler {
  /**
   * Handles the creation of a drawer component
   * @param name - Optional name of the drawer
   * @param options - UI component options
   */
  async handle(name: string | undefined, options: UiComponentOptions): Promise<void> {
    try {
      // If no name is provided, prompt for it
      if (!name) {
        const { drawerName } = await inquirer.prompt([
          {
            type: "input",
            name: "drawerName",
            message: "Enter drawer name:",
            validate: (input) => {
              const result = validateName(input);
              return result === true ? true : result;
            },
          },
        ]);
        name = drawerName;
      }

      // Validate drawer name
      const validationResult = validateName(name as string);
      if (validationResult === false) {
        process.exit(1);
      }

      // Use the normalized name (lowercase) for file paths
      const normalizedName = validationResult as string;

      // Get the current working directory
      const cwd = process.cwd();

      // Get the ezw configuration to determine the correct paths
      logger.spin("🔍 Reading ezWorkflow configuration...");
      const config = await getEzwConfig(cwd);
      logger.spinnerSuccess("Found ezWorkflow configuration");

      if (!config.uiDirPath) {
        logger.error("UI directory path not found in configuration");
        process.exit(1);
      }

      // Get the path for UI components
      const uiDir = path.join(cwd, config.uiDirPath);
      const drawerDir = path.join(uiDir, "drawer");
      const visibilityDir = path.join(drawerDir, options.visibility);

      // Ensure the directories exist
      await ensureDirectory(visibilityDir);

      // Check if the drawer already exists
      const drawerFilePath = path.join(visibilityDir, `${normalizedName}.ts`);

      try {
        await readFile(drawerFilePath);
        logger.error(`Drawer ${normalizedName} already exists at ${drawerFilePath}`);
        process.exit(1);
      } catch (error) {
        // File doesn't exist, which is what we want
      }

      // Generate a unique ID
      const drawerId = uuidv4();

      // Use default values for metadata
      const categories = ["navigation"];
      const tags = ["drawer", "navigation"];
      const description = `${name} drawer`;
      const drawerSize = "medium";
      const position = "right";

      // Create the drawer file
      const drawerContent = this.generateDrawerContent(
        drawerId,
        name as string,
        description,
        categories,
        tags,
        options.visibility,
        drawerSize,
        position
      );

      await writeFile(drawerFilePath, drawerContent);
      logger.success(`Created drawer file: ${drawerFilePath}`);

      // Update the index files
      await this.updateIndexFiles(uiDir, drawerDir, visibilityDir, normalizedName);
      logger.success("Updated index files");

      logger.success(`✅ Successfully created drawer: ${name}`);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error(`Failed to create drawer: ${errorMessage}`);
      process.exit(1);
    }
  }

  /**
   * Generates the content for a drawer file
   */
  private generateDrawerContent(
    id: string,
    name: string,
    description: string,
    categories: string[],
    tags: string[],
    visibility: 'partner' | 'account',
    drawerSize: string,
    position: string
  ): string {
    return `/**
 * @file ${name} Drawer
 *
 * This file defines a drawer component for the ezWorkflow platform.
 */

import { defineDrawer } from '@ezworkflow/node-sdk';

export default defineDrawer({
  id: '${id}',
  name: '${name}',
  description: '${description}',
  version: '1.0.0',
  categories: ${JSON.stringify(categories)},
  tags: ${JSON.stringify(tags)},
  visibility: '${visibility}',
  drawerSize: '${drawerSize}',
  position: '${position}',
  metadata: {
    closeOnClickOutside: true,
    showCloseButton: true,
    showBackdrop: true,
    closeOnEscape: true,
    draggable: false,
  },
});
`;
  }

  /**
   * Updates all necessary index files
   */
  private async updateIndexFiles(
    uiDir: string,
    drawerDir: string,
    visibilityDir: string,
    normalizedName: string
  ): Promise<void> {
    // Update the visibility-specific index file
    const visibilityIndexPath = path.join(visibilityDir, "index.ts");
    let visibilityIndexContent = "";

    try {
      visibilityIndexContent = await readFile(visibilityIndexPath);
    } catch (error) {
      // If the file doesn't exist, create it with basic content
      visibilityIndexContent = `/**
 * @file Drawer Components
 *
 * This file exports all drawer components.
 */

`;
    }

    // Add the export for the new drawer
    const exportIdentifier = toCamelCase(normalizedName);
    const exportStatement = `export { default as ${exportIdentifier} } from './${normalizedName}';\n`;
    if (!visibilityIndexContent.includes(exportStatement)) {
      visibilityIndexContent += exportStatement;
      await writeFile(visibilityIndexPath, visibilityIndexContent);
    }

    // Update the drawer type index file
    const drawerIndexPath = path.join(drawerDir, "index.ts");
    let drawerIndexContent = "";

    try {
      drawerIndexContent = await readFile(drawerIndexPath);
    } catch (error) {
      // If the file doesn't exist, create it with basic content
      drawerIndexContent = `/**
 * @file Drawer Components Index
 *
 * This file exports all drawer components.
 */

// Export partner drawers
export * from './partner';

// Export account drawers
export * from './account';
`;
      await writeFile(drawerIndexPath, drawerIndexContent);
    }

    // Update the main UI index file
    const uiIndexPath = path.join(uiDir, "index.ts");
    let uiIndexContent = "";

    try {
      uiIndexContent = await readFile(uiIndexPath);
    } catch (error) {
      // If the file doesn't exist, create it with basic content
      uiIndexContent = `/**
 * @file UI Components Index
 *
 * This file exports all UI components for the ezWorkflow platform.
 */

// Export dashboard widgets
export * as dashboardWidgets from './dashboard-widget';

// Export modals
export * as modals from './modal';

// Export drawers
export * as drawers from './drawer';

// Export pages
export * as pages from './page';

// Export funnel builder blocks
export * as funnelBuilderBlocks from './funnel-builder-block';

// Export email builder blocks
export * as emailBuilderBlocks from './email-builder-block';
`;
      await writeFile(uiIndexPath, uiIndexContent);
    }

    // Check if the drawers export exists
    if (!uiIndexContent.includes("export * as drawers from './drawer';")) {
      // If the drawers export doesn't exist, add it
      uiIndexContent = uiIndexContent.replace(
        "/**\n * @file UI Components Index",
        "/**\n * @file UI Components Index\n *\n * This file exports all UI components for the ezWorkflow platform.\n */\n\n// Export drawers\nexport * as drawers from './drawer';"
      );
      await writeFile(uiIndexPath, uiIndexContent);
    }
  }
}
