/**
 * @file Create UI Component Command
 *
 * This file implements the 'create ui' command for the ezWorkflow CLI.
 * It handles the creation of new UI component files, including prompting for metadata,
 * generating unique IDs, and updating index files to register the new UI component.
 *
 * The implementation uses a handler-based approach where each UI type has its own handler
 * in the ui-handlers directory. This makes the code more maintainable and easier to extend.
 */

import { logger } from "../utils";
import { UiType, UI_TYPE_HANDLERS, promptForUiType } from "./ui-handlers";
import { validateProjectDirectory } from "../utils/project-validation";
import inquirer from "inquirer";

/**
 * Creates a new UI component file and registers it in the project.
 *
 * This function handles the entire process of creating a new UI component:
 * 1. Validates that we're in a valid ezWorkflow project
 * 2. Prompts for only three pieces of information:
 *    - Component name (if not provided)
 *    - UI component type (if not provided)
 *    - Visibility (partner/account, if not provided)
 * 3. Delegates to the appropriate handler based on the UI type
 *
 * The function supports creating specialized UI component types:
 * - dashboard-widget: For creating dashboard widget components
 * - modal: For creating modal dialog components
 * - drawer: For creating slide-out drawer components
 * - page: For creating full page components
 * - funnel-builder-block: For creating funnel builder block components
 * - email-builder-block: For creating email builder block components
 *
 * @param name - Optional name of the UI component (will be normalized for file paths)
 * @param _options - Command options
 * @param _options.uiType - Optional UI component type
 * @param _options.visibility - Optional visibility type (partner/account)
 * @returns Promise that resolves when the UI component has been created
 */
export const createUi = async (
  name?: string,
  _options: {
    uiType?: string;
    visibility?: 'partner' | 'account';
  } = {}
): Promise<void> => {
  try {
    // Validate that we're in a valid ezWorkflow project
    const cwd = process.cwd();
    const isValidProject = await validateProjectDirectory(cwd);
    if (!isValidProject) {
      process.exit(1);
    }

    // If uiType is provided, use it; otherwise, prompt for UI type selection
    let uiType = _options.uiType as UiType | undefined;

    if (!uiType) {
      // Prompt for UI type selection
      uiType = await promptForUiType();
    } else if (!Object.values(UiType).includes(uiType as UiType)) {
      // If uiType is provided but not valid, show error
      logger.error(`Invalid UI type: ${uiType}`);
      logger.info(`Available UI types: ${Object.values(UiType).join(", ")}`);
      process.exit(1);
    }

    // Prompt for visibility type if not provided
    let visibility = _options.visibility;
    if (!visibility) {
      const { selectedVisibility } = await inquirer.prompt([
        {
          type: 'list',
          name: 'selectedVisibility',
          message: 'Select visibility type:',
          choices: [
            { name: 'Partner', value: 'partner' },
            { name: 'Account', value: 'account' }
          ],
          default: 'partner'
        }
      ]);
      visibility = selectedVisibility;
    }

    // Delegate to the appropriate handler based on the UI type
    if (uiType) {
      const handler = UI_TYPE_HANDLERS[uiType as UiType];
      if (handler) {
        await handler.handle(name, { ..._options, visibility: visibility as 'partner' | 'account' });
      } else {
        logger.error(`No handler found for UI type: ${uiType}`);
        process.exit(1);
      }
    } else {
      logger.error("No UI type selected");
      process.exit(1);
    }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error(`Failed to create UI component: ${errorMessage}`);
    process.exit(1);
  }
};
