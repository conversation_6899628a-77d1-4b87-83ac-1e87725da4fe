/**
 * @file Create Node Command
 *
 * This file implements the 'create node' command for the ezWorkflow CLI.
 * It handles the creation of new node and executor files, including prompting for metadata,
 * generating unique IDs, and updating index files to register the new node and executor.
 */

import path from 'path';
import {
  logger,
  ensureDirectory,
  readFile,
  writeFile,
  validateName
} from '../utils';
import { getEzwConfig } from '../utils/ezw-config';
import { v4 as uuidv4 } from 'uuid';
import inquirer from 'inquirer';

/**
 * Creates a new node and its executor files and registers them in the project.
 *
 * This function handles the entire process of creating a new node:
 * 1. Validates the node name
 * 2. Checks if the node or executor already exists
 * 3. Prompts for additional metadata (category, tags, version)
 * 4. Generates a unique ID for the node
 * 5. Creates the node and executor files with the appropriate content
 * 6. Updates the index files to register the new node and executor
 *
 * The node and executor files are created as a pair, with the executor
 * referencing the node's unique ID to establish the connection between them.
 *
 * @param name - Name of the node (will be normalized for file paths)
 * @param _options - Command options
 * @param _options.category - Optional predefined category for the node
 * @param _options.tags - Optional comma-separated list of tags
 * @returns Promise that resolves when the node and executor have been created
 */
export const createNode = async (
  name?: string,
  _options: { category?: string; tags?: string } = {}
): Promise<void> => {
  try {
    // Name validation is now handled in the CLI command handler
    if (!name) {
      logger.error('Node name is required');
      process.exit(1);
    }

    // Validate node name and get normalized version
    const validationResult = validateName(name);
    if (validationResult === false) {
      process.exit(1);
    }

    // Use the normalized name (lowercase) for file paths
    const normalizedName = validationResult as string;

    // Keep original name for display purposes
    const displayName = name;

    // Get the current working directory
    const cwd = process.cwd();

    // Get the ezw configuration to determine the correct paths
    logger.spin('🔍 Reading ezWorkflow configuration...');
    const config = await getEzwConfig(cwd);
    logger.spinnerSuccess('Found ezWorkflow configuration');

    // Get the paths for nodes and executors
    const nodesDir = path.join(cwd, config.nodesDirPath);
    const executorsDir = path.join(cwd, config.executorsDirPath);

    // Ensure the directories exist
    await ensureDirectory(nodesDir);
    await ensureDirectory(executorsDir);

    // Check if the node already exists (using normalized name for files)
    const nodeFilePath = path.join(nodesDir, `${normalizedName}.ts`);
    const executorFilePath = path.join(executorsDir, `${normalizedName}.ts`);

    try {
      await readFile(nodeFilePath);
      logger.error(`Node ${normalizedName} already exists at ${nodeFilePath}`);
      process.exit(1);
    } catch (error) {
      // File doesn't exist, which is what we want
    }

    try {
      await readFile(executorFilePath);
      logger.error(`Executor for node ${normalizedName} already exists at ${executorFilePath}`);
      process.exit(1);
    } catch (error) {
      // File doesn't exist, which is what we want
    }

    // Note: We'll create valid variable names in the index file update functions

    // Generate a unique 32-character ID
    const nodeId = uuidv4();

    // Prompt for additional metadata
    logger.info('📝 Please provide additional information about your node:');

    // Predefined categories for organizing nodes
    // These categories help users classify their nodes for better organization and discovery
    const predefinedCategories = [
      'Utilities',
      'Data Processing',
      'AI & Machine Learning',
      'API Integration',
      'File Operations',
      'Database',
      'Authentication',
      'Communication',
      'Analytics',
      'Other'
    ];

    // Use provided options or prompt for them
    let category = _options.category;
    let tags = _options.tags;
    let version = '0.0.1'; // Default version

    if (!category || !tags) {
      const questions = [
        {
          type: 'list' as const,
          name: 'category',
          message: 'Select a category for your node:',
          choices: [
            ...predefinedCategories,
            new inquirer.Separator(),
            {
              name: 'Custom category',
              value: 'custom'
            }
          ],
          default: 'Utilities',
          when: !category
        },
        {
          type: 'input' as const,
          name: 'customCategory',
          message: 'Enter your custom category:',
          when: (answers: any) => !category && answers.category === 'custom',
          validate: (input: any) => input.trim() !== '' ? true : 'Category cannot be empty'
        },
        {
          type: 'input' as const,
          name: 'tags',
          message: 'Enter tags (comma-separated):',
          when: !tags,
          default: 'utility',
          validate: (input: any) => {
            const trimmedTags = input.split(',').map((tag: string) => tag.trim()).filter((tag: string) => tag !== '');
            return trimmedTags.length > 0 ? true : 'At least one tag is required';
          }
        },
        {
          type: 'input' as const,
          name: 'version',
          message: 'Enter version number:',
          default: '0.0.1',
          validate: (input: any) => {
            return /^\d+\.\d+\.\d+$/.test(input) ? true : 'Version must be in format x.y.z (e.g., 0.0.1)';
          }
        }
      ];

      const answers = await inquirer.prompt(questions as any);

      // Set values from answers
      if (!category) {
        category = answers.category === 'custom' ? answers.customCategory : answers.category;
      }
      if (!tags) {
        tags = answers.tags;
      }
      version = answers.version;
    }

    // Process tags
    const tagsList = tags ? tags.split(',').map((tag: string) => tag.trim()).filter((tag: string) => tag !== '') : ['utility'];

    // Create the node file with enhanced content
    const nodeFileContent = `import { defineNode } from '@ezworkflow/node-sdk';

/**
 * ${displayName} node
 */
export default defineNode({
  id: 'node-${nodeId}',
  name: '${displayName}',
  description: 'What does this node do?',
  version: '${version}',
  category: '${category || 'Utilities'}',
  tags: [${tagsList.map(tag => `'${tag}'`).join(', ')}],
  // Other properties will be added later
});
`;

    // Create the executor file with simplified content
    const executorFileContent = `import { defineExecutor } from '@ezworkflow/node-sdk';

/**
 * Executor for the ${displayName} node
 */
export default defineExecutor('node-${nodeId}', async () => {});
`;

    // Write the files
    await writeFile(nodeFilePath, nodeFileContent);
    await writeFile(executorFilePath, executorFileContent);

    logger.success(`Created node file at ${nodeFilePath}`);
    logger.success(`Created executor file at ${executorFilePath}`);

    // Update the index files
    await updateNodeIndexFile(nodesDir, normalizedName, nodeId);
    await updateExecutorIndexFile(executorsDir, normalizedName);

    logger.success(`Node ${displayName} created successfully!`);
  } catch (error) {
    logger.error(`Failed to create node: ${error instanceof Error ? error.message : String(error)}`);
    process.exit(1);
  }
};

/**
 * Updates the nodes/index.ts file to include the new node.
 *
 * This function handles the process of updating the index file:
 * 1. Reads the existing index file or creates a new one if it doesn't exist
 * 2. Adds an import statement for the new node
 * 3. Updates the default export to include the new node
 *
 * The function is careful to maintain the structure of the existing file
 * and handles various edge cases like empty files or missing exports.
 *
 * @param nodesDir - Path to the nodes directory
 * @param nodeName - Name of the node (normalized)
 * @param nodeId - Unique ID of the node (used as the key in the export object)
 * @returns Promise that resolves when the index file has been updated
 */
async function updateNodeIndexFile(nodesDir: string, nodeName: string, nodeId: string): Promise<void> {
  const indexFilePath = path.join(nodesDir, 'index.ts');
  let indexContent = '';

  // No need for a variable name in this function as we use the node name directly

  try {
    indexContent = await readFile(indexFilePath);
  } catch (error) {
    // If the file doesn't exist, create a default one
    indexContent = `// This file is auto-generated and will be updated when new nodes are created
// Export all nodes from this directory

export default {};
`;
  }

  // Check if the node is already imported
  if (indexContent.includes(`import ${nodeName}Node from './${nodeName}'`)) {
    logger.warn(`Node ${nodeName} is already imported in the index file`);
    return;
  }

  // Add the import statement
  const importStatement = `// Import node definition
import ${nodeName}Node from './${nodeName}';\n`;

  // Find the position to insert the import
  const lastImportIndex = indexContent.lastIndexOf('import ');
  let newContent = '';

  if (lastImportIndex >= 0) {
    // Find the end of the last import statement
    const lastImportEndIndex = indexContent.indexOf('\n', lastImportIndex) + 1;
    newContent = indexContent.slice(0, lastImportEndIndex) + importStatement + indexContent.slice(lastImportEndIndex);
  } else {
    // No imports yet, add at the beginning after the comment
    const commentEndIndex = indexContent.indexOf('\n\n') + 2;
    newContent = indexContent.slice(0, commentEndIndex) + importStatement + indexContent.slice(commentEndIndex);
  }

  // Update the export statement
  if (newContent.includes('export default {};')) {
    newContent = newContent.replace('export default {};', `export default {
  '${nodeId}': ${nodeName}Node,
};`);
  } else {
    // Find the export default statement
    const exportStartIndex = newContent.indexOf('export default {');
    const exportEndIndex = newContent.indexOf('};', exportStartIndex) + 2;

    // Extract the current export object
    const exportObject = newContent.slice(exportStartIndex + 15, exportEndIndex - 2);

    // Add the new node to the export object
    const newExportObject = exportObject.trim().endsWith(',')
      ? `${exportObject}\n  '${nodeId}': ${nodeName}Node,\n`
      : `${exportObject}${exportObject.trim() ? ',\n' : ''}  '${nodeId}': ${nodeName}Node,\n`;

    // Replace the export object
    newContent = newContent.slice(0, exportStartIndex + 15) + newExportObject + newContent.slice(exportEndIndex - 2);
  }

  // Write the updated content
  await writeFile(indexFilePath, newContent);

  logger.success(`Updated nodes index file at ${indexFilePath}`);
}

/**
 * Updates the executors/index.ts file to include the new executor.
 *
 * This function handles the process of updating the index file:
 * 1. Reads the existing index file or creates a new one if it doesn't exist
 * 2. Adds an import statement for the new executor
 * 3. Updates the default export to include the new executor
 *
 * The function is careful to maintain the structure of the existing file
 * and handles various edge cases like empty files or missing exports.
 * It also handles variable name creation by replacing hyphens with underscores
 * to ensure valid JavaScript identifiers.
 *
 * @param executorsDir - Path to the executors directory
 * @param nodeName - Name of the node (normalized)
 * @returns Promise that resolves when the index file has been updated
 */
async function updateExecutorIndexFile(executorsDir: string, nodeName: string): Promise<void> {
  const indexFilePath = path.join(executorsDir, 'index.ts');
  let indexContent = '';

  // Create a valid variable name (replace hyphens with underscores)
  const varName = nodeName.replace(/-/g, '_');

  try {
    indexContent = await readFile(indexFilePath);
  } catch (error) {
    // If the file doesn't exist, create a default one
    indexContent = `// This file is auto-generated and will be updated when new executors are created
// Export all executors from this directory

export default {};
`;
  }

  // Check if the executor is already imported
  if (indexContent.includes(`import ${varName}Executor`)) {
    logger.warn(`Executor for node ${nodeName} is already imported in the index file`);
    return;
  }

  // Add the import statement
  const importStatement = `// Import executor definition
import ${varName}Executor from './${nodeName}';\n`;

  // Find the position to insert the import
  const lastImportIndex = indexContent.lastIndexOf('import ');
  let newContent = '';

  if (lastImportIndex >= 0) {
    // Find the end of the last import statement
    const lastImportEndIndex = indexContent.indexOf('\n', lastImportIndex) + 1;
    newContent = indexContent.slice(0, lastImportEndIndex) + importStatement + indexContent.slice(lastImportEndIndex);
  } else {
    // No imports yet, add at the beginning after the comment
    const commentEndIndex = indexContent.indexOf('\n\n') + 2;
    newContent = indexContent.slice(0, commentEndIndex) + importStatement + indexContent.slice(commentEndIndex);
  }

  // Update the export statement
  if (newContent.includes('export default {};')) {
    newContent = newContent.replace('export default {};', `export default {
  '${nodeName}': ${varName}Executor,
};`);
  } else {
    // Find the export default statement
    const exportStartIndex = newContent.indexOf('export default {');
    const exportEndIndex = newContent.indexOf('};', exportStartIndex) + 2;

    // Extract the current export object
    const exportObject = newContent.slice(exportStartIndex + 15, exportEndIndex - 2);

    // Add the new executor to the export object
    const newExportObject = exportObject.trim().endsWith(',')
      ? `${exportObject}\n  '${nodeName}': ${varName}Executor,\n`
      : `${exportObject}${exportObject.trim() ? ',\n' : ''}  '${nodeName}': ${varName}Executor,\n`;

    // Replace the export object
    newContent = newContent.slice(0, exportStartIndex + 15) + newExportObject + newContent.slice(exportEndIndex - 2);
  }

  // Write the updated content
  await writeFile(indexFilePath, newContent);

  logger.success(`Updated executors index file at ${indexFilePath}`);
}
