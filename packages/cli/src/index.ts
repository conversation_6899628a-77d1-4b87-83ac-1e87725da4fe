#!/usr/bin/env node

/**
 * @file CLI Entry Point
 *
 * This is the main entry point for the ezWorkflow CLI.
 * It sets up the command-line interface using Commander.js,
 * defines the available commands, and handles command execution.
 * It also provides a visually appealing interface with custom styling.
 */

import { Command } from "commander";
import { createProject } from "./commands/create-project";
import { createNode } from "./commands/create-node";
import { createTrigger } from "./commands/create-trigger";
import { createUi } from "./commands/create-ui";
import { version } from "../package.json";
import { createBanner, text, icons, createBox, colors } from "./utils/theme";
import { logger } from "./utils/logger";
import inquirer from "inquirer";
import { validateName } from "./utils/validation";
import { UiType } from "./commands/ui-handlers";

// No terminal theme detection needed here

// Skip the boxed header and just add a newline for spacing
console.log("");

// Display a fancy banner with high-contrast text
console.log(createBanner("ezWorkflow", "Slant"));

// Display a high-contrast subheader with bold text for better readability
console.log(
  text.highContrast("The next generation workflow automation platform", true) +
  "\n"
);

// Create the main program
const program = new Command();

// Set up program metadata with styled description
program
  .name("ezw")
  .description(
    text.body(
      "CLI tool for ezWorkflow - create and manage node projects, nodes, and triggers"
    )
  )
  .version(version, "-v, --version", text.body("Output the current version"));

program.command("init")
  .description(
    text.body("Initialize a new ezWorkflow project")
  )
  .option(
    "-p, --pm <pm>",
    text.body(
      "Package manager to use for installation (npm, bun, deno, pnpm, yarn)"
    )
  )
  .argument("[name]", text.body("Name of the project to create"))
  .option(
    "-t, --template <template>",
    text.body("Template to use (basic, full, custom)")
  )
  .option(
    "-d, --directory <directory>",
    text.body("Directory to create the project in")
  )
  .action(async (name, options) => {
    // If name is not provided, prompt for it
    if (!name) {
      const { projectName } = await inquirer.prompt([
        {
          type: "input",
          name: "projectName",
          message: "Enter project name:",
          validate: (input) => {
            if (!input.trim()) return "Project name cannot be empty";
            return true;
          }
        }
      ]);
      name = projectName;
    }
    await createProject(name, options);
  });

// Create command with enhanced descriptions
program
  .command("create")
  .description(
    text.body("Create a new node, trigger, or UI component")
  )
  .argument(
    "[type]",
    text.body("Type of item to create (node, trigger, ui)")
  )
  .argument(
    "[subtype]",
    text.body("Subtype of UI component (required only when type is 'ui')")
  )
  .argument(
    "[name]",
    text.body("Name of the component to create")
  )
  .action(async (type, subtype, name) => {
    try {
      // If type is not provided, prompt for it
      if (!type) {
        const { selectedType } = await inquirer.prompt([
          {
            type: "list",
            name: "selectedType",
            message: "Select component type:",
            choices: [
              { name: "Node", value: "node" },
              { name: "Trigger", value: "trigger" },
              { name: "UI Component", value: "ui" },
            ],
            default: "node",
          }
        ]);
        type = selectedType;
      }

      // Convert type to lowercase for consistency
      const _type = type.toLowerCase();

      // Handle different component types
      switch (_type) {
        case "node":
          // If name is not provided, prompt for it
          if (!name) {
            const { nodeName } = await inquirer.prompt([
              {
                type: "input",
                name: "nodeName",
                message: "Enter node name:",
                validate: (input) => {
                  const result = validateName(input);
                  return result === true || typeof result === 'string' ? true : result;
                },
              }
            ]);
            name = nodeName;
          }
          await createNode(name);
          break;

        case "trigger":
          // If name is not provided, prompt for it
          if (!name) {
            const { triggerName } = await inquirer.prompt([
              {
                type: "input",
                name: "triggerName",
                message: "Enter trigger name:",
                validate: (input) => {
                  const result = validateName(input);
                  return result === true || typeof result === 'string' ? true : result;
                },
              }
            ]);
            name = triggerName;
          }
          await createTrigger(name);
          break;

        case "ui":
          // Handle UI component creation with subtypes
          let uiSubtype = subtype;

          // If subtype is not provided, prompt for it
          if (!uiSubtype) {
            const { selectedSubtype } = await inquirer.prompt([
              {
                type: "list",
                name: "selectedSubtype",
                message: "Select UI component type:",
                choices: [
                  { name: "Dashboard Widget", value: UiType.DASHBOARD_WIDGET },
                  { name: "Modal Dialog", value: UiType.MODAL },
                  { name: "Drawer", value: UiType.DRAWER },
                  { name: "Page", value: UiType.PAGE },
                  { name: "Funnel Builder Block", value: UiType.FUNNEL_BUILDER_BLOCK },
                  { name: "Email Builder Block", value: UiType.EMAIL_BUILDER_BLOCK },
                ],
              }
            ]);
            uiSubtype = selectedSubtype;
          }

          // Validate that the subtype is valid
          if (!Object.values(UiType).includes(uiSubtype as UiType)) {
            logger.error(`Invalid UI component type: ${uiSubtype}`);
            logger.info(`Available UI component types: ${Object.values(UiType).join(", ")}`);
            process.exit(1);
          }

          // If name is not provided, prompt for it
          if (!name) {
            const { uiName } = await inquirer.prompt([
              {
                type: "input",
                name: "uiName",
                message: `Enter ${uiSubtype} name:`,
                validate: (input) => {
                  const result = validateName(input);
                  return result === true || typeof result === 'string' ? true : result;
                },
              }
            ]);
            name = uiName;
          }

          // Call createUi with the appropriate parameters
          await createUi(name, { uiType: uiSubtype });
          break;

        default:
          logger.error(`Unknown component type: ${_type}`);
          logger.info("Available types: node, trigger, ui");
          process.exit(1);
      }
    } catch (error) {
      logger.error(
        `An error occurred: ${error instanceof Error ? error.message : String(error)}`
      );
      process.exit(1);
    }
  });

// Add styled help information
const examplesBox = createBox(
  `${text.sectionTitle("EXAMPLES")}\n\n` +
  `${icons.arrow} Create a new project:\n` +
  `  ${text.command("ezw init my-node-project")}\n\n` +
  `${icons.arrow} Create a project with a specific package manager:\n` +
  `  ${text.command("ezw init my-node-project --pm bun")}\n\n` +
  `${icons.arrow} Create a new node:\n` +
  `  ${text.command("ezw create node my-custom-node")}\n\n` +
  `${icons.arrow} Create a new trigger:\n` +
  `  ${text.command("ezw create trigger my-custom-trigger")}\n\n` +
  `${icons.arrow} Create a new UI component with prompts:\n` +
  `  ${text.command("ezw create ui")}\n\n` +
  `${icons.arrow} Create specialized UI components:\n` +
  `  ${text.command("ezw create ui dashboard-widget my-widget")}\n` +
  `  ${text.command("ezw create ui modal my-modal")}\n` +
  `  ${text.command("ezw create ui drawer my-drawer")}\n` +
  `  ${text.command("ezw create ui page my-page")}\n` +
  `  ${text.command("ezw create ui funnel-builder-block my-block")}\n` +
  `  ${text.command("ezw create ui email-builder-block my-block")}`,
  {
    padding: 1,
    margin: { top: 1, bottom: 1 },
    borderStyle: "round",
    borderColor: colors.primary.main,
    backgroundColor: colors.neutral.black,
  }
);

program.addHelpText("after", examplesBox);

// Parse command line arguments
program.parse(process.argv);

// If no arguments, show help with a styled header
if (process.argv.length === 2) {
  console.log(text.header("\nWelcome to ezWorkflow CLI!\n"));
  console.log(text.body("Get started by running one of the commands below:"));
  program.help();
}
