/**
 * @file Prompt Utilities
 *
 * This file provides utilities for prompting the user for input using inquirer.
 * It includes functions for prompting for runtime templates, project information,
 * and other configuration options needed during project creation and setup.
 */

import inquirer from 'inquirer';
import { logger } from './logger';

/**
 * Runtime template options for Hono.js backend runtimes.
 *
 * This type defines the supported Hono.js runtime environments that can be used
 * with ezWorkflow. Each runtime has specific configuration and integration requirements
 * that are handled by the corresponding runtime implementation.
 */
export type HonoRuntimeTemplate =
  | 'aws-lambda'
  | 'bun'
  | 'cloudflare-workers'
  | 'deno'
  | 'fastly'
  | 'lambda-edge'
  | 'netlify'
  | 'nextjs'
  | 'nodejs'
  | 'vercel'
  | 'x-basic';

/**
 * Human-readable descriptions for each Hono.js runtime template.
 *
 * This mapping provides user-friendly descriptions for each runtime option,
 * which are displayed in the CLI when prompting the user to select a runtime.
 * Each description briefly explains what the runtime is and its main use case.
 */
export const runtimeDescriptions: Record<HonoRuntimeTemplate, string> = {
  'aws-lambda': 'AWS Lambda - Serverless functions on AWS',
  'bun': 'Bun - JavaScript runtime with built-in bundler',
  'cloudflare-workers': 'Cloudflare Workers - Edge computing platform',
  'deno': 'Deno - Secure JavaScript/TypeScript runtime',
  'fastly': 'Fastly Compute@Edge - Edge computing platform',
  'lambda-edge': 'AWS Lambda@Edge - Run code at AWS edge locations',
  'netlify': 'Netlify Edge Functions - Edge computing on Netlify',
  'nextjs': 'Next.js API Routes - API routes in Next.js framework',
  'nodejs': 'Node.js Server - Traditional Node.js server',
  'vercel': 'Vercel Edge - Edge computing on Vercel',
  'x-basic': 'HonoX Basic - Hono with JSX support'
};

// Note: Runtime-specific paths are now handled by the runtime implementations in the runtimes directory

/**
 * Prompts the user to select a Hono.js runtime template.
 *
 * This function displays a list of available Hono.js runtime templates
 * with their descriptions and allows the user to select one. The selected
 * runtime determines how the project will be structured and configured.
 *
 * @returns Promise that resolves to the selected runtime template
 * @throws Error if the prompt fails for any reason
 */
export const promptForRuntimeTemplate = async (): Promise<HonoRuntimeTemplate> => {
  try {
    // List of supported backend runtimes
    const backendRuntimes: HonoRuntimeTemplate[] = [
      'aws-lambda',
      'bun',
      'cloudflare-workers',
      'deno',
      'fastly',
      'lambda-edge',
      'netlify',
      'nextjs',
      'nodejs',
      'vercel',
      'x-basic'
    ];

    const { template } = await inquirer.prompt([
      {
        type: 'list',
        name: 'template',
        message: 'Select a runtime for your server:',
        choices: backendRuntimes.map(runtime => ({
          name: runtimeDescriptions[runtime],
          value: runtime
        })),
      },
    ]);

    return template;
  } catch (error) {
    logger.error('Failed to prompt for runtime template');
    throw error;
  }
};

/**
 * Prompts the user for additional project information.
 *
 * This function collects metadata about the project, including:
 * - Essential author information (name and email)
 * - Project categories (selected from a predefined list)
 * - Tags for better discoverability
 * - Optional API key for authentication
 *
 * This information is used to populate the project configuration and
 * provide better organization and security for the project.
 *
 * @returns Promise that resolves to an object containing the collected information
 * @throws Error if the prompt fails for any reason
 */
export const promptForAdditionalInfo = async (): Promise<{
  authorName: string;
  authorEmail: string;
  categories: string[];
  tags: string;
  appType: 'partner' | 'account';
  apiKey?: string;
}> => {
  try {
    const answers = await inquirer.prompt([
      // Author information
      {
        type: 'input',
        name: 'authorName',
        message: 'Author Name (required):',
        default: 'ezworkflow',
        validate: (input) => {
          if (input.trim() === '') {
            return 'Author name is required';
          }
          return true;
        },
      },
      {
        type: 'input',
        name: 'authorEmail',
        message: 'Author Email (required):',
        default: '<EMAIL>',
        validate: (input) => {
          if (input.trim() === '') {
            return 'Author email is required';
          }
          // Simple email validation
          if (!input.includes('@') || !input.includes('.')) {
            return 'Please enter a valid email address';
          }
          return true;
        },
      },
      // App Type
      {
        type: 'list',
        name: 'appType',
        message: 'Application Type:',
        choices: [
          { name: 'Partner Application', value: 'partner' },
          { name: 'Account Application', value: 'account' }
        ],
        default: 'partner',
      },
      // Project categories
      {
        type: 'checkbox',
        name: 'categories',
        message: 'Categories (select at least one):',
        choices: [
          { name: 'AI & Machine Learning', value: 'AI & Machine Learning' },
          { name: 'Analytics', value: 'Analytics' },
          { name: 'API Integration', value: 'API Integration' },
          { name: 'Authentication', value: 'Authentication' },
          { name: 'Automation', value: 'Automation' },
          { name: 'Business Logic', value: 'Business Logic' },
          { name: 'Calendar & Scheduling', value: 'Calendar & Scheduling' },
          { name: 'Cloud Services', value: 'Cloud Services' },
          { name: 'Communication', value: 'Communication' },
          { name: 'Content Management', value: 'Content Management' },
          { name: 'CRM', value: 'CRM' },
          { name: 'Data Processing', value: 'Data Processing' },
          { name: 'Data Transformation', value: 'Data Transformation' },
          { name: 'Database', value: 'Database' },
          { name: 'DevOps', value: 'DevOps' },
          { name: 'Document Processing', value: 'Document Processing' },
          { name: 'E-commerce', value: 'E-commerce' },
          { name: 'Email', value: 'Email' },
          { name: 'Event Processing', value: 'Event Processing' },
          { name: 'File Operations', value: 'File Operations' },
          { name: 'Forms & Surveys', value: 'Forms & Surveys' },
          { name: 'Geo & Maps', value: 'Geo & Maps' },
          { name: 'Health & Medical', value: 'Health & Medical' },
          { name: 'HR & Recruitment', value: 'HR & Recruitment' },
          { name: 'Integrations', value: 'Integrations' },
          { name: 'IoT', value: 'IoT' },
          { name: 'Legal', value: 'Legal' },
          { name: 'Media Processing', value: 'Media Processing' },
          { name: 'Messaging', value: 'Messaging' },
          { name: 'Monitoring & Logging', value: 'Monitoring & Logging' },
          { name: 'Notifications', value: 'Notifications' },
          { name: 'Other', value: 'Other' },
          { name: 'Payments', value: 'Payments' },
          { name: 'Project Management', value: 'Project Management' },
          { name: 'Reporting', value: 'Reporting' },
          { name: 'Sales & Marketing', value: 'Sales & Marketing' },
          { name: 'Scheduling', value: 'Scheduling' },
          { name: 'Search', value: 'Search' },
          { name: 'Security', value: 'Security' },
          { name: 'Social Media', value: 'Social Media' },
          { name: 'Storage', value: 'Storage' },
          { name: 'Task Management', value: 'Task Management' },
          { name: 'Templates', value: 'Templates' },
          { name: 'Translation & Localization', value: 'Translation & Localization' },
          { name: 'Utilities', value: 'Utilities' },
          { name: 'Visualization', value: 'Visualization' },
          { name: 'Webhooks', value: 'Webhooks' },
          { name: 'Workflow Automation', value: 'Workflow Automation' }
        ],
        validate: (input) => {
          if (input.length === 0) {
            return 'You must select at least one category';
          }
          return true;
        },
      },
      // Tags
      {
        type: 'input',
        name: 'tags',
        message: 'Tags (comma separated):',
        default: '',
      },
      // API Key
      {
        type: 'confirm',
        name: 'addApiKey',
        message: 'Do you want to add an API key now? (You can add it later)',
        default: false,
      },
      {
        type: 'input',
        name: 'apiKey',
        message: 'Enter your 64-character API key:',
        when: (answers) => answers.addApiKey,
        validate: (input) => {
          if (input.length === 64) {
            return true;
          }
          return 'API key must be exactly 64 characters long';
        },
      },
    ]);

    return answers;
  } catch (error) {
    logger.error('Failed to prompt for additional information');
    throw error;
  }
};
