/**
 * @file Table Utility
 *
 * This file provides utilities for creating and displaying tables
 * in the CLI. It uses cli-table3 to create visually appealing tables
 * for displaying structured data.
 */

import Table from 'cli-table3';
import chalk from 'chalk';
import { colors } from './theme';

/**
 * Options for creating a table
 */
export interface TableOptions {
  /** The headers for the table columns */
  headers?: string[];
  /** Whether to style the headers (default: true) */
  styleHeaders?: boolean;
  /** The character set to use for the table borders */
  chars?: Partial<Record<'top' | 'topMid' | 'topLeft' | 'topRight' | 'bottom' | 'bottomMid' | 'bottomLeft' | 'bottomRight' | 'left' | 'leftMid' | 'mid' | 'midMid' | 'right' | 'rightMid' | 'middle', string>>;
  /** The style to use for the table borders */
  style?: {
    /** The style for the table border */
    border?: string[];
    /** The style for the table header */
    header?: string[];
  };
  /** Whether to color alternate rows (default: false) */
  colorAlternateRows?: boolean;
  /** The width of the columns (auto by default) */
  colWidths?: number[];
  /** Whether to word-wrap the content (default: false) */
  wordWrap?: boolean;
  /** Whether to truncate the content (default: false) */
  truncate?: boolean;
}

/**
 * Creates a table with the given options
 * @param options - Options for the table
 * @returns A table instance
 */
export const createTable = (options: TableOptions = {}): Table.Table => {
  const {
    headers = [],
    styleHeaders = true,
  } = options;

  // Define default character set for the table borders
  const defaultChars = {
    'top': '═',
    'top-mid': '╤',
    'top-left': '╔',
    'top-right': '╗',
    'bottom': '═',
    'bottom-mid': '╧',
    'bottom-left': '╚',
    'bottom-right': '╝',
    'left': '║',
    'left-mid': '╟',
    'mid': '─',
    'mid-mid': '┼',
    'right': '║',
    'right-mid': '╢',
    'middle': '│'
  };

  // Define default style for the table
  const defaultStyle = {
    border: [chalk.hex(colors.primary.main).toString()],
    header: [chalk.bold.hex(colors.primary.light).toString()],
  };

  // Create the table with the specified options
  const tableOptions: Table.TableConstructorOptions = {
    head: styleHeaders ? headers.map(header => chalk.bold.hex(colors.primary.light)(header)) : headers,
    chars: options.chars || defaultChars,
    style: options.style || defaultStyle,
    colWidths: options.colWidths,
  };

  // Create the table with the specified options
  const table = new Table(tableOptions);

  return table;
};

/**
 * Adds a row to the table with optional styling
 * @param table - The table to add the row to
 * @param row - The row data to add
 * @param index - The index of the row (used for alternate row coloring)
 * @param colorAlternateRows - Whether to color alternate rows
 */
export const addRow = (
  table: Table.Table,
  row: any[],
  index: number,
  colorAlternateRows = false
): void => {
  if (colorAlternateRows && index % 2 === 1) {
    // Color alternate rows with a subtle background
    table.push(row.map(cell => chalk.bgHex(colors.neutral.darkGray)(cell)));
  } else {
    table.push(row);
  }
};

/**
 * Creates a simple key-value table for displaying properties
 * @param data - An object containing key-value pairs to display
 * @param title - Optional title for the table
 * @returns A formatted table string
 */
export const createPropertyTable = (
  data: Record<string, any>,
  title?: string
): string => {
  const table = createTable({
    chars: {
      'top': '',
      'topMid': '',
      'topLeft': '',
      'topRight': '',
      'bottom': '',
      'bottomMid': '',
      'bottomLeft': '',
      'bottomRight': '',
      'left': '',
      'leftMid': '',
      'mid': '',
      'midMid': '',
      'right': '',
      'rightMid': '',
      'middle': '  '
    },
    style: {
      border: [],
      header: [],
    },
  });

  // Add title if provided
  if (title) {
    table.push([{ colSpan: 2, content: chalk.bold.hex(colors.primary.main)(title), hAlign: 'center' }]);
  }

  // Add each key-value pair as a row
  Object.entries(data).forEach(([key, value]) => {
    table.push([
      chalk.hex(colors.accent.blue)(`${key}:`),
      typeof value === 'string' ? value : JSON.stringify(value)
    ]);
  });

  return table.toString();
};

export default {
  createTable,
  addRow,
  createPropertyTable,
};
