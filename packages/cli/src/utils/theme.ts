/**
 * @file Theme Utility
 *
 * This file provides a consistent theme and styling for the CLI.
 * It defines color palettes, styling options, and helper functions
 * for creating visually appealing CLI interfaces.
 */

import chalk from 'chalk';
import gradient from 'gradient-string';
import boxen, { Options as BoxenOptions } from 'boxen';
import figlet from 'figlet';
import { detectTerminalTheme } from './terminal';

// Create gradient functions with proper typing
const primaryGradient = gradient(['#00C7B7', '#118AB2']);

/**
 * Color palette for the CLI
 *
 * This object defines the color scheme used throughout the CLI
 * to ensure visual consistency. Colors are organized by purpose
 * (primary, secondary, etc.) and include both base colors and
 * accent variations.
 */
const colors = {
  // Primary brand colors
  primary: {
    main: '#00C7B7',
    light: '#4ECDC4',
    dark: '#118AB2',
    // High contrast versions for better readability
    highContrast: {
      dark: '#00E6D2', // Brighter teal for dark terminals
      light: '#008577', // Darker teal for light terminals
    }
  },
  // Secondary accent colors
  secondary: {
    main: '#FF6B6B',
    light: '#FF9F9F',
    dark: '#E84855',
    // High contrast versions for better readability
    highContrast: {
      dark: '#FF8585', // Brighter red for dark terminals
      light: '#D32F2F', // Darker red for light terminals
    }
  },
  // Neutral colors for text and backgrounds
  neutral: {
    black: '#1A1A1A',
    darkGray: '#333333',
    gray: '#666666',
    lightGray: '#AAAAAA',
    white: '#FFFFFF',
    // High contrast versions for better readability
    highContrast: {
      dark: '#FFFFFF', // Pure white for dark terminals
      light: '#000000', // Pure black for light terminals
    }
  },
  // Semantic colors for status indicators
  status: {
    success: '#06D6A0',
    warning: '#FFD166',
    error: '#EF476F',
    info: '#118AB2',
    // High contrast versions for better readability
    highContrast: {
      success: {
        dark: '#0AFFBE', // Brighter green for dark terminals
        light: '#00875A', // Darker green for light terminals
      },
      warning: {
        dark: '#FFDF85', // Brighter yellow for dark terminals
        light: '#B78103', // Darker yellow for light terminals
      },
      error: {
        dark: '#FF6B8B', // Brighter red for dark terminals
        light: '#B71C1C', // Darker red for light terminals
      },
      info: {
        dark: '#25A0E0', // Brighter blue for dark terminals
        light: '#0A558C', // Darker blue for light terminals
      }
    }
  },
  // Accent colors for highlights and special elements
  accent: {
    purple: '#9B5DE5',
    blue: '#0496FF',
    yellow: '#F9C80E',
    orange: '#F86624',
    teal: '#00C49A',
    // High contrast versions for better readability
    highContrast: {
      purple: {
        dark: '#B57BFF', // Brighter purple for dark terminals
        light: '#6A1B9A', // Darker purple for light terminals
      },
      blue: {
        dark: '#42A5FF', // Brighter blue for dark terminals
        light: '#0D47A1', // Darker blue for light terminals
      },
      yellow: {
        dark: '#FFD740', // Brighter yellow for dark terminals
        light: '#B7860B', // Darker yellow for light terminals
      },
      orange: {
        dark: '#FF8A50', // Brighter orange for dark terminals
        light: '#BF360C', // Darker orange for light terminals
      },
      teal: {
        dark: '#1DE9B6', // Brighter teal for dark terminals
        light: '#00796B', // Darker teal for light terminals
      }
    }
  },
};

// Export the colors object
export { colors };

/**
 * Styled text functions
 *
 * These functions apply consistent styling to different types of text
 * elements in the CLI, such as headers, subheaders, and body text.
 * They use theme-aware colors for better readability in different terminal environments.
 */
export const text = {
  /**
   * Formats text as a main header with gradient coloring
   * @param text - The text to format as a header
   * @returns Formatted header text
   */
  header: (text: string): string => {
    return primaryGradient(chalk.bold(text));
  },

  /**
   * Formats text as a subheader with primary color
   * @param text - The text to format as a subheader
   * @returns Formatted subheader text
   */
  subheader: (text: string): string => {
    const theme = detectTerminalTheme();
    const color = theme === 'light'
      ? colors.primary.highContrast.light
      : colors.primary.highContrast.dark;

    return chalk.hex(color).bold(text);
  },

  /**
   * Formats text as a section title with secondary color
   * @param text - The text to format as a section title
   * @returns Formatted section title text
   */
  sectionTitle: (text: string): string => {
    const theme = detectTerminalTheme();
    const color = theme === 'light'
      ? colors.secondary.highContrast.light
      : colors.secondary.highContrast.dark;

    return chalk.hex(color).bold(text);
  },

  /**
   * Formats text as body text with optional emphasis
   * @param text - The text to format
   * @param emphasis - Whether to emphasize the text (default: false)
   * @returns Formatted body text
   */
  body: (text: string, emphasis = false): string => {
    const theme = detectTerminalTheme();
    const color = theme === 'light'
      ? colors.neutral.highContrast.light
      : colors.neutral.highContrast.dark;

    return emphasis
      ? chalk.hex(color).bold(text)
      : chalk.hex(color)(text);
  },

  /**
   * Formats text as a dimmed note
   * @param text - The text to format as a note
   * @returns Formatted note text
   */
  note: (text: string): string => {
    const theme = detectTerminalTheme();
    const color = theme === 'light'
      ? colors.neutral.gray
      : colors.neutral.lightGray;

    return chalk.hex(color).italic(text);
  },

  /**
   * Formats text as a command example
   * @param text - The command text to format
   * @returns Formatted command text
   */
  command: (text: string): string => {
    const theme = detectTerminalTheme();
    const color = theme === 'light'
      ? colors.neutral.darkGray
      : colors.neutral.lightGray;

    return chalk.hex(color).bold(`$ ${text}`);
  },

  /**
   * Formats text as a URL or link
   * @param text - The URL text to format
   * @returns Formatted URL text
   */
  url: (text: string): string => {
    const theme = detectTerminalTheme();
    const color = theme === 'light'
      ? colors.accent.highContrast.blue.light
      : colors.accent.highContrast.blue.dark;

    return chalk.hex(color).underline(text);
  },

  /**
   * Formats text with high contrast colors for maximum readability
   * @param text - The text to format
   * @param bold - Whether to make the text bold (default: true)
   * @returns Formatted high-contrast text
   */
  highContrast: (text: string, bold = true): string => {
    const theme = detectTerminalTheme();
    const color = theme === 'light'
      ? colors.neutral.highContrast.light
      : colors.neutral.highContrast.dark;

    return bold
      ? chalk.hex(color).bold(text)
      : chalk.hex(color)(text);
  },

  /**
   * Formats text as a title with high contrast and emphasis
   * @param text - The text to format as a title
   * @returns Formatted title text with high contrast
   */
  title: (text: string): string => {
    const theme = detectTerminalTheme();
    const color = theme === 'light'
      ? colors.primary.highContrast.light
      : colors.primary.highContrast.dark;

    return chalk.hex(color).bold.underline(text);
  },
};

/**
 * Icon set for the CLI
 *
 * This object defines a set of icons used throughout the CLI
 * for different purposes, such as status indicators and actions.
 */
export const icons = {
  info: chalk.hex(colors.status.info)('ℹ'),
  success: chalk.hex(colors.status.success)('✓'),
  warning: chalk.hex(colors.status.warning)('⚠'),
  error: chalk.hex(colors.status.error)('✖'),
  arrow: chalk.hex(colors.primary.main)('→'),
  bullet: chalk.hex(colors.primary.light)('•'),
  star: chalk.hex(colors.accent.yellow)('★'),
  rocket: '🚀',
  gear: '⚙️',
  sparkles: '✨',
  package: '📦',
  document: '📄',
  folder: '📁',
  code: '💻',
  check: '✅',
  x: '❌',
  lightbulb: '💡',
  wrench: '🔧',
  link: '🔗',
  clock: '🕒',
};

/**
 * Default boxen options for creating styled boxes
 *
 * These options define the default appearance of boxes created with boxen,
 * including padding, margins, border style, and colors.
 */
export const defaultBoxOptions: BoxenOptions = {
  padding: 1,
  margin: 1,
  borderStyle: 'round',
  borderColor: colors.primary.main,
  backgroundColor: colors.neutral.black,
};

/**
 * Creates a styled box with the given content and options
 * @param content - The content to display in the box
 * @param options - Optional boxen options to override defaults
 * @returns Boxed content as a string
 */
export const createBox = (content: string, options?: Partial<BoxenOptions>): string => {
  return boxen(content, { ...defaultBoxOptions, ...options });
};

/**
 * Creates a figlet text banner with theme-aware high-contrast coloring
 * @param text - The text to convert to figlet format
 * @param font - Optional figlet font (default: 'Standard')
 * @returns Figlet text with appropriate coloring for the terminal theme
 */
export const createBanner = (text: string, font: figlet.Fonts = 'Standard'): string => {
  try {
    const figletText = figlet.textSync(text, { font });

    // Determine the appropriate color based on terminal theme
    const theme = detectTerminalTheme();

    if (theme === 'light') {
      // For light terminals, use a solid high-contrast color instead of gradient
      return chalk.hex(colors.primary.highContrast.light).bold(figletText);
    } else {
      // For dark terminals, use a brighter color or gradient for better visibility
      // We'll use a custom gradient with higher contrast colors
      const customGradient = gradient([
        colors.primary.highContrast.dark,
        colors.accent.highContrast.blue.dark
      ]);
      return customGradient(figletText);
    }
  } catch (error) {
    // Fallback if figlet fails
    const theme = detectTerminalTheme();
    const color = theme === 'light'
      ? colors.primary.highContrast.light
      : colors.primary.highContrast.dark;

    return chalk.hex(color).bold(text.toUpperCase());
  }
};

/**
 * Creates a styled divider line
 * @param char - Character to use for the divider (default: '─')
 * @param length - Length of the divider (default: 50)
 * @returns Styled divider line
 */
export const createDivider = (char = '─', length = 50): string => {
  return primaryGradient(char.repeat(length));
};

export default {
  colors,
  text,
  icons,
  createBox,
  createBanner,
  createDivider,
};
