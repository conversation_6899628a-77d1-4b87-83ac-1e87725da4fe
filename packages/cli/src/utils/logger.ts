/**
 * @file Logger Utility
 *
 * This file provides an enhanced logging utility for the CLI with support for
 * colored output, animated spinners, and different log levels.
 * It uses chalk for colored output, ora for spinners, and custom styling
 * to create a visually appealing and modern CLI experience.
 */

import chalk from 'chalk';
import ora, { Ora, Options as OraOptions } from 'ora';
import { createSpinner, Spinner } from 'nanospinner';
import cliSpinners from 'cli-spinners';
import { colors, icons, text } from './theme';

// Define spinner types for different operations
const spinnerTypes = {
  default: {
    spinner: cliSpinners.dots,
    color: colors.primary.main,
  },
  install: {
    spinner: cliSpinners.bouncingBar,
    color: colors.accent.blue,
  },
  create: {
    spinner: cliSpinners.star,
    color: colors.accent.purple,
  },
  build: {
    spinner: cliSpinners.arrow3,
    color: colors.accent.teal,
  },
  deploy: {
    spinner: cliSpinners.moon,
    color: colors.accent.orange,
  },
};

/**
 * Enhanced logger utility class for CLI output.
 *
 * This class provides methods for logging messages with different levels
 * (info, success, warning, error, debug) and supports showing animated spinners
 * for long-running operations. It handles the interaction between regular
 * logging and spinners to ensure they don't interfere with each other.
 */
class Logger {
  private spinner: Ora | null = null;
  private nanoSpinner: Spinner | null = null;
  private debugEnabled: boolean = process.env.DEBUG === 'true' || process.env.DEBUG === '1';
  private useNanoSpinner: boolean = false;

  /**
   * Logs an informational message to the console.
   *
   * This method temporarily stops any active spinner, logs the message
   * with a styled info icon, and then restarts the spinner if one was active.
   *
   * @param message - The information message to log
   */
  info(message: string): void {
    if (this.spinner) {
      this.spinner.stop();
    }

    if (this.nanoSpinner) {
      this.nanoSpinner.stop();
      this.nanoSpinner = null;
    }

    console.log(icons.info, text.body(message));

    if (this.spinner) {
      this.spinner.start();
    }
  }

  /**
   * Logs a success message to the console.
   *
   * This method temporarily stops any active spinner, logs the message
   * with a styled success icon, and then restarts the spinner if one was active.
   *
   * @param message - The success message to log
   */
  success(message: string): void {
    if (this.spinner) {
      this.spinner.stop();
    }

    if (this.nanoSpinner) {
      this.nanoSpinner.stop();
      this.nanoSpinner = null;
    }

    console.log(icons.success, chalk.hex(colors.status.success)(message));

    if (this.spinner) {
      this.spinner.start();
    }
  }

  /**
   * Logs a warning message to the console.
   *
   * This method temporarily stops any active spinner, logs the message
   * with a styled warning icon, and then restarts the spinner if one was active.
   *
   * @param message - The warning message to log
   */
  warn(message: string): void {
    if (this.spinner) {
      this.spinner.stop();
    }

    if (this.nanoSpinner) {
      this.nanoSpinner.stop();
      this.nanoSpinner = null;
    }

    console.log(icons.warning, chalk.hex(colors.status.warning)(message));

    if (this.spinner) {
      this.spinner.start();
    }
  }

  /**
   * Logs an error message to the console.
   *
   * This method temporarily stops any active spinner, logs the message
   * with a styled error icon to the error stream, and then restarts the spinner if one was active.
   *
   * @param message - The error message to log
   */
  error(message: string): void {
    if (this.spinner) {
      this.spinner.stop();
    }

    if (this.nanoSpinner) {
      this.nanoSpinner.stop();
      this.nanoSpinner = null;
    }

    console.error(icons.error, chalk.hex(colors.status.error)(message));

    if (this.spinner) {
      this.spinner.start();
    }
  }

  /**
   * Starts a spinner with the given message.
   *
   * This method either updates the text of an existing spinner or
   * creates a new spinner if none is active. Spinners are useful for
   * indicating that a long-running operation is in progress.
   *
   * @param message - The message to display next to the spinner
   * @param type - Optional spinner type (default, install, create, build, deploy)
   */
  spin(message: string, type: keyof typeof spinnerTypes = 'default'): void {
    if (this.useNanoSpinner) {
      if (this.nanoSpinner) {
        this.nanoSpinner.stop();
      }
      this.nanoSpinner = createSpinner(text.body(message)).start();
      return;
    }

    const spinnerConfig = spinnerTypes[type];

    if (this.spinner) {
      this.spinner.text = message;
      // Update spinner type if different
      this.spinner.spinner = spinnerConfig.spinner;
      this.spinner.color = 'cyan';
    } else {
      const options: OraOptions = {
        text: message,
        spinner: spinnerConfig.spinner,
        color: 'cyan', // Using named color as ora doesn't support hex directly
      };

      this.spinner = ora(options).start();
    }
  }

  /**
   * Stops the active spinner without any success or error indication.
   *
   * This method stops the spinner if one is active and clears the reference
   * to it. This is useful when you want to stop showing progress without
   * indicating success or failure.
   */
  stopSpinner(): void {
    if (this.spinner) {
      this.spinner.stop();
      this.spinner = null;
    }

    if (this.nanoSpinner) {
      this.nanoSpinner.stop();
      this.nanoSpinner = null;
    }
  }

  /**
   * Stops the spinner with an error indication and message.
   *
   * This method stops the spinner with a styled error icon and the provided error message
   * if a spinner is active. If no spinner is active, it falls back to the regular
   * error logging method.
   *
   * @param message - The error message to display
   */
  spinnerError(message: string): void {
    if (this.useNanoSpinner && this.nanoSpinner) {
      this.nanoSpinner.error({ text: chalk.hex(colors.status.error)(message) });
      this.nanoSpinner = null;
      return;
    }

    if (this.spinner) {
      this.spinner.fail(chalk.hex(colors.status.error)(message));
      this.spinner = null;
    } else {
      this.error(message);
    }
  }

  /**
   * Stops the spinner with a success indication and message.
   *
   * This method stops the spinner with a styled success icon and the provided success message
   * if a spinner is active. If no spinner is active, it falls back to the regular
   * success logging method.
   *
   * @param message - The success message to display
   */
  spinnerSuccess(message: string): void {
    if (this.useNanoSpinner && this.nanoSpinner) {
      this.nanoSpinner.success({ text: chalk.hex(colors.status.success)(message) });
      this.nanoSpinner = null;
      return;
    }

    if (this.spinner) {
      this.spinner.succeed(chalk.hex(colors.status.success)(message));
      this.spinner = null;
    } else {
      this.success(message);
    }
  }

  /**
   * Logs a debug message to the console if debug mode is enabled.
   *
   * This method logs detailed information that is useful for debugging
   * but would be too verbose for normal operation. Debug messages are
   * only shown if the DEBUG environment variable is set to 'true' or '1'.
   *
   * @param message - The debug message to log
   */
  debug(message: string): void {
    if (!this.debugEnabled) return;

    if (this.spinner) {
      this.spinner.stop();
    }

    if (this.nanoSpinner) {
      this.nanoSpinner.stop();
      this.nanoSpinner = null;
    }

    console.log(chalk.gray('🔍'), chalk.gray(message));

    if (this.spinner) {
      this.spinner.start();
    }
  }

  /**
   * Logs a section header to create visual separation between different parts of the output.
   *
   * @param title - The section title to display
   */
  section(title: string): void {
    if (this.spinner) {
      this.spinner.stop();
    }

    if (this.nanoSpinner) {
      this.nanoSpinner.stop();
      this.nanoSpinner = null;
    }

    console.log('\n' + text.sectionTitle(`■ ${title.toUpperCase()} `) + '\n');

    if (this.spinner) {
      this.spinner.start();
    }
  }

  /**
   * Logs a step in a multi-step process.
   *
   * @param step - The step number
   * @param total - The total number of steps
   * @param message - The step description
   */
  step(step: number, total: number, message: string): void {
    if (this.spinner) {
      this.spinner.stop();
    }

    if (this.nanoSpinner) {
      this.nanoSpinner.stop();
      this.nanoSpinner = null;
    }

    const stepIndicator = chalk.hex(colors.accent.yellow)(`[${step}/${total}]`);
    console.log(`${stepIndicator} ${icons.arrow} ${text.body(message)}`);

    if (this.spinner) {
      this.spinner.start();
    }
  }

  /**
   * Logs a note or tip with subtle styling.
   *
   * @param message - The note message to display
   */
  note(message: string): void {
    if (this.spinner) {
      this.spinner.stop();
    }

    if (this.nanoSpinner) {
      this.nanoSpinner.stop();
      this.nanoSpinner = null;
    }

    console.log(`${icons.lightbulb} ${text.note(message)}`);

    if (this.spinner) {
      this.spinner.start();
    }
  }
}

export const logger = new Logger();
