/**
 * @file Command Execution Utilities
 *
 * This file provides utilities for executing shell commands in a cross-platform way.
 * It wraps the execa library to provide a simpler interface and consistent error
 * handling for command execution throughout the ezWorkflow CLI.
 */

import { execa } from 'execa';
import type { Options, ResultPromise } from 'execa';
import { logger } from './logger';

/**
 * Executes a shell command with the given arguments and options.
 *
 * This function runs a command with the specified arguments and captures its output.
 * The command's stdout and stderr are piped and not displayed to the user.
 * If the command fails, an error is logged and then re-thrown.
 *
 * @param command - The command to execute (e.g., 'npm', 'git')
 * @param args - Array of command arguments
 * @param options - Additional options to pass to execa
 * @param timeoutMs - Optional timeout in milliseconds (default: 5 minutes)
 * @returns Promise that resolves to the command's result
 * @throws Error if the command fails or times out
 */
export const execCommand = async (
  command: string,
  args: string[] = [],
  options: Options = {},
  timeoutMs: number = 5 * 60 * 1000 // Default timeout: 5 minutes
): Promise<any> => {
  // Create a timeout ID and promise for handling command timeout
  let timeoutId: NodeJS.Timeout | undefined;

  // Create a promise that will reject after the timeout
  const timeoutPromise = new Promise<never>((_, reject) => {
    timeoutId = setTimeout(() => {
      reject(new Error(`Command timed out after ${timeoutMs}ms: ${command} ${args.join(' ')}`));
    }, timeoutMs);
  });

  try {
    // Log the command being executed for debugging
    logger.debug(`Executing command: ${command} ${args.join(' ')}`);

    // Start the command execution
    const execaPromise = execa(command, args, {
      stdio: 'pipe',
      ...options,
    });

    // Add event listeners to log output for debugging
    if (execaPromise.stdout) {
      execaPromise.stdout.on('data', (data: any) => {
        logger.debug(`[stdout] ${data.toString().trim()}`);
      });
    }

    if (execaPromise.stderr) {
      execaPromise.stderr.on('data', (data: any) => {
        logger.debug(`[stderr] ${data.toString().trim()}`);
      });
    }

    // Race between the command execution and the timeout
    const result = await Promise.race([execaPromise, timeoutPromise]);

    // Clear the timeout since the command completed successfully
    clearTimeout(timeoutId);

    return result;
  } catch (error) {
    // Clear the timeout to prevent memory leaks
    if (timeoutId) {
      clearTimeout(timeoutId);
    }

    if (error instanceof Error) {
      if (error.message.includes('timed out')) {
        logger.error(`Command timed out: ${command} ${args.join(' ')}`);

        // Try to kill the process if it's still running
        try {
          // This is a simplified approach - in a real implementation, you'd need to
          // track the child process and kill it properly
          logger.warn('Attempting to continue despite timeout...');
        } catch (killError) {
          logger.error('Failed to kill timed out process');
        }
      } else {
        logger.error(`Command failed: ${command} ${args.join(' ')}`);
        logger.error(error.message);
      }
    }
    throw error;
  }
};

/**
 * Executes a shell command with the given arguments and streams its output.
 *
 * This function runs a command with the specified arguments and displays its
 * output directly to the user's console in real-time. This is useful for
 * long-running commands where the user should see progress.
 * If the command fails, an error is logged and then re-thrown.
 *
 * @param command - The command to execute (e.g., 'npm', 'git')
 * @param args - Array of command arguments
 * @param options - Additional options to pass to execa
 * @returns Promise that resolves to the command's result
 * @throws Error if the command fails
 */
export const execCommandWithOutput = async (
  command: string,
  args: string[] = [],
  options: Options = {}
): Promise<any> => {
  try {
    // If stdio is set to 'ignore', we need to handle the case where the command
    // might be waiting for input. We'll set a default timeout of 5 minutes.
    if (options.stdio === 'ignore') {
      const execaProcess = execa(command, args, options);

      // Set a timeout to kill the process if it takes too long
      const timeoutMs = 5 * 60 * 1000; // 5 minutes
      let timeoutId: NodeJS.Timeout | undefined;

      const timeoutPromise = new Promise<never>((_, reject) => {
        timeoutId = setTimeout(() => {
          // Try to kill the process if it's still running
          try {
            if (execaProcess.pid) {
              process.kill(execaProcess.pid);
            }
          } catch (killError) {
            // Ignore kill errors
          }
          reject(new Error(`Command timed out after ${timeoutMs}ms: ${command} ${args.join(' ')}`));
        }, timeoutMs);
      });

      // Ensure the timeout is cleared if the process completes before the timeout
      execaProcess.finally(() => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
      });

      // Race between the command execution and the timeout
      return await Promise.race([execaProcess, timeoutPromise]);
    } else {
      // Normal execution with output streaming
      const result = await execa(command, args, {
        stdio: 'inherit',
        ...options,
      });
      return result;
    }
  } catch (error) {
    if (error instanceof Error) {
      logger.error(`Command failed: ${command} ${args.join(' ')}`);
      logger.error(error.message);
    }
    throw error;
  }
};

/**
 * Checks if a command exists in the system's PATH.
 *
 * This function uses the 'which' command to determine if a given command
 * is available in the system's PATH. This is useful for checking if a
 * particular tool or package manager is installed before trying to use it.
 *
 * @param command - The command to check for existence
 * @returns Promise that resolves to true if the command exists, false otherwise
 */
export const commandExists = async (command: string): Promise<boolean> => {
  try {
    await execCommand('which', [command], { stdio: 'ignore' });
    return true;
  } catch (error) {
    return false;
  }
};

/**
 * Detects the best available package manager to use.
 *
 * This function checks for the existence of various package managers
 * in the following order of preference: bun, pnpm, yarn, npm.
 * It returns the first one found, defaulting to npm if none of the
 * preferred options are available.
 *
 * @returns Promise that resolves to the name of the package manager to use
 */
export const getPackageManager = async (): Promise<string> => {
  if (await commandExists('bun')) {
    return 'bun';
  }
  if (await commandExists('pnpm')) {
    return 'pnpm';
  }
  if (await commandExists('yarn')) {
    return 'yarn';
  }
  return 'npm';
};
