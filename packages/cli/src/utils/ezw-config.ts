/**
 * @file ezWorkflow Configuration Utilities
 *
 * This file provides utilities for managing the ezWorkflow configuration (ezw.json).
 * It handles creating, reading, and updating the configuration file, as well as
 * setting up the ezWorkflow directory structure and integrating with Hono.js applications.
 */

import { join } from 'path';
import { readFile, writeFile, ensureDirectory, fileExists } from './file';
import { HonoRuntimeTemplate } from './prompt';
import { getRuntimeImplementation } from '../runtimes';
import { AuthorInfo } from '../runtimes/types';
import ezConfig from '../config';

// Import types and functions from the new project-config package
import type { EzwConfig } from '@ezworkflow/project-config';
import { getConfig as getConfigFromFile } from '@ezworkflow/project-config';

// Re-export types for backward compatibility
export type { EzwConfig, AuthorInfo, HonoRuntimeTemplate };

// Using directoryExists and ensureDirectory from file.ts

/**
 * Gets the index file path for a specific Hono.js runtime.
 *
 * Each Hono.js runtime has a different expected location for its main index file.
 * This function delegates to the appropriate runtime implementation to get the
 * correct path for the specified runtime.
 *
 * @param runtime - The Hono.js runtime template identifier
 * @returns The expected index file path for the runtime (relative to project root)
 */
export function getIndexPathForRuntime(runtime: HonoRuntimeTemplate): string {
  return getRuntimeImplementation(runtime).getIndexPath();
}

/**
 * Finds the main index file in a Hono.js project.
 *
 * This function attempts to locate the main entry point for a Hono.js application.
 * If a runtime is specified, it uses the known path for that runtime.
 * Otherwise, it tries all known runtime paths until it finds an existing file.
 *
 * This is a critical function for setting up ezWorkflow in an existing project,
 * as it needs to find the main application file to add the ezWorkflow middleware.
 *
 * @param projectDir - The project directory (absolute path)
 * @param runtime - Optional runtime template to use for finding the index file
 * @returns The path to the index file (relative to project root)
 * @throws Error if no index file is found after checking all known locations
 */
export async function findIndexFile(projectDir: string, runtime?: HonoRuntimeTemplate): Promise<string> {
  // If runtime is provided, use the known index path
  if (runtime) {
    const indexPath = getIndexPathForRuntime(runtime);
    const fullPath = join(projectDir, indexPath);

    // Verify the file exists
    if (await fileExists(fullPath)) {
      return indexPath;
    }

    // File doesn't exist, but we know where it should be
    // This is likely because the project was just created
    return indexPath;
  }

  // If no runtime is provided, try to detect the index file
  // Get all possible runtime implementations
  const runtimes: HonoRuntimeTemplate[] = [
    'aws-lambda', 'bun', 'cloudflare-workers', 'deno', 'fastly',
    'lambda-edge', 'netlify', 'nextjs', 'nodejs', 'vercel', 'x-basic'
  ];

  // Check each runtime's index path
  for (const rt of runtimes) {
    const path = getRuntimeImplementation(rt).getIndexPath();
    const fullPath = join(projectDir, path);
    if (await fileExists(fullPath)) {
      return path; // Return the relative path
    }
    // File doesn't exist, try next path
  }

  // No index file found, throw a specific error
  throw new Error(
    'No Hono.js index file found. ezWorkflow requires a backend Hono.js project. ' +
    'Please ensure you are running this command in a valid Hono.js project directory ' +
    'that contains one of the supported index file structures.'
  );
}

/**
 * Initializes the ezWorkflow configuration file (ezw.json).
 *
 * This function creates a new configuration file with appropriate paths and settings
 * based on the detected or specified Hono.js runtime. It determines the correct
 * directory structure and import paths for the runtime, and creates the configuration
 * file in the project root.
 *
 * This is typically called when setting up ezWorkflow in a project for the first time.
 *
 * @param projectDir - The project directory (absolute path)
 * @param runtime - Optional Hono.js runtime template to use for configuration
 * @param apiKey - Optional API key for authentication
 * @param metadata - Optional project metadata (author, categories, tags, appType)
 * @returns The created configuration object
 * @throws Error if the index file cannot be found
 */
export async function initializeEzwConfig(
  projectDir: string,
  runtime?: HonoRuntimeTemplate,
  apiKey?: string,
  metadata?: {
    author?: AuthorInfo;
    categories?: string[];
    tags?: string[];
    appType?: 'partner' | 'account';
  }
): Promise<EzwConfig> {
  // Find the index file (will throw if not found)
  const indexFilePath = await findIndexFile(projectDir, runtime);

  // Use the provided runtime or default to nodejs
  const runtimeType = runtime || 'nodejs';

  // Determine base directory and import path prefix based on the runtime
  const runtimeImpl = getRuntimeImplementation(runtimeType);
  const baseDir = runtimeImpl.getEzwDirPath();
  const importPathPrefix = runtimeImpl.getImportPathPrefix();

  // Create the configuration object
  const config: EzwConfig = {
    baseDir,
    importPathPrefix,
    indexFilePath,
    nodesDirPath: join(baseDir, 'nodes'),
    executorsDirPath: join(baseDir, 'executors'),
    triggersDirPath: join(baseDir, 'triggers'),
    uiDirPath: join(baseDir, 'ui'),
    runtime: runtimeType,
    version: ezConfig.version
  };

  // Add API key if provided
  if (apiKey) {
    config.apiKey = apiKey;
  }

  // Add metadata if provided
  if (metadata) {
    if (metadata.author) config.author = metadata.author;
    if (metadata.categories) config.categories = metadata.categories;
    if (metadata.tags) config.tags = metadata.tags;
    if (metadata.appType) config.appType = metadata.appType;
  }

  // Save the configuration
  const configPath = join(projectDir, 'ezw.json');
  await writeFile(configPath, JSON.stringify(config, null, 2));

  return config;
}

/**
 * Gets the ezWorkflow configuration from the ezw.json file.
 *
 * This function attempts to read the existing configuration file.
 * If the file doesn't exist, it initializes a new configuration.
 *
 * This is the main entry point for accessing the ezWorkflow configuration
 * and is used by most commands that need to know where files should be located.
 *
 * @param projectDir - The project directory (absolute path)
 * @param runtime - Optional Hono.js runtime template to use if creating a new configuration
 * @param apiKey - Optional API key for authentication if creating a new configuration
 * @param metadata - Optional project metadata if creating a new configuration
 * @returns The configuration object (either read from file or newly created)
 * @throws Error if the configuration cannot be read or created
 */
export async function getEzwConfig(
  projectDir: string,
  runtime?: HonoRuntimeTemplate,
  apiKey?: string,
  metadata?: {
    author?: AuthorInfo;
    categories?: string[];
    tags?: string[];
    appType?: 'partner' | 'account';
  }
): Promise<EzwConfig> {
  try {
    // Try to read existing config using the new package
    return getConfigFromFile(projectDir);
  } catch {
    // Config doesn't exist, create a new one
    return await initializeEzwConfig(projectDir, runtime, apiKey, metadata);
  }
}

/**
 * Creates the ezWorkflow directory structure and initial files.
 *
 * This function sets up the necessary directory structure for ezWorkflow:
 * - Base directory (e.g., src/ezw)
 * - Nodes directory
 * - Executors directory
 * - Triggers directory
 *
 * It also creates initial index files in each directory and a main index file
 * that exports everything from the subdirectories.
 *
 * @param projectDir - The project directory (absolute path)
 * @param config - The ezWorkflow configuration object
 * @returns Promise that resolves when all directories and files have been created
 */
export async function createEzWorkflowFiles(projectDir: string, config: EzwConfig): Promise<void> {
  // Create base directory
  const baseDirPath = join(projectDir, config.baseDir);
  await ensureDirectory(baseDirPath);

  // Create subdirectories
  const nodesDirPath = join(projectDir, config.nodesDirPath);
  const executorsDirPath = join(projectDir, config.executorsDirPath);
  const triggersDirPath = join(projectDir, config.triggersDirPath);
  const uiDirPath = config.uiDirPath ? join(projectDir, config.uiDirPath) : null;

  await ensureDirectory(nodesDirPath);
  await ensureDirectory(executorsDirPath);
  await ensureDirectory(triggersDirPath);
  if (uiDirPath) {
    await ensureDirectory(uiDirPath);
  }

  // Create index files
  const nodesIndexContent = `// Export your nodes here
export default {};
`;

  const executorsIndexContent = `// Export your executors here
export default {};
`;

  const triggersIndexContent = `// Export your triggers here
export default {};
`;

  const uiIndexContent = `// This file is auto-generated and will be updated when new UI components are created
// Export all UI components from this directory

export default {};
`;

  // Create ezworkflow.ts file that exports everything
  const ezworkflowContent = `import nodes from './nodes';
import executors from './executors';
import triggers from './triggers';
${uiDirPath ? "import ui from './ui';" : ''}

export {
  nodes,
  executors,
  triggers${uiDirPath ? ',\n  ui' : ''}
};
`;

  // Write the files
  await writeFile(join(nodesDirPath, 'index.ts'), nodesIndexContent);
  await writeFile(join(executorsDirPath, 'index.ts'), executorsIndexContent);
  await writeFile(join(triggersDirPath, 'index.ts'), triggersIndexContent);
  if (uiDirPath) {
    await writeFile(join(uiDirPath, 'index.ts'), uiIndexContent);
  }
  await writeFile(join(baseDirPath, 'index.ts'), ezworkflowContent);
}

/**
 * Adds ezWorkflow import statements to the file content.
 *
 * This function modifies the content of a file (typically the main index.ts)
 * to add the necessary import statements for ezWorkflow. It uses the runtime
 * implementation to get the correct import statements for the specific runtime.
 *
 * The function attempts to add the imports after the last existing import statement,
 * or at the beginning of the file if no imports are found.
 *
 * @param fileContent - The original file content as a string
 * @param runtime - The Hono.js runtime template (defaults to 'nodejs')
 * @returns The modified file content with imports added
 */
export function addImports(fileContent: string, runtime?: HonoRuntimeTemplate): string {
  // Get the runtime implementation
  const runtimeImpl = getRuntimeImplementation(runtime || 'nodejs');

  // Get the import statements from the runtime implementation
  const importStatements = runtimeImpl.getImportStatements();

  // Add our imports after the last import statement
  const lastImportIndex = fileContent.lastIndexOf('import ');
  if (lastImportIndex === -1) {
    // No imports found, add at the beginning
    return importStatements + '\n\n' + fileContent;
  }

  const lastImportLineEnd = fileContent.indexOf('\n', lastImportIndex);
  if (lastImportLineEnd === -1) {
    // Import is on the last line, add after it
    return fileContent + '\n\n' + importStatements;
  }

  return fileContent.substring(0, lastImportLineEnd + 1) +
         '\n' + importStatements +
         fileContent.substring(lastImportLineEnd + 1);
}

/**
 * Adds ezWorkflow middleware code after the Hono app initialization.
 *
 * This function modifies the content of a file to add the ezWorkflow middleware
 * to the Hono application. It looks for common patterns of Hono initialization
 * and adds the middleware code after it.
 *
 * The middleware code registers the ezWorkflow server at the '/api/ezw' route,
 * providing access to the nodes, executors, and triggers.
 *
 * @param fileContent - The file content with imports already added
 * @param config - The ezWorkflow configuration object
 * @returns The modified file content with middleware code added
 */
export function addCodeAfterAppInit(fileContent: string, config: EzwConfig): string {
  // Handle different patterns of Hono initialization
  const appInitPatterns = [
    /const app = new Hono\(\)/,
    /const app = new Hono\<.*?\>\(\)/,
    /const app = new Hono\(\)\.basePath\(.*?\)/
  ];

  let position = -1;

  for (const pattern of appInitPatterns) {
    const match = fileContent.match(pattern);
    if (match) {
      position = fileContent.indexOf(match[0]) + match[0].length;
      break;
    }
  }

  if (position === -1) return fileContent; // No match found

  // Find the end of the line
  const lineEnd = fileContent.indexOf('\n', position);
  if (lineEnd === -1) {
    position = fileContent.length;
  } else {
    position = lineEnd;
  }

  // Build server options object with all available metadata
  const serverOptions = {
    apiKey: config.apiKey || '',
    ...(config.author ? { author: config.author } : {}),
    ...(config.categories ? { categories: config.categories } : {}),
    ...(config.tags ? { tags: config.tags } : {}),
    ...(config.appType ? { appType: config.appType } : {}),
    version: config.version
  };

  // Convert to JSON string and format for code insertion
  const serverOptionsStr = JSON.stringify(serverOptions, null, 2)
    .replace(/\n/g, '\n  ') // Proper indentation
    .replace(/"/g, "'");    // Use single quotes for JS

  const middlewareCode = `

app.route('/api/ezw', getServer(${serverOptionsStr}));`;

  return fileContent.substring(0, position) + middlewareCode + fileContent.substring(position);
}

/**
 * Modifies the main index file to add ezWorkflow integration.
 *
 * This function reads the main index file, adds the necessary imports and
 * middleware code, and writes the modified content back to the file.
 *
 * This is a critical step in setting up ezWorkflow in a project, as it
 * integrates the ezWorkflow server with the existing Hono application.
 *
 * @param projectDir - The project directory (absolute path)
 * @param config - The ezWorkflow configuration object
 * @returns True if the modification was successful, false otherwise
 */
export async function modifyIndexFile(projectDir: string, config: EzwConfig): Promise<boolean> {
  const indexPath = join(projectDir, config.indexFilePath);

  try {
    let indexContent = await readFile(indexPath);
    indexContent = addImports(indexContent, config.runtime);
    indexContent = addCodeAfterAppInit(indexContent, config);
    await writeFile(indexPath, indexContent);
    return true;
  } catch (error) {
    console.error('Error modifying index file:', error);
    return false;
  }
}

/**
 * Sets up ezWorkflow in a Hono.js project.
 *
 * This function performs the complete setup process for ezWorkflow:
 * 1. Initializes or gets the configuration
 * 2. Updates the configuration with an API key and metadata if provided
 * 3. Creates the ezWorkflow directory structure and files
 * 4. Modifies the main index file to integrate ezWorkflow
 *
 * This is the main entry point for setting up ezWorkflow in a project
 * and is typically called by the 'create project' command.
 *
 * @param projectDir - The project directory (absolute path)
 * @param runtime - Optional Hono.js runtime template to use for configuration
 * @param apiKey - Optional API key for authentication
 * @param metadata - Optional project metadata (author, categories, tags, appType)
 * @returns True if the setup was successful, false if there were non-critical errors
 * @throws Error if no Hono.js index file is found (critical error)
 */
export async function setupEzWorkflow(
  projectDir: string,
  runtime?: HonoRuntimeTemplate,
  apiKey?: string,
  metadata?: {
    author?: AuthorInfo;
    categories?: string[];
    tags?: string[];
    appType?: 'partner' | 'account';
  }
): Promise<boolean> {
  try {
    // Initialize or get configuration
    // This will throw an error if no Hono.js index file is found
    let config = await getEzwConfig(projectDir, runtime);

    // Update config with API key and metadata if provided
    let configUpdated = false;

    if (apiKey) {
      config.apiKey = apiKey;
      configUpdated = true;
    }

    if (metadata) {
      if (metadata.author) config.author = metadata.author;
      if (metadata.categories) config.categories = metadata.categories;
      if (metadata.tags) config.tags = metadata.tags;
      if (metadata.appType) config.appType = metadata.appType;
      configUpdated = true;
    }

    // Save the updated configuration if changes were made
    if (configUpdated) {
      const configPath = join(projectDir, 'ezw.json');
      await writeFile(configPath, JSON.stringify(config, null, 2));
    }

    // Create ezWorkflow files
    // await createEzWorkflowFiles(projectDir, config);

    // Modify the index file
    const success = await modifyIndexFile(projectDir, config);
    if (!success) {
      console.warn('Could not automatically configure ezWorkflow. You will need to manually add it to your Hono app.');
      return false;
    }

    return true;
  } catch (error) {
    // Re-throw the error if it's about not finding a Hono.js index file
    if (error instanceof Error && error.message.includes('No Hono.js index file found')) {
      throw error;
    }

    console.error('Error setting up ezWorkflow:', error);
    return false;
  }
}
