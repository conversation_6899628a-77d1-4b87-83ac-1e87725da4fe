/**
 * @file File System Utilities
 *
 * This file provides utility functions for common file system operations.
 * It wraps the fs-extra library to provide a simpler interface and consistent
 * error handling for file operations used throughout the ezWorkflow CLI.
 */

import fs from 'fs-extra';

/**
 * Checks if a file exists at the specified path.
 *
 * This function attempts to access the file and returns a boolean
 * indicating whether the file exists and is a regular file (not a directory).
 *
 * @param filePath - Path to the file to check
 * @returns Promise that resolves to true if the file exists, false otherwise
 */
export const fileExists = async (filePath: string): Promise<boolean> => {
  try {
    const stats = await fs.stat(filePath);
    return stats.isFile();
  } catch (error) {
    return false;
  }
};

/**
 * Checks if a directory exists at the specified path.
 *
 * This function attempts to access the directory and returns a boolean
 * indicating whether the directory exists and is a directory (not a file).
 *
 * @param dirPath - Path to the directory to check
 * @returns Promise that resolves to true if the directory exists, false otherwise
 */
export const directoryExists = async (dirPath: string): Promise<boolean> => {
  try {
    const stats = await fs.stat(dirPath);
    return stats.isDirectory();
  } catch (error) {
    return false;
  }
};

/**
 * Ensures a directory exists, creating it if necessary.
 *
 * This function creates a directory at the specified path if it doesn't already exist.
 * It also creates any parent directories that don't exist (like mkdir -p).
 *
 * @param dirPath - Path to the directory to ensure exists
 * @returns Promise that resolves when the directory has been created or verified
 */
export const ensureDirectory = async (dirPath: string): Promise<void> => {
  await fs.ensureDir(dirPath);
};

/**
 * Writes content to a file, creating the file if it doesn't exist.
 *
 * This function writes the specified content to a file at the given path.
 * If the file already exists, it will be overwritten.
 * If the directory containing the file doesn't exist, an error will be thrown.
 *
 * @param filePath - Path to the file to write
 * @param content - Content to write to the file
 * @returns Promise that resolves when the file has been written
 */
export const writeFile = async (filePath: string, content: string): Promise<void> => {
  await fs.writeFile(filePath, content);
};

/**
 * Reads the content of a file as a string.
 *
 * This function reads the file at the specified path and returns its content as a string.
 * If the file doesn't exist or can't be read, an error will be thrown.
 *
 * @param filePath - Path to the file to read
 * @returns Promise that resolves to the content of the file as a string
 * @throws Error if the file doesn't exist or can't be read
 */
export const readFile = async (filePath: string): Promise<string> => {
  return fs.readFile(filePath, 'utf-8');
};

/**
 * Reads and parses a JSON file.
 *
 * This function reads the file at the specified path and parses it as JSON.
 * If the file doesn't exist or contains invalid JSON, an error will be thrown.
 *
 * @param filePath - Path to the JSON file to read
 * @returns Promise that resolves to the parsed JSON content
 * @throws Error if the file doesn't exist or contains invalid JSON
 */
export const readJsonFile = async (filePath: string): Promise<any> => {
  return fs.readJson(filePath);
};

/**
 * Writes data to a file as formatted JSON.
 *
 * This function serializes the provided data to JSON and writes it to a file.
 * The JSON is formatted with 2-space indentation for readability.
 * If the file already exists, it will be overwritten.
 *
 * @param filePath - Path to the file to write
 * @param content - Data to serialize as JSON and write to the file
 * @returns Promise that resolves when the file has been written
 */
export const writeJsonFile = async (filePath: string, content: any): Promise<void> => {
  await fs.writeJson(filePath, content, { spaces: 2 });
};

/**
 * Copies a file or directory from one location to another.
 *
 * This function copies a file or directory, including subdirectories and files.
 * If the destination already exists, it will be overwritten.
 * If the source doesn't exist, an error will be thrown.
 *
 * @param sourcePath - Path to the source file or directory
 * @param destPath - Path to the destination
 * @returns Promise that resolves when the copy operation is complete
 * @throws Error if the source doesn't exist or the copy operation fails
 */
export const copyFile = async (sourcePath: string, destPath: string): Promise<void> => {
  await fs.copy(sourcePath, destPath);
};
