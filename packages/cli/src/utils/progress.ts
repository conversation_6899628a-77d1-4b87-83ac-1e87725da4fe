/**
 * @file Progress Bar Utility
 *
 * This file provides utilities for creating and managing progress bars
 * in the CLI. It uses cli-progress to create visually appealing progress
 * indicators for long-running operations.
 */

import cliProgress from 'cli-progress';
import chalk from 'chalk';
import { colors } from './theme';

/**
 * Options for creating a progress bar
 */
export interface ProgressBarOptions {
  /** The total number of steps in the progress bar */
  total: number;
  /** The initial value of the progress bar (default: 0) */
  startValue?: number;
  /** The format string for the progress bar (default: a predefined format) */
  format?: string;
  /** Whether to clear the progress bar when complete (default: false) */
  clearOnComplete?: boolean;
  /** Whether to stop the process when the progress bar completes (default: false) */
  stopOnComplete?: boolean;
  /** Whether to hide the progress bar (default: false) */
  hideCursor?: boolean;
}

/**
 * Creates a single progress bar with the given options
 * @param options - Options for the progress bar
 * @returns A progress bar instance
 */
export const createProgressBar = (options: ProgressBarOptions): cliProgress.SingleBar => {
  const {
    total,
    startValue = 0,
    clearOnComplete = false,
    stopOnComplete = true,
    hideCursor = true,
  } = options;

  // Define a custom format if none is provided
  const format = options.format ||
    `${chalk.hex(colors.primary.main)('{bar}')} ${chalk.hex(colors.accent.blue)('{percentage}%')} | ` +
    `${chalk.hex(colors.neutral.white)('{value}/{total}')} | ` +
    `${chalk.hex(colors.neutral.lightGray)('ETA: {eta_formatted}')}`;

  // Create the progress bar with the specified options
  const progressBar = new cliProgress.SingleBar({
    format,
    barCompleteChar: '█',
    barIncompleteChar: '░',
    hideCursor,
    clearOnComplete,
    stopOnComplete,
    etaBuffer: 10,
    etaAsynchronousUpdate: true,
    noTTYOutput: false,
    emptyOnZero: true,
    forceRedraw: true,
  }, cliProgress.Presets.shades_classic);

  // Start the progress bar with the initial value
  progressBar.start(total, startValue);

  return progressBar;
};

/**
 * Creates a multi-progress bar with the given options
 * @param options - Options for the progress bar
 * @returns A multi-progress bar instance
 */
export const createMultiProgressBar = (options: ProgressBarOptions): cliProgress.MultiBar => {
  const {
    clearOnComplete = false,
    hideCursor = true,
  } = options;

  // Create the multi-progress bar with the specified options
  const multiBar = new cliProgress.MultiBar({
    clearOnComplete,
    hideCursor,
    format: options.format,
    barCompleteChar: '█',
    barIncompleteChar: '░',
    forceRedraw: true,
    noTTYOutput: false,
    emptyOnZero: true,
  }, cliProgress.Presets.shades_classic);

  return multiBar;
};

/**
 * Creates a payload for a progress bar update
 * @param value - The current value of the progress bar
 * @param payload - Additional payload data to include in the update
 * @returns The combined payload for the update
 */
export const createProgressPayload = (
  value: number,
  payload: Record<string, any> = {}
): Record<string, any> => {
  return {
    value,
    ...payload,
  };
};

export default {
  createProgressBar,
  createMultiProgressBar,
  createProgressPayload,
};
