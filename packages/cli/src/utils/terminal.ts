/**
 * @file Terminal Utility
 *
 * This file provides utilities for detecting terminal capabilities
 * and adapting the CLI's appearance based on the terminal environment.
 */

import chalk, { type Chalk } from 'chalk';

/**
 * Terminal theme type
 */
export type TerminalTheme = 'dark' | 'light' | 'unknown';

/**
 * Attempts to detect if the terminal is using a light or dark theme.
 *
 * This is a best-effort detection that uses various environment variables
 * and terminal capabilities to guess the theme. It's not 100% accurate
 * but provides a reasonable default for most common terminal setups.
 *
 * @returns The detected terminal theme ('dark', 'light', or 'unknown')
 */
export const detectTerminalTheme = (): TerminalTheme => {
  // Check if we're in a terminal at all
  if (!process.stdout.isTTY) {
    return 'unknown';
  }

  // Check for known environment variables that indicate theme
  const termProgram = process.env.TERM_PROGRAM;
  const colorTerm = process.env.COLORTERM;
  const termTheme = process.env.TERM_THEME;
  const colorfgbg = process.env.COLORFGBG;

  // Direct theme indication (some terminals set this)
  if (termTheme) {
    if (termTheme.toLowerCase().includes('dark') || termTheme.toLowerCase().includes('black')) {
      return 'dark';
    }
    if (termTheme.toLowerCase().includes('light') || termTheme.toLowerCase().includes('white')) {
      return 'light';
    }
  }

  // Check COLORFGBG environment variable (set by some terminals)
  // Format is typically foreground;background
  if (colorfgbg) {
    const colors = colorfgbg.split(';');
    if (colors.length >= 2) {
      const bg = parseInt(colors[1], 10);
      // If background color code is low, it's likely a dark theme
      if (bg < 8) {
        return 'dark';
      }
      // If background color code is high, it's likely a light theme
      if (bg > 8) {
        return 'light';
      }
    }
  }

  // Check for specific terminal programs
  if (termProgram) {
    // macOS Terminal.app defaults to light theme
    if (termProgram === 'Apple_Terminal') {
      return 'light';
    }

    // VS Code integrated terminal - can't reliably detect, but more often dark
    if (termProgram === 'vscode') {
      return 'dark';
    }

    // iTerm2 is often configured with dark themes
    if (termProgram === 'iTerm.app') {
      return 'dark';
    }
  }

  // Most modern terminals default to dark themes
  if (colorTerm === 'truecolor' || colorTerm === '24bit') {
    return 'dark';
  }

  // Default to dark as it's the most common terminal theme these days
  return 'dark';
};

/**
 * Determines if the terminal supports true color (16 million colors).
 *
 * @returns True if the terminal supports true color, false otherwise
 */
export const supportsTrueColor = (): boolean => {
  const colorTerm = process.env.COLORTERM;
  return colorTerm === 'truecolor' || colorTerm === '24bit';
};

/**
 * Gets the appropriate color for the current terminal theme.
 *
 * @param darkThemeColor - The color to use in dark themes
 * @param lightThemeColor - The color to use in light themes
 * @param fallbackColor - The fallback color to use if theme detection fails
 * @returns The appropriate color for the current terminal theme
 */
export const getThemeAwareColor = (
  darkThemeColor: string,
  lightThemeColor: string,
  fallbackColor?: string
): string => {
  const theme = detectTerminalTheme();

  if (theme === 'dark') {
    return darkThemeColor;
  }

  if (theme === 'light') {
    return lightThemeColor;
  }

  return fallbackColor || darkThemeColor;
};

/**
 * Creates a theme-aware chalk instance that uses appropriate colors
 * based on the detected terminal theme.
 *
 * @param darkThemeColor - The color to use in dark themes
 * @param lightThemeColor - The color to use in light themes
 * @param fallbackColor - The fallback color to use if theme detection fails
 * @returns A chalk instance with the appropriate color
 */
export const themeAwareChalk = (
  darkThemeColor: string,
  lightThemeColor: string,
  fallbackColor?: string
): typeof chalk => {
  const color = getThemeAwareColor(darkThemeColor, lightThemeColor, fallbackColor);
  return chalk.hex(color);
};

export default {
  detectTerminalTheme,
  supportsTrueColor,
  getThemeAwareColor,
  themeAwareChalk,
};
