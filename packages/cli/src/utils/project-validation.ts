/**
 * @file Project Validation Utilities
 *
 * This file provides validation functions for checking if a directory
 * is a valid ezWorkflow project and other project-related validations.
 */

import { existsSync } from 'fs';
import { join } from 'path';
import { logger } from './logger';
import { getEzwConfig } from './ezw-config';

/**
 * Validates if the current directory is a valid ezWorkflow project.
 * This function checks for the presence of essential project files and structure.
 * 
 * @param cwd - The current working directory to validate
 * @returns Promise<boolean> - True if the directory is a valid project, false otherwise
 */
export const validateProjectDirectory = async (cwd: string): Promise<boolean> => {
  try {
    // Check for ezw.json configuration file
    const configPath = join(cwd, 'ezw.json');
    if (!existsSync(configPath)) {
      logger.error('This directory is not an ezWorkflow project.');
      logger.info('Please run "ezw create project" first to create a new project.');
      return false;
    }

    // Try to read the configuration to validate it's properly structured
    await getEzwConfig(cwd);
    return true;
  } catch (error) {
    logger.error('This directory is not a valid ezWorkflow project.');
    logger.info('Please run "ezw create project" first to create a new project.');
    return false;
  }
}; 