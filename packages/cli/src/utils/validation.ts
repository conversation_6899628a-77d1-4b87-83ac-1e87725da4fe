/**
 * @file Validation Utilities
 *
 * This file provides validation functions for user input and other data.
 * These functions help ensure that user-provided values meet the requirements
 * for creating valid nodes, triggers, and other components.
 */

import { logger } from './logger';

/**
 * Validates a name for nodes, triggers, and other components.
 *
 * This function checks if a name meets the following criteria:
 * - Not empty
 * - Contains only lowercase letters, numbers, and hyphens
 * - Does not start or end with a hyphen
 *
 * If the name is valid, it returns the normalized version (lowercase)
 * for consistent file naming. If invalid, it logs an error and returns false.
 *
 * @param name - The name to validate
 * @returns The normalized name (lowercase) if valid, or false if invalid
 */
export const validateName = (name: string): boolean | string => {
  if (!name) {
    logger.error('Name cannot be empty');
    return false;
  }

  // Convert to lowercase for validation but preserve original case
  const normalizedName = name.toLowerCase();

  // Check if the normalized name contains only allowed characters
  if (!/^[a-z0-9-]+$/.test(normalizedName)) {
    logger.error('Name can only contain letters, numbers, and hyphens');
    return false;
  }

  if (normalizedName.startsWith('-') || normalizedName.endsWith('-')) {
    logger.error('Name cannot start or end with a hyphen');
    return false;
  }

  // Return the normalized name (lowercase) for consistency in file naming
  return normalizedName;
};

/**
 * Converts a hyphenated name to a valid JavaScript identifier (camelCase).
 * 
 * This function takes a hyphenated name (e.g., "test-partner-modal") and converts it
 * to a valid JavaScript identifier (e.g., "testPartnerModal") for use in export statements.
 * 
 * @param name - The hyphenated name to convert
 * @returns The camelCase version of the name
 */
export const toCamelCase = (name: string): string => {
  return name.replace(/-([a-z])/g, (_, letter) => letter.toUpperCase());
};
