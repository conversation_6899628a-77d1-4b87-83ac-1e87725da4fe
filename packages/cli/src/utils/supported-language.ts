/**
 * List of supported languages with variants only where significant differences exist
 * Format: language code (ISO 639-1 or ISO 639-1-ISO 3166-1) as key, native language name as value
 */
const supportedLanguages = {
  // English - variants have significant spelling and vocabulary differences
  en: 'English',
//   'en-US': 'English (United States)',
//   'en-GB': 'English (United Kingdom)',

//   // Chinese - significant differences between simplified and traditional
//   zh: '中文',
//   'zh-CN': '中文 (简体)', // Simplified
//   'zh-TW': '中文 (繁體)', // Traditional
//   'zh-HK': '中文 (香港)', // Hong Kong variant (uses traditional characters with local terms)

//   // Japanese
//   ja: '日本語',

//   // Korean
//   ko: '한국어',

//   // French - Canadian French has significant differences
  fr: 'Français',
//   'fr-CA': 'Français (Canada)',

//   // German - Swiss German has some spelling differences
  de: 'Deutsch',
//   'de-CH': 'Deutsch (Schweiz)',

//   // Spanish - significant regional vocabulary differences
//   es: 'Español',
//   'es-ES': 'Español (España)',
//   'es-MX': 'Español (México)',
//   'es-AR': 'Español (Argentina)',

//   // Portuguese - Brazilian and European Portuguese have significant differences
//   pt: 'Português',
//   'pt-BR': 'Português (Brasil)',
//   'pt-PT': 'Português (Portugal)',

//   // Italian
//   it: 'Italiano',

//   // Russian
//   ru: 'Русский',

//   // Arabic - regional dialects can vary significantly
//   ar: 'العربية',
//   'ar-EG': 'العربية (مصر)', // Egyptian Arabic
//   'ar-SA': 'العربية (السعودية)', // Gulf Arabic
//   'ar-MA': 'العربية (المغرب)', // Maghrebi Arabic

//   // Hindi
//   hi: 'हिंदी',

//   // Bengali - Bangladesh and Indian variants have vocabulary and pronunciation differences
  'bn-BD': 'বাংলা (বাংলাদেশ)',
//   'bn-IN': 'বাংলা (ভারত)',

//   // Punjabi - India uses Gurmukhi script, Pakistan uses Shahmukhi (Arabic) script
//   'pa-IN': 'ਪੰਜਾਬੀ (ਭਾਰਤ)',
//   'pa-PK': 'پنجابی (پاکستان)',

//   // Tamil
//   ta: 'தமிழ்',

//   // Telugu
//   te: 'తెలుగు',

//   // Malayalam
//   ml: 'മലയാളം',

//   // Dutch - Belgian Dutch (Flemish) has significant differences
  nl: 'Nederlands',
//   'nl-BE': 'Vlaams', // Flemish - the common name for Belgian Dutch

//   // Polish
//   pl: 'Polski',

//   // Romanian - Moldovan has some vocabulary and pronunciation differences
//   ro: 'Română',
//   'ro-MD': 'Moldovenească', // Common name for Moldovan Romanian

//   // Swedish
//   sv: 'Svenska',

//   // Turkish
//   tr: 'Türkçe',

//   // Ukrainian
//   uk: 'Українська',

//   // Vietnamese
//   vi: 'Tiếng Việt',

//   // Norwegian - has two official written standards (Bokmål and Nynorsk)
//   no: 'Norsk', // Generic Norwegian (typically defaults to Bokmål)
//   nb: 'Norsk Bokmål', // Used by majority (~85%)
//   nn: 'Norsk Nynorsk', // Used by minority (~15%)

//   // Finnish
//   fi: 'Suomi',

//   // Danish
//   da: 'Dansk',

//   // Greek
//   el: 'Ελληνικά',

//   // Czech
//   cs: 'Čeština',

//   // Hungarian
//   hu: 'Magyar',

//   // Thai
//   th: 'ไทย',

//   // Indonesian
//   id: 'Bahasa Indonesia',

//   // Malay
//   ms: 'Bahasa Melayu',

//   // Hebrew
//   he: 'עברית',

//   // Bulgarian
//   bg: 'Български',

//   // Catalan
//   ca: 'Català',

//   // Croatian
//   hr: 'Hrvatski',

//   // Estonian
//   et: 'Eesti',

//   // Latvian
//   lv: 'Latviešu',

//   // Lithuanian
//   lt: 'Lietuvių',

//   // Serbian - Cyrillic and Latin script variants
//   sr: 'Српски', // Cyrillic
//   'sr-Latn': 'Srpski', // Latin script

//   // Slovak
//   sk: 'Slovenčina',

//   // Slovenian
//   sl: 'Slovenščina',

//   // Urdu
  ur: 'اردو',

//   // Farsi/Persian
//   fa: 'فارسی',

//   // Gujarati
//   gu: 'ગુજરાતી',

//   // Kannada
//   kn: 'ಕನ್ನಡ',

//   // Marathi
//   mr: 'मराठी',

//   // Swahili
//   sw: 'Kiswahili',
};

export default supportedLanguages;