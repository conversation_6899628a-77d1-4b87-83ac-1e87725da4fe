/**
 * Type definitions for cli-welcome
 */

declare module 'cli-welcome' {
  interface WelcomeOptions {
    title?: string;
    tagLine?: string;
    description?: string;
    version?: string;
    bgColor?: string;
    color?: string;
    bold?: boolean;
    clear?: boolean;
    boxOptions?: {
      padding?: number;
      margin?: number;
      borderStyle?: string;
      borderColor?: string;
    };
  }

  function welcome(options: WelcomeOptions): void;
  export default welcome;
}
