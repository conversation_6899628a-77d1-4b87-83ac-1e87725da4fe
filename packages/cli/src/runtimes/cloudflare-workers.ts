/**
 * Cloudflare Workers runtime implementation
 */
import path from 'path';
import { readFile, writeFile, readJsonFile, writeJsonFile, ensureDirectory, logger } from '../utils';
import { BaseRuntime } from './base';
import { ProjectMetadata } from './types';
import config from '../config';

/**
 * Cloudflare Workers runtime class
 */
export class CloudflareWorkersRuntime extends BaseRuntime {
  /**
   * Constructor
   */
  constructor() {
    super('cloudflare-workers');
  }

  /**
   * Get the ezw directory path for the runtime
   * @returns The ezw directory path
   */
  getEzwDirPath(): string {
    return 'src/ezw';
  }

  /**
   * Get the index file path for the runtime
   * @returns The index file path
   */
  getIndexPath(): string {
    return 'src/index.ts';
  }

  /**
   * Get the import statements for the runtime
   * @returns The import statements
   */
  getImportStatements(): string {
    return `import { getServer } from "@ezworkflow/node-sdk";`;
  }

  /**
   * Get the README content for the runtime
   * @returns The README content specific to the runtime
   */
  getReadmeContent(): string {
    return `
# ezWorkflow Platform

## Overview
ezWorkflow is an enterprise-grade workflow automation platform that enables you to build, deploy, and manage complex workflows with ease. This project template provides a robust foundation for developing custom workflow nodes and triggers with Cloudflare Workers.

## Prerequisites
- Node.js 20.x or higher
- Wrangler CLI
- ezWorkflow Account
- Application credentials from the [ezWorkflow Marketplace](https://marketplace.ezworkflow.ai/my-app)

## Project Architecture

\`\`\`
├── src/
│   ├── index.ts          # Application entry point
│   └── ezw/
│       ├── nodes/        # Custom node implementations
│       ├── executors/    # Node execution handlers
│       ├── triggers/     # Custom trigger implementations
│       └── ui/           # UI components
├── wrangler.toml         # Cloudflare Workers configuration
└── package.json          # Project dependencies
\`\`\`

## Development Workflow

### Creating New Components
\`\`\`bash
# Create a new node with its executor
npx ezw create node <node-name>

# Create a new trigger with its handler
npx ezw create trigger <trigger-name>

# Create a new UI component
npx ezw create ui <ui-name>
\`\`\`

### Development Commands
\`\`\`bash
# Start development server
npm run dev

# Deploy to Cloudflare Workers
npm run deploy
\`\`\`

## API Reference

### Core Endpoints
- \`GET /\`: API documentation
- \`GET /nodes\`: List available nodes
- \`GET /nodes/:id\`: Retrieve node definition
- \`POST /api/nodes/:id/execute\`: Execute node
- \`POST /api/triggers/:id\`

## Configuration
1. Register your application in the [ezWorkflow Marketplace](https://marketplace.ezworkflow.ai/my-app)
2. Obtain your application credentials
3. Configure the credentials in your project settings

## Best Practices
- Follow the established project structure
- Implement proper error handling
- Add comprehensive documentation for custom nodes
- Test thoroughly before deployment

## Support
For support, contact your ezWorkflow account manager or visit our [Support Portal](https://support.ezworkflow.ai).

## License
ezWorkflow License - All rights reserved
`;
  }

  /**
   * Setup runtime-specific configuration
   * @param projectDir Project directory
   * @param metadata Project metadata
   */
  async setupRuntime(projectDir: string, metadata: ProjectMetadata): Promise<void> {
    logger.info('📦 Setting up Cloudflare Workers dependencies...');

    // Create the ezw directory structure
    const ezwDir = path.join(projectDir, 'src', 'ezw');
    await ensureDirectory(ezwDir);
    await ensureDirectory(path.join(ezwDir, 'nodes'));
    await ensureDirectory(path.join(ezwDir, 'executors'));
    await ensureDirectory(path.join(ezwDir, 'triggers'));
    await ensureDirectory(path.join(ezwDir, 'ui'));

    // Handle wrangler configuration
    await this.setupWranglerConfig(projectDir);

    // Update package.json with Cloudflare Workers-specific dependencies
    const packageJsonPath = path.join(projectDir, 'package.json');

    try {
      let packageJson = await readJsonFile(packageJsonPath);

      // Add dependencies without overwriting existing ones
      packageJson.dependencies = packageJson.dependencies || {};

      // Only add SDK if not already present
      if (!packageJson.dependencies['@ezworkflow/node-sdk']) {
        packageJson.dependencies['@ezworkflow/node-sdk'] = `^${config.version}`;
      }

      // Add Hono if not already present
      if (!packageJson.dependencies['@hono/hono']) {
        packageJson.dependencies['@hono/hono'] = '^3.0.0';
      }

      // Add Wrangler as dev dependency if not present
      if (!packageJson.devDependencies?.['wrangler']) {
        packageJson.devDependencies = packageJson.devDependencies || {};
        packageJson.devDependencies['wrangler'] = '^3.0.0';
      }

      // Add scripts for Cloudflare Workers if they don't exist
      packageJson.scripts = packageJson.scripts || {};

      // Only add dev script if not already defined
      if (!packageJson.scripts['dev']) {
        packageJson.scripts['dev'] = 'wrangler dev';
      }

      // Only add deploy script if not already defined
      if (!packageJson.scripts['deploy']) {
        packageJson.scripts['deploy'] = 'wrangler deploy';
      }

      await writeJsonFile(packageJsonPath, packageJson);
      logger.spinnerSuccess('Package.json updated with Cloudflare Workers dependencies!');
    } catch (error) {
      logger.warn(`Could not update package.json: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * Setup wrangler configuration with nodejs_compat flag
   * @param projectDir Project directory
   */
  private async setupWranglerConfig(projectDir: string): Promise<void> {
    // Try to find wrangler.toml first
    const wranglerTomlPath = path.join(projectDir, 'wrangler.toml');
    const wranglerJsoncPath = path.join(projectDir, 'wrangler.jsonc');

    try {
      // Try to read wrangler.toml first
      let configContent = '';
      let configPath = '';
      let isToml = true;

      try {
        configContent = await readFile(wranglerTomlPath);
        configPath = wranglerTomlPath;
      } catch (error) {
        // If wrangler.toml doesn't exist, try wrangler.jsonc
        try {
          configContent = await readFile(wranglerJsoncPath);
          configPath = wranglerJsoncPath;
          isToml = false;
        } catch (error) {
          // If neither exists, create wrangler.toml
          configPath = wranglerTomlPath;
          configContent = `name = "${path.basename(projectDir)}"
compatibility_flags = ["nodejs_compat"]
`;
        }
      }

      // Parse the existing configuration
      let config: any = {};
      if (isToml) {
        // For TOML, we'll do a simple string manipulation
        if (!configContent.includes('compatibility_flags')) {
          // Add compatibility_flags if not present
          configContent = configContent.trim() + '\ncompatibility_flags = ["nodejs_compat"]\n';
        } else if (!configContent.includes('nodejs_compat')) {
          // Add nodejs_compat to existing flags
          configContent = configContent.replace(
            /compatibility_flags\s*=\s*\[(.*?)\]/,
            (match, flags) => {
              const flagList = flags.split(',').map((f: string) => f.trim());
              if (!flagList.includes('"nodejs_compat"')) {
                flagList.push('"nodejs_compat"');
              }
              return `compatibility_flags = [${flagList.join(', ')}]`;
            }
          );
        }
      } else {
        // For JSONC, we'll parse and modify the JSON
        try {
          config = JSON.parse(configContent);
          if (!config.compatibility_flags) {
            config.compatibility_flags = ['nodejs_compat'];
          } else if (!config.compatibility_flags.includes('nodejs_compat')) {
            config.compatibility_flags.push('nodejs_compat');
          }
          configContent = JSON.stringify(config, null, 2);
        } catch (error) {
          logger.warn('Could not parse wrangler.jsonc, creating new configuration');
          configContent = JSON.stringify({
            name: path.basename(projectDir),
            compatibility_flags: ['nodejs_compat']
          }, null, 2);
        }
      }

      // Write the updated configuration
      await writeFile(configPath, configContent);
      logger.spinnerSuccess('Wrangler configuration updated with nodejs_compat flag!');
    } catch (error) {
      logger.warn(`Could not update wrangler configuration: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
} 