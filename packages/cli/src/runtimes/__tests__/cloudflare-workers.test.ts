/**
 * @file Tests for Cloudflare Workers runtime implementation
 */

import { join } from "path";
import { CloudflareWorkersRuntime } from "../cloudflare-workers";
import { ProjectMetadata } from "../types";

// Mock the logger to avoid console output during tests
jest.mock("../../utils/logger", () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    success: jest.fn(),
    spin: jest.fn(),
    spinnerSuccess: jest.fn(),
    spinnerFail: jest.fn(),
  },
}));

// Mock the file utilities
jest.mock("../../utils/file", () => ({
  readFile: jest.fn(),
  writeFile: jest.fn(),
  readJsonFile: jest.fn(),
  writeJsonFile: jest.fn(),
  ensureDirectory: jest.fn(),
}));

// Mock the exec utilities
jest.mock("../../utils/exec", () => ({
  execCommandWithOutput: jest.fn(),
}));

// Mock the config
jest.mock("../../config", () => ({
  default: {
    version: "0.1.0",
  },
}));

// Import the mocked utilities
import {
  readFile,
  writeFile,
  readJsonFile,
  writeJsonFile,
  ensureDirectory,
} from "../../utils/file";

const mockReadFile = readFile as jest.MockedFunction<typeof readFile>;
const mockWriteFile = writeFile as jest.MockedFunction<typeof writeFile>;
const mockReadJsonFile = readJsonFile as jest.MockedFunction<
  typeof readJsonFile
>;
const mockWriteJsonFile = writeJsonFile as jest.MockedFunction<
  typeof writeJsonFile
>;
const mockEnsureDirectory = ensureDirectory as jest.MockedFunction<
  typeof ensureDirectory
>;

describe("CloudflareWorkersRuntime", () => {
  const testDir = "/test/project";
  let runtime: CloudflareWorkersRuntime;

  beforeEach(() => {
    runtime = new CloudflareWorkersRuntime();

    // Reset all mocks
    jest.clearAllMocks();

    // Setup default mock implementations
    mockEnsureDirectory.mockResolvedValue(undefined);
    mockWriteFile.mockResolvedValue(undefined);
    mockWriteJsonFile.mockResolvedValue(undefined);
  });

  describe("Basic Runtime Properties", () => {
    it("should have correct runtime name", () => {
      expect(runtime.name).toBe("cloudflare-workers");
    });

    it("should return correct ezw directory path", () => {
      expect(runtime.getEzwDirPath()).toBe("src/ezw");
    });

    it("should return correct index file path", () => {
      expect(runtime.getIndexPath()).toBe("src/index.ts");
    });

    it("should return correct import statements", () => {
      const imports = runtime.getImportStatements();
      expect(imports).toContain("@ezworkflow/node-sdk");
      expect(imports).toContain("getServer");
    });

    it("should return README content", () => {
      const readme = runtime.getReadmeContent();
      expect(readme).toContain("Cloudflare Workers");
      expect(readme).toContain("wrangler.toml");
      expect(readme).toContain("npm run dev");
    });
  });

  describe("Node.js Compatibility Configuration", () => {
    const metadata: ProjectMetadata = {
      author: { name: "Test Author", email: "<EMAIL>" },
      categories: ["test"],
      tags: ["test"],
      appType: "partner",
    };

    it("should create wrangler.toml with nodejs_compat when no config exists", async () => {
      // Mock that neither wrangler.toml nor wrangler.jsonc exist
      mockReadFile.mockRejectedValue(new Error("File not found"));
      mockReadJsonFile.mockResolvedValue({});

      await runtime.setupRuntime(testDir, metadata);

      // Verify that writeFile was called with the correct content
      expect(mockWriteFile).toHaveBeenCalledWith(
        expect.stringContaining("wrangler.toml"),
        expect.stringContaining('compatibility_flags = ["nodejs_compat"]')
      );
    });

    it("should add nodejs_compat to existing wrangler.toml", async () => {
      const initialContent = `name = "test-app"
main = "src/index.ts"
`;
      // Mock reading existing wrangler.toml
      mockReadFile.mockResolvedValue(initialContent);
      mockReadJsonFile.mockResolvedValue({});

      await runtime.setupRuntime(testDir, metadata);

      // Verify that writeFile was called with updated content including nodejs_compat
      expect(mockWriteFile).toHaveBeenCalledWith(
        expect.stringContaining("wrangler.toml"),
        expect.stringMatching(/compatibility_flags = \["nodejs_compat"\]/)
      );
    });

    it("should handle wrangler.jsonc format", async () => {
      const initialConfig = {
        name: "test-app",
        main: "src/index.ts",
      };

      // Mock that wrangler.toml doesn't exist but wrangler.jsonc does
      mockReadFile
        .mockRejectedValueOnce(new Error("File not found")) // First call for wrangler.toml
        .mockResolvedValueOnce(JSON.stringify(initialConfig, null, 2)); // Second call for wrangler.jsonc
      mockReadJsonFile.mockResolvedValue({});

      await runtime.setupRuntime(testDir, metadata);

      // Verify that writeFile was called with updated JSON content
      expect(mockWriteFile).toHaveBeenCalledWith(
        expect.stringContaining("wrangler.jsonc"),
        expect.stringContaining('"nodejs_compat"')
      );
    });
  });

  describe("Directory Structure Creation", () => {
    const metadata: ProjectMetadata = {
      author: { name: "Test Author", email: "<EMAIL>" },
      categories: ["test"],
      tags: ["test"],
      appType: "partner",
    };

    it("should create ezw directory structure", async () => {
      // Mock that no wrangler config exists
      mockReadFile.mockRejectedValue(new Error("File not found"));
      mockReadJsonFile.mockResolvedValue({});

      await runtime.setupRuntime(testDir, metadata);

      // Verify that ensureDirectory was called for all required directories
      expect(mockEnsureDirectory).toHaveBeenCalledWith(
        expect.stringContaining("src/ezw")
      );
      expect(mockEnsureDirectory).toHaveBeenCalledWith(
        expect.stringContaining("nodes")
      );
      expect(mockEnsureDirectory).toHaveBeenCalledWith(
        expect.stringContaining("executors")
      );
      expect(mockEnsureDirectory).toHaveBeenCalledWith(
        expect.stringContaining("triggers")
      );
      expect(mockEnsureDirectory).toHaveBeenCalledWith(
        expect.stringContaining("ui")
      );
    });
  });
});
