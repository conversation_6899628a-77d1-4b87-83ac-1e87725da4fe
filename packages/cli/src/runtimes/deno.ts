/**
 * Deno runtime implementation
 */
import path from 'path';
import { readJsonFile, writeJsonFile, logger, ensureDirectory } from '../utils';
import { BaseRuntime } from './base';
import { ProjectMetadata } from './types';
import config from '../config';

/**
 * Deno runtime class
 */
export class DenoRuntime extends BaseRuntime {
  /**
   * Constructor
   */
  constructor() {
    super('deno');
  }

  /**
   * Get the ezw directory path for the runtime
   * @returns The ezw directory path
   */
  getEzwDirPath(): string {
    return 'ezw';
  }

  /**
   * Get the index file path for the runtime
   * @returns The index file path
   */
  getIndexPath(): string {
    return 'main.ts';
  }

  /**
   * Get the import statements for the runtime
   * @returns The import statements
   */
  getImportStatements(): string {
    return `import { getServer } from "jsr:@ezworkflow/node-sdk";`;
  }

  /**
   * Get the README content for the runtime
   * @returns The README content specific to the runtime
   */
  getReadmeContent(): string {
    return `
# ezWorkflow Platform

## Overview
ezWorkflow is an enterprise-grade workflow automation platform that enables you to build, deploy, and manage complex workflows with ease. This project template provides a robust foundation for developing custom workflow nodes and triggers with Deno.

## Prerequisites
- Deno 1.37.0 or higher
- ezWorkflow Account
- Application credentials from the [ezWorkflow Marketplace](https://marketplace.ezworkflow.ai/my-app)

## Project Architecture

\`\`\`
├── main.ts               # Application entry point
├── deno.json             # Deno configuration
├── ezw/
│   ├── nodes/            # Custom node implementations
│   ├── executors/        # Node execution handlers
│   ├── ui/               # UI components
│   └── triggers/         # Custom trigger implementations
\`\`\`

## Development Workflow

### Creating New Components
\`\`\`bash
# Create a new node with its executor
deno run --allow-read --allow-write jsr:@ezworkflow/cli create node <node-name>

# Create a new trigger with its handler
deno run --allow-read --allow-write jsr:@ezworkflow/cli create trigger <trigger-name>

# Create a new UI component
deno run --allow-read --allow-write jsr:@ezworkflow/cli create ui <ui-name>
\`\`\`

### Development Commands
\`\`\`bash
# Start development server
deno task start

# Run with watch mode for development
deno task dev

# Run tests
deno task test
\`\`\`

## API Reference

### Core Endpoints
- \`GET /\`: API documentation
- \`GET /nodes\`: List available nodes
- \`GET /nodes/:id\`: Retrieve node definition
- \`POST /api/nodes/:id/execute\`: Execute node
- \`POST /api/triggers/:id\`: Trigger workflow

## Configuration
1. Register your application in the [ezWorkflow Marketplace](https://marketplace.ezworkflow.ai/my-app)
2. Obtain your application credentials
3. Configure the credentials in your project settings

## Best Practices
- Follow the established project structure
- Implement proper error handling
- Add comprehensive documentation for custom nodes
- Test thoroughly before deployment

## Support
For support, contact your ezWorkflow account manager or visit our [Support Portal](https://support.ezworkflow.ai).

## License
ezWorkflow License - All rights reserved
`;
  }

  /**
   * Setup runtime-specific configuration
   * @param projectDir Project directory
   * @param metadata Project metadata
   */
  async setupRuntime(projectDir: string, metadata: ProjectMetadata): Promise<void> {
    logger.info('📦 Setting up Deno dependencies...');

    // We don't need to create deps.ts as Hono's Deno template uses imports in deno.json
    logger.spinnerSuccess('Using Deno import maps instead of deps.ts');

    // Create the ezw directory structure
    const ezwDir = path.join(projectDir, 'ezw');
    await ensureDirectory(ezwDir);
    await ensureDirectory(path.join(ezwDir, 'nodes'));
    await ensureDirectory(path.join(ezwDir, 'executors'));
    await ensureDirectory(path.join(ezwDir, 'triggers'));

    // Update the existing deno.json file with ezWorkflow metadata
    const denoJsonPath = path.join(projectDir, 'deno.json');

    try {
      // Read the existing deno.json file
      let denoJson: Record<string, any> = {};
      try {
        denoJson = await readJsonFile(denoJsonPath);
      } catch (error) {
        // If file doesn't exist, create a basic structure
        denoJson = {
          imports: {},
          tasks: {},
          compilerOptions: {}
        };
      }

      // Add basic metadata if not present
      denoJson.name = denoJson.name || path.basename(projectDir);
      denoJson.description = denoJson.description || "A project for creating workflow nodes with Deno";
      denoJson.version = denoJson.version || "0.1.0";
      // No longer adding ezWorkflow metadata to deno.json
      // All metadata is now stored exclusively in ezw.json

      // Enhance tasks
      denoJson.tasks = {
        ...denoJson.tasks,
        "start": denoJson.tasks?.start || "deno run --allow-net --allow-read --allow-env main.ts",
        "dev": denoJson.tasks?.dev || "deno run --watch --allow-net --allow-read --allow-env main.ts",
        "test": denoJson.tasks?.test || "deno test --allow-net --allow-read"
      };

      // Add ezWorkflow SDK to imports
      denoJson.imports = {
        ...denoJson.imports,
        "@ezworkflow/node-sdk": `jsr:@ezworkflow/node-sdk@${config.version}`
      };

      // Write the updated deno.json
      await writeJsonFile(denoJsonPath, denoJson);
      logger.spinnerSuccess('Deno configuration updated successfully!');
    } catch (error) {
      logger.warn(`Could not update deno.json: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
