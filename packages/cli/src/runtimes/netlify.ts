/**
 * Netlify runtime implementation
 */
import path from 'path';
import { readFile, writeFile, readJsonFile, writeJsonFile, ensureDirectory, logger } from '../utils';
import { BaseRuntime } from './base';
import { ProjectMetadata } from './types';
import config from '../config';

/**
 * Netlify runtime class
 */
export class NetlifyRuntime extends BaseRuntime {
  /**
   * Constructor
   */
  constructor() {
    super('netlify');
  }

  /**
   * Get the ezw directory path for the runtime
   * @returns The ezw directory path
   */
  getEzwDirPath(): string {
    return 'netlify/edge-functions/ezw';
  }

  /**
   * Get the index file path for the runtime
   * @returns The index file path
   */
  getIndexPath(): string {
    return 'netlify/edge-functions/index.ts';
  }

  /**
   * Get the import statements for the runtime
   * @returns The import statements
   */
  getImportStatements(): string {
    return `import { getServer } from "jsr:@ezworkflow/node-sdk";`;
  }

  /**
   * Get the README content for the runtime
   * @returns The README content specific to the runtime
   */
  getReadmeContent(): string {
    return `
# ezWorkflow Platform

## Overview
ezWorkflow is an enterprise-grade workflow automation platform that enables you to build, deploy, and manage complex workflows with ease. This project template provides a robust foundation for developing custom workflow nodes and triggers with Netlify Edge Functions.

## Prerequisites
- Node.js 20.x or higher
- Netlify CLI
- ezWorkflow Account
- Application credentials from the [ezWorkflow Marketplace](https://marketplace.ezworkflow.ai/my-app)

## Project Architecture

\`\`\`
netlify/
├── edge-functions/
│   ├── index.ts           # Application entry point
│   └── ezw/
│       ├── nodes/         # Custom node implementations
│       ├── executors/     # Node execution handlers
│       ├── triggers/      # Custom trigger implementations
│       └── ui/            # UI components
\`\`\`

## Development Workflow

### Creating New Components
\`\`\`bash
# Create a new node with its executor
npx ezw create node <node-name>

# Create a new trigger with its handler
npx ezw create trigger <trigger-name>

# Create a new UI component
npx ezw create ui <ui-name>
\`\`\`

### Development Commands
\`\`\`bash
# Start development server
npm run dev

# Deploy to Netlify
npm run deploy
\`\`\`

## API Reference

### Core Endpoints
- \`GET /\`: API documentation
- \`GET /nodes\`: List available nodes
- \`GET /nodes/:id\`: Retrieve node definition
- \`POST /api/nodes/:id/execute\`: Execute node
- \`POST /api/triggers/:id\`: Trigger workflow

## Configuration
1. Register your application in the [ezWorkflow Marketplace](https://marketplace.ezworkflow.ai/my-app)
2. Obtain your application credentials
3. Configure the credentials in your project settings

## Best Practices
- Follow the established project structure
- Implement proper error handling
- Add comprehensive documentation for custom nodes
- Test thoroughly before deployment

## Support
For support, contact your ezWorkflow account manager or visit our [Support Portal](https://support.ezworkflow.ai).

## License
ezWorkflow License - All rights reserved
`;
  }

  /**
   * Setup runtime-specific configuration
   * @param projectDir Project directory
   * @param metadata Project metadata
   */
  async setupRuntime(projectDir: string, metadata: ProjectMetadata): Promise<void> {
    logger.info('📦 Setting up Netlify Edge Functions dependencies...');

    // Check if netlify.toml exists and update it if needed
    const netlifyTomlPath = path.join(projectDir, 'netlify.toml');

    try {
      await readFile(netlifyTomlPath);
      logger.info('Netlify configuration already exists');
    } catch (error) {
      // Create netlify.toml if it doesn't exist
      const netlifyTomlContent = `[build]
  publish = "public"

[dev]
  framework = "#custom"
  command = "npm run dev"
  port = 8888
  targetPort = 3000

[[edge_functions]]
  function = "index"
  path = "/*"
`;
      try {
        await writeFile(netlifyTomlPath, netlifyTomlContent);
        logger.spinnerSuccess('Netlify configuration created successfully!');
      } catch (error) {
        logger.warn(`Could not create netlify.toml: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    // Create the Netlify Edge Functions directory if it doesn't exist
    const edgeFunctionsDir = path.join(projectDir, 'netlify', 'edge-functions');
    await ensureDirectory(edgeFunctionsDir);

    // Create the ezw directory structure
    const ezwDir = path.join(edgeFunctionsDir, 'ezw');
    await ensureDirectory(ezwDir);
    await ensureDirectory(path.join(ezwDir, 'nodes'));
    await ensureDirectory(path.join(ezwDir, 'executors'));
    await ensureDirectory(path.join(ezwDir, 'triggers'));

    // Check if index.ts already exists
    const indexPath = path.join(edgeFunctionsDir, 'index.ts');

    try {
      const existingContent = await readFile(indexPath);
      logger.info('Netlify Edge Functions index file already exists');

      // If the file exists but doesn't have our ezWorkflow imports, add them
      if (!existingContent.includes('@ezworkflow/node-sdk')) {
        logger.info('Adding ezWorkflow SDK to existing index.ts file');

        // Preserve the existing file structure but add our imports and middleware
        let updatedContent = existingContent;

        // Add import for ezWorkflow SDK if not present
        if (!updatedContent.includes('@ezworkflow/node-sdk')) {
          const importLine = "import { getServer } from 'jsr:@ezworkflow/node-sdk'\nimport { nodes, executors, triggers } from './ezw'\n";
          // Add after the last import statement
          const lastImportIndex = updatedContent.lastIndexOf('import ');
          if (lastImportIndex !== -1) {
            const lastImportLineEnd = updatedContent.indexOf('\n', lastImportIndex);
            if (lastImportLineEnd !== -1) {
              updatedContent = updatedContent.substring(0, lastImportLineEnd + 1) +
                '\n' + importLine + updatedContent.substring(lastImportLineEnd + 1);
            }
          }
        }

        // Add middleware if not present
        if (!updatedContent.includes('/api/ezw')) {
          const middlewareLine = "\n// Add ezWorkflow middleware\napp.route('/api/ezw', getServer({\n  nodes,\n  executors,\n  triggers\n}))\n";
          // Add after app initialization
          const appInitIndex = updatedContent.indexOf('const app = new Hono');
          if (appInitIndex !== -1) {
            const appInitLineEnd = updatedContent.indexOf('\n', appInitIndex);
            if (appInitLineEnd !== -1) {
              updatedContent = updatedContent.substring(0, appInitLineEnd + 1) +
                middlewareLine + updatedContent.substring(appInitLineEnd + 1);
            }
          }
        }

        await writeFile(indexPath, updatedContent);
        logger.spinnerSuccess('Updated Netlify Edge Functions index file with ezWorkflow integration');
      }
    } catch (error) {
      // If the file doesn't exist, create it with the standard Hono template
      const indexContent = `import { Hono } from 'jsr:@hono/hono'
import { handle } from 'jsr:@hono/hono/netlify'
import { getServer } from 'jsr:@ezworkflow/node-sdk'
import { nodes, executors, triggers } from './ezw'

const app = new Hono()

// Add ezWorkflow middleware
app.route('/api/ezw', getServer({
  nodes,
  executors,
  triggers
}))

app.get('/', (c) => {
  return c.text('Hello from ezWorkflow on Netlify Edge Functions!')
})

export default handle(app)
`;
      try {
        await writeFile(indexPath, indexContent);
        logger.spinnerSuccess('Netlify Edge Functions index file created successfully!');
      } catch (error) {
        logger.warn(`Could not create index.ts: ${error instanceof Error ? error.message : String(error)}`);
      }
    }

    // Update package.json with Netlify-specific dependencies
    const packageJsonPath = path.join(projectDir, 'package.json');

    try {
      // Read existing package.json
      let packageJson = await readJsonFile(packageJsonPath);

      // No longer adding ezWorkflow metadata to package.json
      // All metadata is now stored exclusively in ezw.json

      // Add dependencies without overwriting existing ones
      packageJson.dependencies = packageJson.dependencies || {};

      // Only add SDK if not already present
      if (!packageJson.dependencies['@ezworkflow/node-sdk']) {
        packageJson.dependencies['@ezworkflow/node-sdk'] = '^0.1.0';
      }

      // Add Hono if not already present
      if (!packageJson.dependencies['@hono/hono']) {
        packageJson.dependencies['@hono/hono'] = '^3.0.0';
      }

      // Check if Netlify CLI is already in dependencies
      const hasNetlifyCli =
        (packageJson.devDependencies && packageJson.devDependencies['netlify-cli']) ||
        (packageJson.dependencies && packageJson.dependencies['netlify-cli']);

      // Add Netlify CLI as dev dependency if not present
      if (!hasNetlifyCli) {
        packageJson.devDependencies = packageJson.devDependencies || {};
        packageJson.devDependencies['netlify-cli'] = '^17.10.0';
      }

      // Add scripts for Netlify if they don't exist
      packageJson.scripts = packageJson.scripts || {};

      // Only add dev script if not already defined
      if (!packageJson.scripts['dev']) {
        packageJson.scripts['dev'] = 'netlify dev';
      }

      // Only add deploy script if not already defined
      if (!packageJson.scripts['deploy']) {
        packageJson.scripts['deploy'] = 'netlify deploy --prod';
      }

      await writeJsonFile(packageJsonPath, packageJson);
      logger.spinnerSuccess('Package.json updated with Netlify dependencies!');
    } catch (error) {
      logger.warn(`Could not update package.json: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
