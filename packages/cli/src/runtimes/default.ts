/**
 * Default runtime implementation for standard Node.js runtimes
 */
import path from 'path';
import { readJsonFile, writeJsonFile, logger, ensureDirectory } from '../utils';
import { BaseRuntime } from './base';
import { ProjectMetadata } from './types';
import { HonoRuntimeTemplate } from '../utils/prompt';
import config from '../config';

/**
 * Default runtime paths
 */
const DEFAULT_PATHS: Record<HonoRuntimeTemplate, { ezwDir: string; indexPath: string }> = {
  'aws-lambda': { ezwDir: 'src/ezw', indexPath: 'src/index.ts' },
  'bun': { ezwDir: 'src/ezw', indexPath: 'src/index.ts' },
  'cloudflare-workers': { ezwDir: 'src/ezw', indexPath: 'src/index.ts' },
  'deno': { ezwDir: 'ezw', indexPath: 'main.ts' },
  'fastly': { ezwDir: 'src/ezw', indexPath: 'src/index.ts' },
  'lambda-edge': { ezwDir: 'src/ezw', indexPath: 'src/index.ts' },
  'netlify': { ezwDir: 'netlify/edge-functions/ezw', indexPath: 'netlify/edge-functions/index.ts' },
  'nextjs': { ezwDir: 'app/ezw', indexPath: 'app/api/[...route]/route.ts' },
  'nodejs': { ezwDir: 'src/ezw', indexPath: 'src/index.ts' },
  'vercel': { ezwDir: 'api/ezw', indexPath: 'api/index.ts' },
  'x-basic': { ezwDir: 'app/ezw', indexPath: 'app/server.ts' }
};

/**
 * Default runtime class for standard Node.js runtimes
 */
export class DefaultRuntime extends BaseRuntime {
  /**
   * Constructor
   * @param name Runtime name
   */
  constructor(name: HonoRuntimeTemplate) {
    super(name);
  }

  /**
   * Get the ezw directory path for the runtime
   * @returns The ezw directory path
   */
  getEzwDirPath(): string {
    return DEFAULT_PATHS[this.name]?.ezwDir || 'src/ezw';
  }

  /**
   * Get the index file path for the runtime
   * @returns The index file path
   */
  getIndexPath(): string {
    return DEFAULT_PATHS[this.name]?.indexPath || 'src/index.ts';
  }

  /**
   * Get the import statements for the runtime
   * @returns The import statements
   */
  getImportStatements(): string {
    return `import { getServer } from "@ezworkflow/node-sdk";`;
  }

  /**
   * Get the README content for the runtime
   * @returns The README content specific to the runtime
   */
  getReadmeContent(): string {
    // Customize the project architecture based on the runtime
    let projectArchitecture = '';

    if (this.name === 'nextjs') {
      projectArchitecture = `app/
├── api/
│   └── [...route]/
│       └── route.ts       # API entry point
├── ezw/
│   ├── nodes/             # Custom node implementations
│   ├── executors/         # Node execution handlers
│   ├── ui/                # UI components
│   └── triggers/          # Custom trigger implementations`;
    } else if (this.name === 'vercel') {
      projectArchitecture = `api/
├── index.ts              # API entry point
├── ezw/
│   ├── nodes/            # Custom node implementations
│   ├── executors/        # Node execution handlers
│   ├── ui/               # UI components
│   └── triggers/         # Custom trigger implementations`;
    } else if (this.name === 'x-basic') {
      projectArchitecture = `app/
├── server.ts             # Application entry point
├── ezw/
│   ├── nodes/            # Custom node implementations
│   ├── executors/        # Node execution handlers
│   ├── ui/               # UI components
│   └── triggers/         # Custom trigger implementations`;
    } else {
      // Default structure for most runtimes
      projectArchitecture = `src/
├── index.ts              # Application entry point
├── ezw/
│   ├── nodes/            # Custom node implementations
│   ├── executors/        # Node execution handlers
│   ├── ui/               # UI components
│   └── triggers/         # Custom trigger implementations`;
    }

    return `
# ezWorkflow Platform

## Overview
ezWorkflow is an enterprise-grade workflow automation platform that enables you to build, deploy, and manage complex workflows with ease. This project template provides a robust foundation for developing custom workflow nodes and triggers.

## Prerequisites
- Node.js 20.x or higher
- ezWorkflow Account
- Application credentials from the [ezWorkflow Marketplace](https://marketplace.ezworkflow.ai/my-app)

## Project Architecture

\`\`\`
${projectArchitecture}
\`\`\`

## Development Workflow

### Creating New Components
\`\`\`bash
# Create a new node with its executor
npx ezw create node <node-name>

# Create a new trigger with its handler
npx ezw create trigger <trigger-name>

# Create a new UI component
npx ezw create ui <ui-name>
\`\`\`

### Development Commands
\`\`\`bash
# Start development server
npx ezw dev

# Build for production
npx ezw build
\`\`\`

## API Reference

### Core Endpoints
- \`GET /\`: API documentation
- \`GET /nodes\`: List available nodes
- \`GET /nodes/:id\`: Retrieve node definition
- \`POST /api/nodes/:id/execute\`: Execute node
- \`POST /api/triggers/:id\`: Trigger workflow
- \`GET /ui\`: List available UI components
- \`GET /ui/:id\`: Retrieve UI component definition

## Configuration
1. Register your application in the [ezWorkflow Marketplace](https://marketplace.ezworkflow.ai/my-app)
2. Obtain your application credentials
3. Configure the credentials in your project settings

## Best Practices
- Follow the established project structure
- Implement proper error handling
- Add comprehensive documentation for custom nodes
- Test thoroughly before deployment

## Support
For support, contact your ezWorkflow account manager or visit our [Support Portal](https://support.ezworkflow.ai).

## License
ezWorkflow License - All rights reserved
`;
  }

  /**
   * Setup runtime-specific configuration
   * @param projectDir Project directory
   * @param metadata Project metadata
   */
  async setupRuntime(projectDir: string, _metadata: ProjectMetadata): Promise<void> {
    logger.info(`📦 Setting up ${this.name} dependencies...`);

    // Create the ezw directory structure
    const ezwDirPath = this.getEzwDirPath();
    const ezwDir = path.join(projectDir, ezwDirPath);
    await ensureDirectory(ezwDir);
    await ensureDirectory(path.join(ezwDir, 'nodes'));
    await ensureDirectory(path.join(ezwDir, 'executors'));
    await ensureDirectory(path.join(ezwDir, 'triggers'));
    await ensureDirectory(path.join(ezwDir, 'ui'));

    // Update package.json with runtime-specific dependencies
    const packageJsonPath = path.join(projectDir, 'package.json');

    try {
      let packageJson = await readJsonFile(packageJsonPath);

      // Add dependencies without overwriting existing ones
      packageJson.dependencies = packageJson.dependencies || {};

      // Only add SDK if not already present
      if (!packageJson.dependencies['@ezworkflow/node-sdk']) {
        packageJson.dependencies['@ezworkflow/node-sdk'] = `^${config.version}`;
      }

      await writeJsonFile(packageJsonPath, packageJson);
      logger.spinnerSuccess('Package.json updated with dependencies!');
    } catch (error) {
      logger.warn(`Could not update package.json: ${error instanceof Error ? error.message : String(error)}`);
    }
  }
}
