/**
 * @file Runtime Implementations Index
 *
 * This file serves as the entry point for all runtime implementations.
 * It provides a factory function to get the appropriate runtime implementation
 * based on the specified runtime template, and exports all runtime-related types
 * and implementations.
 */
import { HonoRuntimeTemplate } from '../utils/prompt';
import { RuntimeConfig } from './types';
import { DenoRuntime } from './deno';
import { NetlifyRuntime } from './netlify';
import { CloudflareWorkersRuntime } from './cloudflare-workers';
import { DefaultRuntime } from './default';

/**
 * Gets the appropriate runtime implementation for a specific runtime template.
 *
 * This factory function returns the runtime implementation that corresponds to
 * the specified runtime template. It handles special cases for runtimes that
 * have custom implementations (like Deno and Netlify) and falls back to the
 * default implementation for other runtimes.
 *
 * @param runtime - The runtime template identifier
 * @returns The corresponding runtime implementation
 */
export function getRuntimeImplementation(runtime: HonoRuntimeTemplate): RuntimeConfig {
  switch (runtime) {
    case 'deno':
      return new DenoRuntime();
    case 'netlify':
      return new NetlifyRuntime();
    case 'cloudflare-workers':
      return new CloudflareWorkersRuntime();
    default:
      return new DefaultRuntime(runtime);
  }
}

export * from './types';
export * from './base';
export * from './deno';
export * from './netlify';
export * from './default';
