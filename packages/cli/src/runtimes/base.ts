/**
 * @file Base Runtime Implementation
 *
 * This file provides a base abstract class that all runtime implementations extend.
 * It implements common functionality and defines the abstract methods that
 * each specific runtime must implement to provide its unique behavior.
 */

import { HonoRuntimeTemplate } from '../utils/prompt';
import { ProjectMetadata, RuntimeConfig } from './types';

/**
 * Base abstract class for runtime implementations.
 *
 * This class implements the RuntimeConfig interface and provides a foundation
 * for all specific runtime implementations. It implements common functionality
 * and defines abstract methods that each runtime must implement to provide
 * its unique behavior.
 */
export abstract class BaseRuntime implements RuntimeConfig {
  /**
   * The identifier for this runtime template.
   *
   * This property stores the runtime identifier that matches one of the
   * values in the HonoRuntimeTemplate type.
   */
  name: HonoRuntimeTemplate;

  /**
   * Creates a new runtime implementation instance.
   *
   * @param name - The runtime identifier from HonoRuntimeTemplate
   */
  constructor(name: HonoRuntimeTemplate) {
    this.name = name;
  }

  /**
   * Sets up runtime-specific configuration for a project.
   *
   * This abstract method must be implemented by each runtime to perform
   * any runtime-specific setup steps.
   *
   * @param projectDir - Absolute path to the project directory
   * @param metadata - Project metadata including author, categories, and tags
   */
  abstract setupRuntime(projectDir: string, metadata: ProjectMetadata): Promise<void>;

  /**
   * Gets the import path prefix for the runtime.
   *
   * This method provides a default implementation that derives the import
   * path prefix from the ezWorkflow directory path. Specific runtimes can
   * override this if they need custom import path prefixes.
   *
   * @returns The import path prefix as a string
   */
  getImportPathPrefix(): string {
    return './' + this.getEzwDirPath();
  }

  /**
   * Gets the ezWorkflow directory path for the runtime.
   *
   * This abstract method must be implemented by each runtime to specify
   * where ezWorkflow files should be stored for that runtime.
   *
   * @returns The ezWorkflow directory path as a string
   */
  abstract getEzwDirPath(): string;

  /**
   * Gets the main index file path for the runtime.
   *
   * This abstract method must be implemented by each runtime to specify
   * the path to the main entry point file for that runtime.
   *
   * @returns The index file path as a string
   */
  abstract getIndexPath(): string;

  /**
   * Gets the import statements needed for the runtime.
   *
   * This abstract method must be implemented by each runtime to provide
   * the appropriate import statements for that runtime.
   *
   * @returns The import statements as a string
   */
  abstract getImportStatements(): string;

  /**
   * Gets the README content specific to the runtime.
   *
   * This abstract method must be implemented by each runtime to provide
   * custom documentation about how to use ezWorkflow with that specific runtime.
   *
   * @returns The README content as a string
   */
  abstract getReadmeContent(): string;
}
