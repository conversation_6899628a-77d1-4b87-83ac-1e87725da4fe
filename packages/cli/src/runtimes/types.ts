/**
 * @file Runtime Configuration Types
 *
 * This file defines the interfaces used for runtime-specific configurations.
 * Each supported Hono.js runtime has its own implementation of these interfaces,
 * allowing for consistent handling of different runtimes with runtime-specific
 * customizations.
 */

import { HonoRuntimeTemplate } from '../utils/prompt';

/**
 * Author information interface for storing essential author data.
 *
 * This interface defines the structure of author information that is collected
 * during project creation and used for documentation and configuration.
 */
export interface AuthorInfo {
  /** The name of the author or organization */
  name: string;

  /** The email of the author */
  email: string;
}

/**
 * Project metadata interface for storing project information.
 *
 * This interface defines the structure of metadata that is collected
 * during project creation and used for documentation and configuration.
 */
export interface ProjectMetadata {
  /** Author information */
  author: AuthorInfo;

  /** Categories that describe the project's purpose or domain */
  categories: string[];

  /** Tags for better discoverability */
  tags: string[];

  /** Application type (partner or account) */
  appType?: 'partner' | 'account';
}

/**
 * Runtime configuration interface for Hono.js runtimes.
 *
 * This interface defines the contract that all runtime implementations must follow.
 * It provides methods for getting runtime-specific paths, import statements,
 * and other configuration details needed for setting up ezWorkflow in a
 * specific Hono.js runtime environment.
 */
export interface RuntimeConfig {
  /**
   * The identifier for this runtime template.
   *
   * This must match one of the values in the HonoRuntimeTemplate type.
   */
  name: HonoRuntimeTemplate;

  /**
   * Sets up runtime-specific configuration for a project.
   *
   * This method is called during project creation to perform any runtime-specific
   * setup steps, such as creating configuration files, modifying package.json,
   * or setting up directory structures specific to the runtime.
   *
   * @param projectDir - Absolute path to the project directory
   * @param metadata - Project metadata including author, categories, and tags
   * @returns Promise that resolves when setup is complete
   */
  setupRuntime: (projectDir: string, metadata: ProjectMetadata) => Promise<void>;

  /**
   * Gets the import path prefix for the runtime.
   *
   * This method returns the prefix that should be used in import statements
   * when importing ezWorkflow modules. Different runtimes may have different
   * import path conventions.
   *
   * @returns The import path prefix as a string
   */
  getImportPathPrefix: () => string;

  /**
   * Gets the ezWorkflow directory path for the runtime.
   *
   * This method returns the path where ezWorkflow files should be stored
   * for this runtime. Different runtimes may have different conventions
   * for where these files should be located.
   *
   * @returns The ezWorkflow directory path as a string
   */
  getEzwDirPath: () => string;

  /**
   * Gets the main index file path for the runtime.
   *
   * This method returns the path to the main entry point file for the runtime.
   * This is the file where the Hono app is initialized and where ezWorkflow
   * middleware will be added.
   *
   * @returns The index file path as a string
   */
  getIndexPath: () => string;

  /**
   * Gets the import statements needed for the runtime.
   *
   * This method returns the import statements that should be added to the
   * main index file to import ezWorkflow. Different runtimes may have
   * different import syntax or requirements.
   *
   * @returns The import statements as a string
   */
  getImportStatements: () => string;

  /**
   * Gets the README content specific to the runtime.
   *
   * This method returns the runtime-specific portion of the README.md file
   * that will be included in the project. This allows each runtime to provide
   * custom documentation about how to use ezWorkflow with that specific runtime.
   *
   * @returns The README content as a string
   */
  getReadmeContent: () => string;
}
