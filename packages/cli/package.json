{"name": "ezw", "version": "0.1.0", "description": "CLI tool for ezWorkflow - create and manage node projects, nodes, and triggers", "main": "dist/index.js", "bin": {"ezw": "dist/index.js"}, "files": ["dist", "templates"], "scripts": {"build": "tsc", "dev": "tsc -w", "lint": "eslint src --ext .ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prepublishOnly": "npm run build"}, "keywords": ["ezworkflow", "cli", "node", "workflow", "automation"], "author": "ezWorkflow Team", "license": "MIT", "dependencies": {"@ezworkflow/project-config": "workspace:*", "boxen": "8.0.1", "chalk": "5.4.1", "cli-progress": "3.12.0", "cli-spinners": "3.2.0", "cli-table3": "0.6.5", "cli-welcome": "3.0.1", "commander": "^14.0.0", "create-hono": "^0.19.1", "execa": "^9.6.0", "figlet": "1.8.1", "fs-extra": "^11.3.0", "gradient-string": "3.0.0", "inquirer": "^12.6.3", "nanospinner": "1.2.2", "ora": "8.2.0", "validate-npm-package-name": "^6.0.0"}, "devDependencies": {"@types/cli-progress": "3.11.6", "@types/figlet": "1.7.0", "@types/fs-extra": "^11.0.4", "@types/gradient-string": "1.1.6", "@types/inquirer": "^9.0.8", "@types/jest": "^29.5.14", "@types/node": "^22.15.29", "@types/validate-npm-package-name": "^4.0.2", "eslint": "^9.28.0", "jest": "^29.7.0", "ts-jest": "^29.3.4", "typescript": "^5.8.3"}, "engines": {"node": ">=14.0.0"}}