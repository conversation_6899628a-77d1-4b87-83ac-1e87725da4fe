/**
 * @file Core types for ezWorkflow project configuration
 *
 * This file defines the types used for ezWorkflow project configuration.
 * These types are shared between the CLI and user projects.
 */

/**
 * Supported Hono.js runtime templates
 */
export type HonoRuntimeTemplate =
  | 'aws-lambda'
  | 'bun'
  | 'cloudflare-workers'
  | 'deno'
  | 'fastly'
  | 'lambda-edge'
  | 'netlify'
  | 'nextjs'
  | 'nodejs'
  | 'vercel'
  | 'x-basic';

/**
 * Author information interface for storing essential author data.
 *
 * This interface defines the structure of author information that is collected
 * during project creation and used for documentation and configuration.
 */
export interface AuthorInfo {
  /** The name of the author or organization */
  name: string;

  /** The email of the author */
  email: string;
}

/**
 * Configuration interface for ezw.json
 *
 * This interface defines the structure of the ezWorkflow configuration file.
 * It contains paths to important directories, runtime information, and other
 * settings needed for the ezWorkflow system to function properly.
 */
export interface EzwConfig {
  /** Base directory for ezWorkflow files (e.g., 'src/ezw', 'ezw', 'app/ezw') */
  baseDir: string;

  /** Import path prefix for ezWorkflow files (used in import statements) */
  importPathPrefix: string;

  /** Path to the main Hono index file (relative to project root) */
  indexFilePath: string;

  /** Path to the nodes directory (relative to project root) */
  nodesDirPath: string;

  /** Path to the executors directory (relative to project root) */
  executorsDirPath: string;

  /** Path to the triggers directory (relative to project root) */
  triggersDirPath: string;

  /** Path to the UI components directory (relative to project root) */
  uiDirPath?: string;

  /** Hono.js runtime template (e.g., 'nodejs', 'deno', 'cloudflare-workers') */
  runtime: HonoRuntimeTemplate;

  /** Version information for the ezWorkflow configuration */
  version: string;

  /** API key for authentication (optional) */
  apiKey?: string;

  /** Author information */
  author?: AuthorInfo;

  /** Categories that describe the project's purpose or domain */
  categories?: string[];

  /** Tags for better discoverability */
  tags?: string[];

  /** Application type (partner or account) */
  appType?: 'partner' | 'account';
}

/**
 * Configuration validation error
 */
export class ConfigValidationError extends Error {
  constructor(message: string, public readonly field?: string) {
    super(message);
    this.name = 'ConfigValidationError';
  }
}

/**
 * Configuration file not found error
 */
export class ConfigNotFoundError extends Error {
  constructor(configPath: string) {
    super(`ezWorkflow configuration file not found at: ${configPath}`);
    this.name = 'ConfigNotFoundError';
  }
}
