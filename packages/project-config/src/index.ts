/**
 * @file Main entry point for @ezworkflow/project-config
 *
 * This package provides utilities for reading and validating ezWorkflow
 * configuration files. It's designed to be used both by the ezWorkflow CLI
 * and by user projects that need to access their configuration.
 */

// Export types
export type {
  EzwConfig,
  AuthorInfo,
  HonoRuntimeTemplate
} from './types';

// Export error classes
export {
  ConfigValidationError,
  ConfigNotFoundError
} from './types';

// Export core functions
export {
  getConfig,
  hasConfig,
  getConfigPath
} from './config';

// Export validation schema (for advanced use cases)
export {
  EzwConfigSchema,
  AuthorInfoSchema,
  HonoRuntimeTemplateSchema,
  type ValidatedEzwConfig
} from './schema';
