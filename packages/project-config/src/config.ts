/**
 * @file Core configuration reading functionality
 *
 * This file provides the main functions for reading and validating
 * ezWorkflow configuration files from user projects.
 */

import { readFileSync, existsSync } from 'fs';
import { join } from 'path';
import { EzwConfigSchema } from './schema';
import { EzwConfig, ConfigValidationError, ConfigNotFoundError } from './types';

// Re-export error classes for convenience
export { ConfigValidationError, ConfigNotFoundError } from './types';

/**
 * Gets the ezWorkflow configuration from the ezw.json file.
 *
 * This function reads the configuration file from the specified directory,
 * validates its structure, and returns the parsed configuration object.
 *
 * Unlike the CLI version, this function only reads existing configurations
 * and does not create new ones. It's designed for use in user projects
 * where the configuration should already exist.
 *
 * @param projectDir - The project directory (absolute path). Defaults to current working directory.
 * @returns The validated configuration object
 * @throws ConfigNotFoundError if the configuration file doesn't exist
 * @throws ConfigValidationError if the configuration is invalid
 *
 * @example
 * ```typescript
 * import { getConfig } from '@ezworkflow/project-config';
 *
 * try {
 *   const config = getConfig();
 *   console.log('Base directory:', config.baseDir);
 *   console.log('Runtime:', config.runtime);
 * } catch (error) {
 *   if (error instanceof ConfigNotFoundError) {
 *     console.error('ezWorkflow not initialized in this project');
 *   } else if (error instanceof ConfigValidationError) {
 *     console.error('Invalid configuration:', error.message);
 *   }
 * }
 * ```
 */
export function getConfig(projectDir: string = process.cwd()): EzwConfig {
  const configPath = join(projectDir, 'ezw.json');

  // Check if the configuration file exists
  if (!existsSync(configPath)) {
    throw new ConfigNotFoundError(configPath);
  }

  try {
    // Read and parse the configuration file
    const configContent = readFileSync(configPath, 'utf-8');
    const rawConfig = JSON.parse(configContent);

    // Validate the configuration using Zod schema
    const validationResult = EzwConfigSchema.safeParse(rawConfig);

    if (!validationResult.success) {
      const firstError = validationResult.error.errors[0];
      const field = firstError.path.join('.');
      throw new ConfigValidationError(
        `Invalid configuration: ${firstError.message}`,
        field
      );
    }

    return validationResult.data;
  } catch (error) {
    if (error instanceof ConfigNotFoundError || error instanceof ConfigValidationError) {
      throw error;
    }

    if (error instanceof SyntaxError) {
      throw new ConfigValidationError('Invalid JSON in configuration file');
    }

    throw new ConfigValidationError(
      `Failed to read configuration file: ${error instanceof Error ? error.message : String(error)}`
    );
  }
}

/**
 * Checks if an ezWorkflow configuration file exists in the specified directory.
 *
 * This is a utility function that can be used to check if a project has been
 * initialized with ezWorkflow before attempting to read the configuration.
 *
 * @param projectDir - The project directory (absolute path). Defaults to current working directory.
 * @returns True if the configuration file exists, false otherwise
 *
 * @example
 * ```typescript
 * import { hasConfig } from '@ezworkflow/project-config';
 *
 * if (hasConfig()) {
 *   const config = getConfig();
 *   // Use the configuration...
 * } else {
 *   console.log('ezWorkflow not initialized in this project');
 * }
 * ```
 */
export function hasConfig(projectDir: string = process.cwd()): boolean {
  const configPath = join(projectDir, 'ezw.json');
  return existsSync(configPath);
}

/**
 * Gets the configuration file path for a given project directory.
 *
 * This utility function returns the expected path to the ezWorkflow
 * configuration file for a given project directory.
 *
 * @param projectDir - The project directory (absolute path). Defaults to current working directory.
 * @returns The full path to the configuration file
 */
export function getConfigPath(projectDir: string = process.cwd()): string {
  return join(projectDir, 'ezw.json');
}
