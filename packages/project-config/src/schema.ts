/**
 * @file Zod schemas for ezWorkflow configuration validation
 *
 * This file defines the validation schemas used to ensure that ezWorkflow
 * configuration files are properly structured and contain valid data.
 */

import { z } from 'zod';

/**
 * Schema for Hono.js runtime templates
 */
export const HonoRuntimeTemplateSchema = z.enum([
  'aws-lambda',
  'bun',
  'cloudflare-workers',
  'deno',
  'fastly',
  'lambda-edge',
  'netlify',
  'nextjs',
  'nodejs',
  'vercel',
  'x-basic'
]);

/**
 * Schema for author information
 */
export const AuthorInfoSchema = z.object({
  name: z.string().min(1, 'Author name is required'),
  email: z.string().email('Valid email address is required')
});

/**
 * Schema for ezWorkflow configuration
 */
export const EzwConfigSchema = z.object({
  baseDir: z.string().min(1, 'Base directory is required'),
  importPathPrefix: z.string().min(1, 'Import path prefix is required'),
  indexFilePath: z.string().min(1, 'Index file path is required'),
  nodesDirPath: z.string().min(1, 'Nodes directory path is required'),
  executorsDirPath: z.string().min(1, 'Executors directory path is required'),
  triggersDirPath: z.string().min(1, 'Triggers directory path is required'),
  uiDirPath: z.string().optional(),
  runtime: HonoRuntimeTemplateSchema,
  version: z.string().min(1, 'Version is required'),
  apiKey: z.string().optional(),
  author: AuthorInfoSchema.optional(),
  categories: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  appType: z.enum(['partner', 'account']).optional()
});

/**
 * Type inference from the schema
 */
export type ValidatedEzwConfig = z.infer<typeof EzwConfigSchema>;
