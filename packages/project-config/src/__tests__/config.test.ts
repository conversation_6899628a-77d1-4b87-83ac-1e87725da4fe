/**
 * @file Tests for configuration reading functionality
 */

import { writeFileSync, unlinkSync, existsSync, mkdirSync, rmSync } from 'fs';
import { join } from 'path';
import { getConfig, hasConfig, getConfigPath, ConfigNotFoundError, ConfigValidationError } from '../config';
import type { EzwConfig } from '../types';

describe('Configuration Functions', () => {
  const testDir = join(__dirname, 'test-project');
  const configPath = join(testDir, 'ezw.json');

  beforeEach(() => {
    // Create test directory
    if (!existsSync(testDir)) {
      mkdirSync(testDir, { recursive: true });
    }
  });

  afterEach(() => {
    // Clean up test files
    if (existsSync(testDir)) {
      rmSync(testDir, { recursive: true, force: true });
    }
  });

  describe('hasConfig', () => {
    it('should return true when config file exists', () => {
      const validConfig: EzwConfig = {
        baseDir: 'src/ezw',
        importPathPrefix: './src/ezw',
        indexFilePath: 'src/index.ts',
        nodesDirPath: 'src/ezw/nodes',
        executorsDirPath: 'src/ezw/executors',
        triggersDirPath: 'src/ezw/triggers',
        uiDirPath: 'src/ezw/ui',
        runtime: 'nodejs',
        version: '0.1.0'
      };

      writeFileSync(configPath, JSON.stringify(validConfig, null, 2));
      expect(hasConfig(testDir)).toBe(true);
    });

    it('should return false when config file does not exist', () => {
      expect(hasConfig(testDir)).toBe(false);
    });
  });

  describe('getConfigPath', () => {
    it('should return correct config path', () => {
      const expectedPath = join(testDir, 'ezw.json');
      expect(getConfigPath(testDir)).toBe(expectedPath);
    });

    it('should use current working directory by default', () => {
      const expectedPath = join(process.cwd(), 'ezw.json');
      expect(getConfigPath()).toBe(expectedPath);
    });
  });

  describe('getConfig', () => {
    it('should read and validate a valid configuration', () => {
      const validConfig: EzwConfig = {
        baseDir: 'src/ezw',
        importPathPrefix: './src/ezw',
        indexFilePath: 'src/index.ts',
        nodesDirPath: 'src/ezw/nodes',
        executorsDirPath: 'src/ezw/executors',
        triggersDirPath: 'src/ezw/triggers',
        uiDirPath: 'src/ezw/ui',
        runtime: 'nodejs',
        version: '0.1.0',
        apiKey: 'test-api-key',
        author: {
          name: 'Test Author',
          email: '<EMAIL>'
        },
        categories: ['utilities'],
        tags: ['test'],
        appType: 'partner'
      };

      writeFileSync(configPath, JSON.stringify(validConfig, null, 2));
      const result = getConfig(testDir);

      expect(result).toEqual(validConfig);
    });

    it('should throw ConfigNotFoundError when config file does not exist', () => {
      expect(() => getConfig(testDir)).toThrow(ConfigNotFoundError);
    });

    it('should throw ConfigValidationError for invalid JSON', () => {
      writeFileSync(configPath, 'invalid json');
      expect(() => getConfig(testDir)).toThrow(ConfigValidationError);
    });

    it('should throw ConfigValidationError for missing required fields', () => {
      const invalidConfig = {
        baseDir: 'src/ezw'
        // Missing required fields
      };

      writeFileSync(configPath, JSON.stringify(invalidConfig, null, 2));
      expect(() => getConfig(testDir)).toThrow(ConfigValidationError);
    });

    it('should throw ConfigValidationError for invalid runtime', () => {
      const invalidConfig = {
        baseDir: 'src/ezw',
        importPathPrefix: './src/ezw',
        indexFilePath: 'src/index.ts',
        nodesDirPath: 'src/ezw/nodes',
        executorsDirPath: 'src/ezw/executors',
        triggersDirPath: 'src/ezw/triggers',
        runtime: 'invalid-runtime',
        version: '0.1.0'
      };

      writeFileSync(configPath, JSON.stringify(invalidConfig, null, 2));
      expect(() => getConfig(testDir)).toThrow(ConfigValidationError);
    });
  });
});
