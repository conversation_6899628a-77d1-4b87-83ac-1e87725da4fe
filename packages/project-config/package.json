{"name": "@ezworkflow/project-config", "version": "0.1.0", "description": "Configuration file handling for ezWorkflow projects", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["ezworkflow", "config", "configuration", "project"], "author": "ezWorkflow Team", "license": "MIT", "dependencies": {"zod": "^3.25.42"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "^22.15.29", "eslint": "^9.28.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "rimraf": "^6.0.1", "ts-jest": "^29.3.4", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}}