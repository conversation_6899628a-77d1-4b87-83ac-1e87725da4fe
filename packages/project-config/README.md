# @ezworkflow/project-config

Configuration file handling for ezWorkflow projects.

## Overview

This package provides utilities for reading and validating ezWorkflow configuration files (`ezw.json`). It's designed to be used both by the ezWorkflow CLI and by user projects that need to access their configuration.

## Installation

```bash
npm install @ezworkflow/project-config
# or
yarn add @ezworkflow/project-config
# or
bun add @ezworkflow/project-config
```

## Usage

### Basic Usage

```typescript
import { getConfig, hasConfig, ConfigNotFoundError } from '@ezworkflow/project-config';

// Check if a project has ezWorkflow configuration
if (hasConfig()) {
  const config = getConfig();
  console.log('Runtime:', config.runtime);
  console.log('Base directory:', config.baseDir);
} else {
  console.log('ezWorkflow not initialized in this project');
}

// Handle errors gracefully
try {
  const config = getConfig('/path/to/project');
  // Use the configuration...
} catch (error) {
  if (error instanceof ConfigNotFoundError) {
    console.error('ezWorkflow configuration not found');
  } else {
    console.error('Invalid configuration:', error.message);
  }
}
```

### Available Functions

#### `getConfig(projectDir?: string): EzwConfig`

Reads and validates the ezWorkflow configuration from `ezw.json`.

- **projectDir**: Optional project directory path (defaults to current working directory)
- **Returns**: Validated configuration object
- **Throws**: `ConfigNotFoundError` if config file doesn't exist, `ConfigValidationError` if config is invalid

#### `hasConfig(projectDir?: string): boolean`

Checks if an ezWorkflow configuration file exists.

- **projectDir**: Optional project directory path (defaults to current working directory)
- **Returns**: `true` if config file exists, `false` otherwise

#### `getConfigPath(projectDir?: string): string`

Gets the expected path to the configuration file.

- **projectDir**: Optional project directory path (defaults to current working directory)
- **Returns**: Full path to the `ezw.json` file

### Configuration Structure

The configuration object has the following structure:

```typescript
interface EzwConfig {
  baseDir: string;              // Base directory for ezWorkflow files
  importPathPrefix: string;     // Import path prefix for modules
  indexFilePath: string;        // Path to main Hono index file
  nodesDirPath: string;         // Path to nodes directory
  executorsDirPath: string;     // Path to executors directory
  triggersDirPath: string;      // Path to triggers directory
  uiDirPath?: string;          // Path to UI components directory
  runtime: HonoRuntimeTemplate; // Hono.js runtime template
  version: string;              // Configuration version
  apiKey?: string;             // Optional API key
  author?: AuthorInfo;         // Optional author information
  categories?: string[];       // Optional categories
  tags?: string[];            // Optional tags
  appType?: 'partner' | 'account'; // Optional app type
}
```

### Error Handling

The package provides specific error classes for different failure scenarios:

- **`ConfigNotFoundError`**: Thrown when the `ezw.json` file doesn't exist
- **`ConfigValidationError`**: Thrown when the configuration file is invalid or malformed

### Integration with ezWorkflow Node SDK

This package is automatically included when you install `@ezworkflow/node-sdk`:

```typescript
import { getConfig } from '@ezworkflow/node-sdk';

const config = getConfig();
console.log('Current runtime:', config.runtime);
```

## Development

### Building

```bash
bun run build
```

### Testing

```bash
bun run test
```

### Linting

```bash
bun run lint
```

## License

MIT
