# @ezworkflow/node-sdk

SDK for creating and exposing workflow nodes for the ezWorkflow platform.

## 🚀 What's New in v0.1.0

The ezWorkflow Node SDK has been completely reorganized for better developer experience:

- **🏗️ Improved Organization**: Types, builders, and utilities are now organized in logical directories
- **📦 Centralized Imports**: All components can be imported from a single entry point
- **🔧 Enhanced Builders**: Field and auth builders with comprehensive documentation and examples
- **🎯 Better Type Safety**: Improved TypeScript support with path aliases and better IntelliSense
- **📚 Comprehensive Documentation**: All functions and types include detailed JSDoc documentation
- **🔄 Backward Compatibility**: All existing code continues to work without changes

### Server Class Renamed

The `NodeServer` class has been renamed to `ezwServer` to better reflect its role as a comprehensive ezWorkflow server:

```typescript
// NEW (recommended)
import { ezwServer } from "@ezworkflow/node-sdk";
const server = new ezwServer();

// OLD (still works for backward compatibility)
import { NodeServer } from "@ezworkflow/node-sdk";
const server = new NodeServer(); // Alias to ezwServer
```

## Installation

```bash
npm install @ezworkflow/node-sdk
# or
yarn add @ezworkflow/node-sdk
# or
pnpm add @ezworkflow/node-sdk
# or
bun add @ezworkflow/node-sdk
```

## Usage

### Creating a Node

```typescript
import {
  defineNode,
  string,
  number,
  boolean,
  options,
  object,
  array,
  stream,
} from "@ezworkflow/node-sdk";

export default defineNode({
  id: "my-node",
  name: "My Node",
  description: "Description of my node",
  version: "1.0.0",
  category: "Utilities",
  tags: ["utility", "example"],

  inputs: ({ string, options, object, boolean, number }) => ({
    name: string().description("Your name").required(),
    age: number().description("Your age").min(0).max(120),
    gender: options([
      { label: "Male", value: "male" },
      { label: "Female", value: "female" },
      { label: "Other", value: "other" },
    ]).description("Your gender"),
  }),

  outputs: ({ object, array, stream, boolean, string }) => ({
    greeting: string().description("Greeting message"),
    details: object({
      name: string(),
      age: number(),
      gender: string(),
    }).description("User details"),
  }),

  config: ({ boolean, number, string, object }) => ({
    includeAge: boolean().description("Include age in greeting").default(true),
    greetingTemplate: string()
      .description("Greeting template")
      .default("Hello, {name}!"),
  }),
});
```

### Creating an Executor

```typescript
import { defineExecutor } from "@ezworkflow/node-sdk";

export default defineExecutor(
  "my-node",
  async ({ inputs, config, logger, auth }) => {
    try {
      logger.info("Executing my-node");

      // Get inputs
      const { name, age, gender } = inputs;

      // Get configuration
      const { includeAge, greetingTemplate } = config || {};

      // Your logic here
      let greeting = greetingTemplate.replace("{name}", name);
      if (includeAge && age) {
        greeting += ` You are ${age} years old.`;
      }

      // Return outputs
      return {
        success: true,
        outputs: {
          greeting,
          details: {
            name,
            age,
            gender,
          },
        },
      };
    } catch (error) {
      logger.error(
        `Error executing my-node: ${error instanceof Error ? error.message : String(error)}`
      );

      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error",
        outputs: {},
      };
    }
  }
);
```

### Creating a Trigger

```typescript
import {
  defineTrigger,
  string,
  number,
  boolean,
  options,
  object,
  array,
} from "@ezworkflow/node-sdk";

export default defineTrigger({
  name: "My Trigger",
  version: "1.0.0",
  categories: ["Utilities"],
  tags: ["utility", "example"],
  type: "http",
  registarUrl: "https://example.com",

  expect: ({ object, array }) => ({
    contact: object({
      name: string(),
      email: string().required(),
      phone: string(),
    }).required(),
    tags: array(string()),
  }),
});
```

## 📁 New Organized Structure

The SDK now features a well-organized directory structure:

```
src/
├── types/           # Type definitions organized by domain
│   ├── core-types.ts      # Fundamental enums and interfaces
│   ├── node-types.ts      # Node-specific definitions
│   ├── field-types.ts     # Field builder interfaces
│   ├── auth-types.ts      # Authentication definitions
│   └── server-types.ts    # Server configuration types
├── builders/        # Builder implementations
│   ├── fields/            # Field builders with comprehensive docs
│   └── auth/              # Authentication builders
├── defines/         # Component definition functions
│   └── index.ts           # Centralized exports
└── server/          # ezwServer implementation
```

### 🎯 Centralized Imports

All components can now be imported from a single entry point:

```typescript
import {
  // Server
  ezwServer,

  // Define functions
  defineNode,
  defineExecutor,
  defineTrigger,

  // Field builders
  string,
  number,
  boolean,
  options,
  object,
  array,

  // Auth builders
  apiKey,
  oauth2,
  basic,

  // Types
  NodeDefinition,
  ExecutionContext,
  Logger,
} from "@ezworkflow/node-sdk";
```

### 🔧 Enhanced Field Builders

Field builders now include comprehensive validation and documentation:

```typescript
// Enhanced string field with validation
const emailField = string()
  .description("User email address")
  .format("email")
  .pattern(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)
  .minLength(5)
  .maxLength(100)
  .required();

// Number field with constraints
const ageField = number()
  .description("User age in years")
  .min(0)
  .max(120)
  .integer()
  .required();

// Secret field for sensitive data
const apiKeyField = string()
  .description("API key for authentication")
  .secret(true)
  .fromEnv("API_KEY")
  .required();
```

## API Reference

### Types

- `DataType`: Enum of supported data types (string, number, boolean, etc.)
- `FieldType`: Enum of supported field types (text, number, select, etc.)
- `NodeDefinition`: Type for defining a node's structure
- `ExecutionContext`: Context provided to node executors
- `ExecutionResult`: Result returned from node executors
- `TriggerDefinition`: Type for defining a trigger's structure

### Field Builders

- `string()`: Create a string field
- `number()`: Create a number field
- `boolean()`: Create a boolean field
- `options()`: Create a select field
- `object()`: Create an object field
- `array()`: Create an array field
- `stream()`: Create a stream field

### Core Functions

- `defineNode()`: Define a node
- `defineExecutor()`: Define a node executor
- `defineTrigger()`: Define a trigger

## Configuration Access

The SDK provides utilities to read ezWorkflow project configuration:

```typescript
import {
  getConfig,
  hasConfig,
  ConfigNotFoundError,
} from "@ezworkflow/node-sdk";

// Check if ezWorkflow is configured in the current project
if (hasConfig()) {
  const config = getConfig();
  console.log("Runtime:", config.runtime);
  console.log("Base directory:", config.baseDir);
  console.log("API Key:", config.apiKey);
} else {
  console.log("ezWorkflow not initialized in this project");
}

// Handle configuration errors
try {
  const config = getConfig();
  // Use configuration...
} catch (error) {
  if (error instanceof ConfigNotFoundError) {
    console.error("ezWorkflow configuration not found");
  } else {
    console.error("Invalid configuration:", error.message);
  }
}
```

For more details about configuration handling, see the [@ezworkflow/project-config](../project-config/README.md) package documentation.

## License

MIT
