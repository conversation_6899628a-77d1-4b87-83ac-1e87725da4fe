{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "strict": true, "declaration": true, "outDir": "dist", "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@/defines": ["src/defines"], "@/types": ["src/types"], "@/builders": ["src/builders"], "@/server": ["src/server"], "@/utils": ["src/utils"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist", "**/*.test.ts"]}