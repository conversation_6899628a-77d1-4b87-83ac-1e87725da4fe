{"name": "@ezworkflow/node-sdk", "version": "0.1.0", "description": "SDK for creating and exposing workflow nodes for the ezWorkflow platform", "main": "dist/index.js", "module": "dist/index.mjs", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"build": "tsup", "dev": "tsup --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src --ext .ts", "clean": "<PERSON><PERSON><PERSON> dist"}, "keywords": ["workflow", "node", "sdk", "ezworkflow"], "author": "ezWorkflow Team", "license": "MIT", "dependencies": {"@ezworkflow/project-config": "workspace:*", "@hono/node-ws": "^1.1.4", "@hono/zod-validator": "^0.7.0", "hono": "^4.7.10", "uuid": "^11.1.0", "zod": "^3.25.42"}, "devDependencies": {"@types/jest": "^29.5.0", "@types/node": "^22.15.29", "@types/supertest": "^6.0.3", "@types/uuid": "^10.0.0", "eslint": "^9.28.0", "jest": "^29.7.0", "jest-environment-node": "^29.7.0", "rimraf": "^6.0.1", "supertest": "^7.1.1", "ts-jest": "^29.1.0", "tsup": "^8.5.0", "typescript": "^5.8.3"}, "engines": {"node": ">=18.0.0"}}