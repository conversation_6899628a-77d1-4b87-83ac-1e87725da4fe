/**
 * @file Options field builder implementation
 * 
 * This module provides the options field builder for dropdown/select fields.
 * 
 * @since 0.1.0
 */

import { FieldType } from '@/types/core-types';
import { OptionsFieldBuilder } from '@/types/field-types';
import { BaseFieldBuilder } from './base-field-builder';

/**
 * Options field builder implementation
 * 
 * Provides methods for creating dropdown/select fields with predefined options.
 * 
 * @class OptionsFieldBuilderImpl
 * @extends BaseFieldBuilder<string | string[]>
 * @implements OptionsFieldBuilder
 * @since 0.1.0
 */
export class OptionsFieldBuilderImpl extends BaseFieldBuilder<string | string[]> implements OptionsFieldBuilder {
  constructor(options?: Array<{ label: string; value: string; icon?: string }>) {
    super(FieldType.SELECT);
    if (options) {
      this.options(options);
    }
  }

  /**
   * Set the available options
   * 
   * @param options - Array of option objects with label, value, and optional icon
   * @returns This builder instance for method chaining
   * @since 0.1.0
   */
  options(options: Array<{ label: string; value: string; icon?: string }>): this {
    this.metadata({ options });
    return this;
  }

  /**
   * Enable multiple selection
   * 
   * @param value - Whether to allow multiple selections (default: true)
   * @returns This builder instance for method chaining
   * @since 0.1.0
   */
  multiple(value = true): this {
    if (value) {
      this.definition.type = FieldType.MULTISELECT;
    } else {
      this.definition.type = FieldType.SELECT;
    }
    return this;
  }

  /**
   * Load options from environment variable
   * 
   * @param envName - Name of the environment variable
   * @returns This builder instance for method chaining
   * @since 0.1.0
   */
  fromEnv(envName: string): this {
    this.metadata({ fromEnv: envName });
    return this;
  }
}
