/**
 * @file File field builder implementation
 * 
 * This module provides the file field builder for file upload fields.
 * 
 * @since 0.1.0
 */

import { DataType } from '@/types/core-types';
import { FileFieldBuilder } from '@/types/field-types';
import { BaseFieldBuilder } from './base-field-builder';

/**
 * File field builder implementation
 * 
 * Provides methods for creating file upload fields with validation.
 * 
 * @class FileFieldBuilderImpl
 * @extends BaseFieldBuilder<unknown>
 * @implements FileFieldBuilder
 * @since 0.1.0
 */
export class FileFieldBuilderImpl extends BaseFieldBuilder<unknown> implements FileFieldBuilder {
  constructor() {
    super(DataType.FILE);
  }

  /**
   * Set accepted MIME types
   * 
   * @param mimeTypes - Array of accepted MIME types
   * @returns This builder instance for method chaining
   * @since 0.1.0
   */
  accept(mimeTypes: string[]): this {
    this.metadata({ accept: mimeTypes });
    return this;
  }

  /**
   * Set maximum file size in bytes
   * 
   * @param sizeInBytes - Maximum file size
   * @returns This builder instance for method chaining
   * @since 0.1.0
   */
  maxSize(sizeInBytes: number): this {
    this.metadata({ maxSize: sizeInBytes });
    return this;
  }

  /**
   * Enable multiple file selection
   * 
   * @param value - Whether to allow multiple files (default: true)
   * @returns This builder instance for method chaining
   * @since 0.1.0
   */
  multiple(value = true): this {
    this.metadata({ multiple: value });
    return this;
  }
}
