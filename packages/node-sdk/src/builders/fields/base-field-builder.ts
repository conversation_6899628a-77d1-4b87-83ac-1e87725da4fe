/**
 * @file Base field builder implementation
 * 
 * This module provides the base implementation for all field builders,
 * containing common functionality shared across all field types.
 * 
 * @since 0.1.0
 */

import { DataType, FieldType } from '@/types/core-types';
import { FieldDefinition } from '@/types/node-types';
import { FieldBuilder } from '@/types/field-types';

/**
 * Base field builder implementation
 * 
 * Provides the common functionality shared by all field builders, including
 * validation, metadata management, and definition generation.
 * 
 * @class BaseFieldBuilder
 * @template T - The TypeScript type of the field value
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * // This class is typically extended by specific field builders
 * class CustomFieldBuilder extends BaseFieldBuilder<string> {
 *   constructor() {
 *     super(DataType.STRING);
 *   }
 *   
 *   customMethod(value: string): this {
 *     this.metadata({ customProperty: value });
 *     return this;
 *   }
 * }
 * ```
 */
export class BaseFieldBuilder<T> implements FieldBuilder<T> {
  protected definition: FieldDefinition;

  /**
   * Create a new base field builder
   * 
   * @param type - The data or field type for this builder
   * @since 0.1.0
   */
  constructor(type: DataType | FieldType) {
    this.definition = {
      type,
      required: false,
    };
  }

  /**
   * Set the field description
   * 
   * Provides a human-readable description that explains what this field
   * represents and how it should be used.
   * 
   * @param desc - The description text
   * @returns This builder instance for method chaining
   * @since 0.1.0
   * 
   * @example
   * ```typescript
   * const field = string()
   *   .description('The user\'s email address for notifications');
   * ```
   */
  description(desc: string): this {
    this.definition.description = desc;
    return this;
  }

  /**
   * Set the default value for the field
   * 
   * Specifies a default value that will be used when no value is provided.
   * The default value should match the field's expected type.
   * 
   * @param value - The default value
   * @returns This builder instance for method chaining
   * @since 0.1.0
   * 
   * @example
   * ```typescript
   * const field = string()
   *   .default('Hello, World!')
   *   .description('Greeting message');
   * ```
   */
  default(value: T): this {
    this.definition.default = value;
    return this;
  }

  /**
   * Mark the field as required
   * 
   * Required fields must have a value provided and cannot be left empty.
   * The platform will validate that required fields are filled before
   * allowing workflow execution.
   * 
   * @returns This builder instance for method chaining
   * @since 0.1.0
   * 
   * @example
   * ```typescript
   * const field = string()
   *   .description('API endpoint URL')
   *   .required();
   * ```
   */
  required(): this {
    this.definition.required = true;
    return this;
  }

  /**
   * Mark the field as optional
   * 
   * Optional fields can be left empty and will use their default value
   * (if specified) or undefined when not provided.
   * 
   * @returns This builder instance for method chaining
   * @since 0.1.0
   * 
   * @example
   * ```typescript
   * const field = string()
   *   .description('Optional description text')
   *   .optional();
   * ```
   */
  optional(): this {
    this.definition.required = false;
    return this;
  }

  /**
   * Set conditional display logic
   * 
   * Allows the field to be shown or hidden based on the values of other
   * fields. The condition function receives the parent object and inputs
   * and should return true to show the field or false to hide it.
   * 
   * @param condition - Function that determines field visibility
   * @returns This builder instance for method chaining
   * @since 0.1.0
   * 
   * @example
   * ```typescript
   * const field = string()
   *   .description('API key for authentication')
   *   .showIf((parent, inputs) => parent.authType === 'api-key');
   * ```
   */
  showIf(condition: (parent: Record<string, unknown>, inputs?: Record<string, unknown>) => boolean): this {
    this.definition.showIf = condition;
    return this;
  }

  /**
   * Set the field group for UI organization
   * 
   * Groups related fields together in the user interface for better
   * organization and user experience.
   * 
   * @param name - The group name
   * @returns This builder instance for method chaining
   * @since 0.1.0
   * 
   * @example
   * ```typescript
   * const field = string()
   *   .description('Database host')
   *   .group('Database Configuration');
   * ```
   */
  group(name: string): this {
    this.definition.group = name;
    return this;
  }

  /**
   * Make the field group collapsible
   * 
   * When enabled, the field group can be collapsed/expanded in the UI
   * to save space and improve organization.
   * 
   * @param value - Whether the group should be collapsible (default: true)
   * @returns This builder instance for method chaining
   * @since 0.1.0
   * 
   * @example
   * ```typescript
   * const field = string()
   *   .group('Advanced Options')
   *   .collapsible(true);
   * ```
   */
  collapsible(value = true): this {
    this.definition.collapsible = value;
    return this;
  }

  /**
   * Add custom metadata to the field
   * 
   * Allows adding arbitrary metadata that can be used by custom validators,
   * transformers, or UI components. Metadata is merged with existing metadata.
   * 
   * @param data - The metadata object to add
   * @returns This builder instance for method chaining
   * @since 0.1.0
   * 
   * @example
   * ```typescript
   * const field = string()
   *   .metadata({
   *     placeholder: 'Enter your name',
   *     helpText: 'This will be displayed publicly'
   *   });
   * ```
   */
  metadata(data: Record<string, unknown>): this {
    this.definition.metadata = { ...(this.definition.metadata || {}), ...data };
    return this;
  }

  /**
   * Add validation logic to the field
   * 
   * Provides custom validation that runs when the field value changes.
   * The validator can return void (valid), a string (error message),
   * or a boolean (true = valid, false = invalid).
   * 
   * @param validator - The validation function
   * @returns This builder instance for method chaining
   * @since 0.1.0
   * 
   * @example
   * ```typescript
   * const field = string()
   *   .validate((value) => {
   *     if (value.length < 3) {
   *       return 'Must be at least 3 characters long';
   *     }
   *   });
   * ```
   */
  validate(validator: (value: T) => void | string | boolean): this {
    this.metadata({ validator });
    return this;
  }

  /**
   * Add transformation logic to the field
   * 
   * Provides custom transformation that runs on the field value before
   * it's used. This can be used to format, normalize, or convert values.
   * 
   * @param transformer - The transformation function
   * @returns This builder instance for method chaining
   * @since 0.1.0
   * 
   * @example
   * ```typescript
   * const field = string()
   *   .transform((value, inputs) => value.trim().toLowerCase());
   * ```
   */
  transform(transformer: (value: T, inputs?: Record<string, unknown>) => unknown): this {
    this.metadata({ transformer });
    return this;
  }

  /**
   * Get the final field definition
   * 
   * Returns a copy of the field definition with all configured properties.
   * This is typically called internally by the define functions.
   * 
   * @returns The complete field definition
   * @since 0.1.0
   */
  getDefinition(): FieldDefinition {
    return { ...this.definition };
  }
}
