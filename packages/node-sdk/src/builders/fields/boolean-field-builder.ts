/**
 * @file Boolean field builder implementation
 * 
 * This module provides the boolean field builder for checkbox/toggle fields.
 * 
 * @since 0.1.0
 */

import { DataType } from '@/types/core-types';
import { BooleanFieldBuilder } from '@/types/field-types';
import { BaseFieldBuilder } from './base-field-builder';

/**
 * Boolean field builder implementation
 * 
 * Provides methods for creating and configuring boolean/checkbox fields.
 * 
 * @class BooleanFieldBuilderImpl
 * @extends BaseFieldBuilder<boolean>
 * @implements BooleanFieldBuilder
 * @since 0.1.0
 */
export class BooleanFieldBuilderImpl extends BaseFieldBuilder<boolean> implements BooleanFieldBuilder {
  constructor() {
    super(DataType.BOOLEAN);
  }
}
