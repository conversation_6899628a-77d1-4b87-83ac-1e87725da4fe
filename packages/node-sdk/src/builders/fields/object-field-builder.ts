/**
 * @file Object field builder implementation
 * 
 * This module provides the object field builder for complex nested structures.
 * 
 * @since 0.1.0
 */

import { DataType } from '@/types/core-types';
import { FieldDefinition } from '@/types/node-types';
import { ObjectFieldBuilder } from '@/types/field-types';
import { BaseFieldBuilder } from './base-field-builder';

/**
 * Object field builder implementation
 * 
 * Provides methods for creating complex object fields with nested properties.
 * 
 * @class ObjectFieldBuilderImpl
 * @extends BaseFieldBuilder<Record<string, unknown>>
 * @implements ObjectFieldBuilder
 * @since 0.1.0
 */
export class ObjectFieldBuilderImpl extends BaseFieldBuilder<Record<string, unknown>> implements ObjectFieldBuilder {
  constructor(properties?: Record<string, FieldDefinition>) {
    super(DataType.OBJECT);
    if (properties) {
      this.properties(properties);
    }
  }

  /**
   * Define the object properties
   * 
   * @param props - Object defining the nested field structure
   * @returns This builder instance for method chaining
   * @since 0.1.0
   */
  properties(props: Record<string, FieldDefinition>): this {
    this.metadata({ properties: props });
    return this;
  }
}
