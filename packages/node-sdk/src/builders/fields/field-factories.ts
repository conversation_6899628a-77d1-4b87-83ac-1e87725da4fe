/**
 * @file Field factory functions
 *
 * This module provides factory functions for creating field builders.
 * These functions provide a convenient API for creating typed field definitions.
 *
 * @since 0.1.0
 */

import { FieldDefinition } from "@/types/node-types";
import {
  <PERSON><PERSON>ieldBuilder,
  NumberFieldBuilder,
  BooleanFieldBuilder,
  OptionsFieldBuilder,
  ObjectFieldBuilder,
  ArrayFieldBuilder,
  StreamFieldBuilder,
  FileFieldBuilder,
} from "@/types/field-types";

import { StringFieldBuilderImpl } from "./string-field-builder";
import { NumberFieldBuilderImpl } from "./number-field-builder";
import { BooleanFieldBuilderImpl } from "./boolean-field-builder";
import { OptionsFieldBuilderImpl } from "./options-field-builder";
import { ObjectFieldBuilderImpl } from "./object-field-builder";
import { ArrayFieldBuilderImpl } from "./array-field-builder";
import { StreamFieldBuilderImpl } from "./stream-field-builder";
import { FileFieldBuilderImpl } from "./file-field-builder";

/**
 * Create a string field builder
 *
 * Creates a builder for string/text fields with validation and formatting options.
 *
 * @returns A new string field builder instance
 * @since 0.1.0
 *
 * @example
 * ```typescript
 * const nameField = string()
 *   .description('User full name')
 *   .required()
 *   .minLength(2)
 *   .maxLength(100);
 *
 * const emailField = string()
 *   .description('Email address')
 *   .format('email')
 *   .required();
 * ```
 */
export const string = (): StringFieldBuilder => new StringFieldBuilderImpl();

/**
 * Create a number field builder
 *
 * Creates a builder for numeric fields with range validation and type constraints.
 *
 * @returns A new number field builder instance
 * @since 0.1.0
 *
 * @example
 * ```typescript
 * const ageField = number()
 *   .description('Age in years')
 *   .min(0)
 *   .max(120)
 *   .integer()
 *   .required();
 *
 * const priceField = number()
 *   .description('Product price')
 *   .min(0)
 *   .step(0.01);
 * ```
 */
export const number = (): NumberFieldBuilder => new NumberFieldBuilderImpl();

/**
 * Create a boolean field builder
 *
 * Creates a builder for boolean/checkbox fields.
 *
 * @returns A new boolean field builder instance
 * @since 0.1.0
 *
 * @example
 * ```typescript
 * const enabledField = boolean()
 *   .description('Enable this feature')
 *   .default(false);
 *
 * const agreeField = boolean()
 *   .description('I agree to the terms and conditions')
 *   .required();
 * ```
 */
export const boolean = (): BooleanFieldBuilder => new BooleanFieldBuilderImpl();

/**
 * Create an options field builder
 *
 * Creates a builder for dropdown/select fields with predefined options.
 *
 * @param options - Optional array of initial options
 * @returns A new options field builder instance
 * @since 0.1.0
 *
 * @example
 * ```typescript
 * const priorityField = options([
 *   { label: 'Low', value: 'low' },
 *   { label: 'Medium', value: 'medium' },
 *   { label: 'High', value: 'high' }
 * ])
 *   .description('Task priority')
 *   .default('medium');
 *
 * const categoriesField = options()
 *   .description('Product categories')
 *   .multiple(true)
 *   .options([
 *     { label: 'Electronics', value: 'electronics' },
 *     { label: 'Clothing', value: 'clothing' }
 *   ]);
 * ```
 */
export const options = (
  options?: Array<{ label: string; value: string; icon?: string }>
): OptionsFieldBuilder => new OptionsFieldBuilderImpl(options);

/**
 * Create an object field builder
 *
 * Creates a builder for complex object fields with nested properties.
 *
 * @param properties - Optional object defining the nested field structure
 * @returns A new object field builder instance
 * @since 0.1.0
 *
 * @example
 * ```typescript
 * const userField = object({
 *   name: string().required(),
 *   email: string().format('email').required(),
 *   age: number().min(0).optional()
 * })
 *   .description('User information');
 *
 * const addressField = object()
 *   .description('Shipping address')
 *   .properties({
 *     street: string().required(),
 *     city: string().required(),
 *     zipCode: string().pattern(/^\d{5}$/).required()
 *   });
 * ```
 */
export const object = (
  properties?: Record<string, FieldDefinition>
): ObjectFieldBuilder => new ObjectFieldBuilderImpl(properties);

/**
 * Create an array field builder
 *
 * Creates a builder for array/list fields with item type validation.
 *
 * @param itemType - Optional field definition for array items
 * @returns A new array field builder instance
 * @since 0.1.0
 *
 * @example
 * ```typescript
 * const tagsField = array(string())
 *   .description('List of tags')
 *   .minItems(1)
 *   .maxItems(10);
 *
 * const numbersField = array()
 *   .description('List of numbers')
 *   .items(number().min(0))
 *   .required();
 * ```
 */
export const array = (itemType?: FieldDefinition): ArrayFieldBuilder =>
  new ArrayFieldBuilderImpl(itemType);

/**
 * Create a stream field builder
 *
 * Creates a builder for streaming data fields for handling large datasets.
 *
 * @returns A new stream field builder instance
 * @since 0.1.0
 *
 * @example
 * ```typescript
 * const dataStreamField = stream()
 *   .description('Large dataset stream')
 *   .format('application/json')
 *   .chunkSize(1024);
 * ```
 */
export const stream = (): StreamFieldBuilder => new StreamFieldBuilderImpl();

/**
 * Create a file field builder
 *
 * Creates a builder for file upload fields with validation.
 *
 * @returns A new file field builder instance
 * @since 0.1.0
 *
 * @example
 * ```typescript
 * const imageField = file()
 *   .description('Profile image')
 *   .accept(['image/jpeg', 'image/png'])
 *   .maxSize(5 * 1024 * 1024) // 5MB
 *   .required();
 *
 * const documentsField = file()
 *   .description('Supporting documents')
 *   .accept(['application/pdf', 'application/msword'])
 *   .multiple(true)
 *   .optional();
 * ```
 */
export const file = (): FileFieldBuilder => new FileFieldBuilderImpl();
