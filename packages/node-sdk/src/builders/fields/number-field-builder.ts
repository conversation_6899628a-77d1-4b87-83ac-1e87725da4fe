/**
 * @file Number field builder implementation
 * 
 * This module provides the number field builder with validation and
 * formatting options for numeric fields.
 * 
 * @since 0.1.0
 */

import { DataType } from '@/types/core-types';
import { NumberFieldBuilder } from '@/types/field-types';
import { BaseFieldBuilder } from './base-field-builder';

/**
 * Number field builder implementation
 * 
 * Provides methods for creating and configuring numeric fields with
 * range validation, step configuration, and type constraints.
 * 
 * @class NumberFieldBuilderImpl
 * @extends BaseFieldBuilder<number>
 * @implements NumberFieldBuilder
 * @since 0.1.0
 */
export class NumberFieldBuilderImpl extends BaseFieldBuilder<number> implements NumberFieldBuilder {
  constructor() {
    super(DataType.NUMBER);
  }

  /**
   * Set minimum value validation
   * 
   * @param value - Minimum allowed value
   * @returns This builder instance for method chaining
   * @since 0.1.0
   */
  min(value: number): this {
    this.metadata({ min: value });
    return this;
  }

  /**
   * Set maximum value validation
   * 
   * @param value - Maximum allowed value
   * @returns This builder instance for method chaining
   * @since 0.1.0
   */
  max(value: number): this {
    this.metadata({ max: value });
    return this;
  }

  /**
   * Set step value for input controls
   * 
   * @param value - Step increment for numeric inputs
   * @returns This builder instance for method chaining
   * @since 0.1.0
   */
  step(value: number): this {
    this.metadata({ step: value });
    return this;
  }

  /**
   * Restrict to integer values only
   * 
   * @returns This builder instance for method chaining
   * @since 0.1.0
   */
  integer(): this {
    this.metadata({ integer: true });
    return this;
  }

  /**
   * Load value from environment variable
   * 
   * @param envName - Name of the environment variable
   * @returns This builder instance for method chaining
   * @since 0.1.0
   */
  fromEnv(envName: string): this {
    this.metadata({ fromEnv: envName });
    return this;
  }
}
