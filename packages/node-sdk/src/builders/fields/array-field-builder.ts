/**
 * @file Array field builder implementation
 * 
 * This module provides the array field builder for list/collection fields.
 * 
 * @since 0.1.0
 */

import { DataType } from '@/types/core-types';
import { FieldDefinition } from '@/types/node-types';
import { ArrayFieldBuilder } from '@/types/field-types';
import { BaseFieldBuilder } from './base-field-builder';

/**
 * Array field builder implementation
 * 
 * Provides methods for creating array/list fields with item validation.
 * 
 * @class ArrayFieldBuilderImpl
 * @extends BaseFieldBuilder<unknown[]>
 * @implements ArrayFieldBuilder
 * @since 0.1.0
 */
export class ArrayFieldBuilderImpl extends BaseFieldBuilder<unknown[]> implements ArrayFieldBuilder {
  constructor(itemType?: FieldDefinition) {
    super(DataType.ARRAY);
    if (itemType) {
      this.items(itemType);
    }
  }

  /**
   * Define the type of array items
   * 
   * @param itemType - Field definition for array items
   * @returns This builder instance for method chaining
   * @since 0.1.0
   */
  items(itemType: FieldDefinition): this {
    this.metadata({ items: itemType });
    return this;
  }

  /**
   * Set minimum number of items
   * 
   * @param count - Minimum number of items required
   * @returns This builder instance for method chaining
   * @since 0.1.0
   */
  minItems(count: number): this {
    this.metadata({ minItems: count });
    return this;
  }

  /**
   * Set maximum number of items
   * 
   * @param count - Maximum number of items allowed
   * @returns This builder instance for method chaining
   * @since 0.1.0
   */
  maxItems(count: number): this {
    this.metadata({ maxItems: count });
    return this;
  }
}
