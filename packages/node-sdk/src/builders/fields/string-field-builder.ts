/**
 * @file String field builder implementation
 * 
 * This module provides the string field builder with comprehensive validation
 * and formatting options for text-based fields.
 * 
 * @since 0.1.0
 */

import { DataType } from '@/types/core-types';
import { StringFieldBuilder } from '@/types/field-types';
import { BaseFieldBuilder } from './base-field-builder';

/**
 * String field builder implementation
 * 
 * Provides methods for creating and configuring string/text fields with
 * validation rules, formatting options, and security features.
 * 
 * @class StringFieldBuilderImpl
 * @extends BaseFieldBuilder<string>
 * @implements StringFieldBuilder
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * // Create a basic string field
 * const nameField = new StringFieldBuilderImpl()
 *   .description('User full name')
 *   .required()
 *   .minLength(2)
 *   .maxLength(100);
 * 
 * // Create an email field with validation
 * const emailField = new StringFieldBuilderImpl()
 *   .description('Email address')
 *   .format('email')
 *   .pattern(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)
 *   .required();
 * 
 * // Create a password field
 * const passwordField = new StringFieldBuilderImpl()
 *   .description('User password')
 *   .secret(true)
 *   .minLength(8)
 *   .required();
 * ```
 */
export class StringFieldBuilderImpl extends BaseFieldBuilder<string> implements StringFieldBuilder {
  /**
   * Create a new string field builder
   * 
   * @since 0.1.0
   */
  constructor() {
    super(DataType.STRING);
  }

  /**
   * Set minimum length validation
   * 
   * Ensures the string value has at least the specified number of characters.
   * This validation is enforced both in the UI and during execution.
   * 
   * @param length - Minimum number of characters required
   * @returns This builder instance for method chaining
   * @since 0.1.0
   * 
   * @example
   * ```typescript
   * const field = string()
   *   .description('Username')
   *   .minLength(3) // At least 3 characters
   *   .required();
   * ```
   */
  minLength(length: number): this {
    this.metadata({ minLength: length });
    return this;
  }

  /**
   * Set maximum length validation
   * 
   * Ensures the string value does not exceed the specified number of characters.
   * This helps prevent overly long inputs and database field overflow.
   * 
   * @param length - Maximum number of characters allowed
   * @returns This builder instance for method chaining
   * @since 0.1.0
   * 
   * @example
   * ```typescript
   * const field = string()
   *   .description('Tweet content')
   *   .maxLength(280) // Twitter character limit
   *   .multiline(true);
   * ```
   */
  maxLength(length: number): this {
    this.metadata({ maxLength: length });
    return this;
  }

  /**
   * Set regex pattern validation
   * 
   * Validates the string value against a regular expression pattern.
   * Useful for enforcing specific formats like phone numbers, IDs, etc.
   * 
   * @param regex - Regular expression pattern to match
   * @returns This builder instance for method chaining
   * @since 0.1.0
   * 
   * @example
   * ```typescript
   * const field = string()
   *   .description('Phone number')
   *   .pattern(/^\+?[\d\s\-\(\)]+$/) // International phone format
   *   .required();
   * ```
   */
  pattern(regex: RegExp): this {
    this.metadata({ pattern: regex.source });
    return this;
  }

  /**
   * Set string format hint
   * 
   * Provides a hint about the expected format of the string, which can
   * be used by the UI to provide appropriate input controls and validation.
   * 
   * Common formats: 'email', 'url', 'tel', 'date', 'time', 'datetime-local'
   * 
   * @param format - The format identifier
   * @returns This builder instance for method chaining
   * @since 0.1.0
   * 
   * @example
   * ```typescript
   * const emailField = string()
   *   .description('Email address')
   *   .format('email')
   *   .required();
   * 
   * const urlField = string()
   *   .description('Website URL')
   *   .format('url')
   *   .optional();
   * ```
   */
  format(format: string): this {
    this.metadata({ format });
    return this;
  }

  /**
   * Enable multiline text input
   * 
   * When enabled, the UI will render a textarea instead of a single-line
   * input field, allowing for longer text content with line breaks.
   * 
   * @param value - Whether to enable multiline input (default: true)
   * @returns This builder instance for method chaining
   * @since 0.1.0
   * 
   * @example
   * ```typescript
   * const field = string()
   *   .description('Product description')
   *   .multiline(true)
   *   .maxLength(1000)
   *   .optional();
   * ```
   */
  multiline(value = true): this {
    this.metadata({ multiline: value });
    return this;
  }

  /**
   * Mark field as secret/password
   * 
   * When enabled, the field value will be masked in the UI and handled
   * securely. Secret fields are typically used for passwords, API keys,
   * and other sensitive information.
   * 
   * @param value - Whether the field contains secret data (default: true)
   * @returns This builder instance for method chaining
   * @since 0.1.0
   * 
   * @example
   * ```typescript
   * const field = string()
   *   .description('API secret key')
   *   .secret(true)
   *   .required();
   * ```
   */
  secret(value = true): this {
    this.metadata({ secret: value });
    return this;
  }

  /**
   * Load value from environment variable
   * 
   * Allows the field value to be populated from an environment variable.
   * This is useful for configuration values that should not be hardcoded.
   * 
   * @param envName - Name of the environment variable
   * @returns This builder instance for method chaining
   * @since 0.1.0
   * 
   * @example
   * ```typescript
   * const field = string()
   *   .description('Database connection string')
   *   .fromEnv('DATABASE_URL')
   *   .secret(true)
   *   .required();
   * ```
   */
  fromEnv(envName: string): this {
    this.metadata({ fromEnv: envName });
    return this;
  }
}
