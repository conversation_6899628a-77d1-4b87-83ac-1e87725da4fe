/**
 * @file Stream field builder implementation
 * 
 * This module provides the stream field builder for streaming data fields.
 * 
 * @since 0.1.0
 */

import { DataType } from '@/types/core-types';
import { StreamFieldBuilder } from '@/types/field-types';
import { BaseFieldBuilder } from './base-field-builder';

/**
 * Stream field builder implementation
 * 
 * Provides methods for creating streaming data fields for large datasets.
 * 
 * @class StreamFieldBuilderImpl
 * @extends BaseFieldBuilder<unknown>
 * @implements StreamFieldBuilder
 * @since 0.1.0
 */
export class StreamFieldBuilderImpl extends BaseFieldBuilder<unknown> implements StreamFieldBuilder {
  constructor() {
    super(DataType.STREAM);
  }

  /**
   * Set the stream format
   * 
   * @param format - Stream format string or function
   * @returns This builder instance for method chaining
   * @since 0.1.0
   */
  format(format: string | ((inputs: Record<string, unknown>) => string)): this {
    this.metadata({ format });
    return this;
  }

  /**
   * Set the chunk size for streaming
   * 
   * @param size - Chunk size in bytes
   * @returns This builder instance for method chaining
   * @since 0.1.0
   */
  chunkSize(size: number): this {
    this.metadata({ chunkSize: size });
    return this;
  }
}
