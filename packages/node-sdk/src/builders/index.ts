/**
 * @file Centralized exports for all builder classes and functions
 * 
 * This module provides a single entry point for all builder-related functionality,
 * including field builders and authentication builders. It organizes builders
 * into logical groups while maintaining backward compatibility.
 * 
 * @since 0.1.0
 */

// Field builders
export * from './fields';

// Authentication builders
export * from './auth';

// Re-export builder types for convenience
export type {
  FieldBuilder,
  StringFieldBuilder,
  NumberFieldBuilder,
  BooleanFieldBuilder,
  OptionsFieldBuilder,
  ObjectFieldBuilder,
  ArrayFieldBuilder,
  StreamFieldBuilder,
  FileFieldBuilder,
} from '@/types/field-types';

export type {
  AuthBuilder,
  ApiKeyAuthBuilder,
  OAuth2AuthBuilder,
  BasicAuthBuilder,
  BearerAuthBuilder,
  CustomAuthBuilder,
} from '@/types/auth-types';
