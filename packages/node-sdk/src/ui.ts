/**
 * UI Component definition interface
 *
 * This interface defines the structure of a UI component that can be
 * registered with the ezWorkflow platform.
 */
export interface UiComponentDefinition {
    /** Unique identifier for the UI component */
    id: string;
  
    /** Name of the UI component */
    name: string;
  
    /**
     * Menu title for the UI component in different languages
     * The key is the language code (e.g., 'en', 'zh-CN')
     */
    menuTitle: Record<string, string>;
  
    /**
     * Description of the UI component in different languages
     * The key is the language code (e.g., 'en', 'zh-CN')
     */
    description: Record<string, string>;
  
    /** Version of the UI component */
    version: string;
  
    /** Category of the UI component */
    categories: string[];
  
    /** Type of the UI component ('partner' or 'account') */
    type: 'partner' | 'account';
  
    /** Visibility of the UI component ('partner' or 'account') */
    visibility: 'partner' | 'account';
  
    /** Tags for the UI component */
    tags: string[];
  
    /** Icon for the UI component (from iconify) */
    icon?: string;
  
    /** URL for the UI component */
    url: string;
  
    /** Type of screen ('fullPage', 'modal', or 'drawer') */
    screenType?: 'fullPage' | 'modal' | 'drawer';
  
    /** Size of modal or drawer ('small', 'medium', or 'large') */
    modalOrDrawerSize?: 'small' | 'medium' | 'large';
  
    /** Additional metadata for the UI component */
    metadata?: Record<string, unknown>;
  }
  