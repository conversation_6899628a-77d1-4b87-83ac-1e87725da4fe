/**
 * @file Core types for the ezWorkflow node SDK
 */

/**
 * Logger interface for node execution
 */
export interface Logger {
  info: (message: string) => void;
  warn: (message: string) => void;
  error: (message: string) => void;
  debug: (message: string) => void;
}

/**
 * Data types supported by node inputs and outputs
 */
export enum DataType {
  STRING = 'string',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  OBJECT = 'object',
  ARRAY = 'array',
  STREAM = 'stream',
  FILE = 'file',
  ANY = 'any',
}

/**
 * Field types for node configuration
 */
export enum FieldType {
  TEXT = 'text',
  NUMBER = 'number',
  BOOLEAN = 'boolean',
  SELECT = 'select',
  MULTISELECT = 'multiselect',
  CODE = 'code',
  JSON = 'json',
  FILE = 'file',
  COLOR = 'color',
  DATE = 'date',
  DATETIME = 'datetime',
}

/**
 * Trigger types
 */
export enum TriggerType {
  HTTP = 'http',
  SOCKET = 'socket',
}

/**
 * Base field definition
 */
export interface FieldDefinition {
  type: DataType | FieldType;
  description?: string;
  required?: boolean;
  default?: unknown;
  group?: string;
  collapsible?: boolean;
  showIf?: (parent: Record<string, unknown>, inputs?: Record<string, unknown>) => boolean;
  metadata?: Record<string, unknown>;
}

/**
 * Input field definition
 */
export interface InputFieldDefinition extends FieldDefinition {
  // Input-specific properties
}

/**
 * Output field definition
 */
export interface OutputFieldDefinition extends FieldDefinition {
  // Output-specific properties
}

/**
 * Config field definition
 */
export interface ConfigFieldDefinition extends FieldDefinition {
  // Config-specific properties
}

/**
 * Node definition
 */
export interface NodeDefinition {
  id: string;
  name: string;
  description: string;
  version: string;
  category: string | string[]; // Support both single category and array of categories
  tags?: string[];
  inputs: Record<string, InputFieldDefinition>;
  outputs: Record<string, OutputFieldDefinition>;
  config?: Record<string, ConfigFieldDefinition>;
  auth?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
}

/**
 * Execution context provided to node executors
 */
export interface ExecutionContext {
  inputs: Record<string, unknown>;
  config?: Record<string, unknown>;
  auth?: Record<string, unknown>;
  logger: Logger;
}

/**
 * Execution result returned from node executors
 */
export interface ExecutionResult<O = Record<string, unknown>> {
  success: boolean;
  outputs: O;
  error?: string;
  logs?: string[];
}

/**
 * Node executor function type
 */
export type NodeExecutor<O = Record<string, unknown>> = (context: ExecutionContext) => Promise<ExecutionResult<O>>;

/**
 * Complete node with definition and executor
 */
export interface Node {
  definition: NodeDefinition;
  executor?: NodeExecutor;
}

/**
 * Trigger definition
 */
export interface TriggerDefinition {
  id: string; // Added ID field
  name: string;
  version: string;
  categories: string[];
  tags: string[];
  type: TriggerType;
  registarUrl: string;
  expect: Record<string, unknown>;
}

/**
 * Field builder interface
 */
export interface FieldBuilder<T> {
  description: (desc: string) => FieldBuilder<T>;
  default: (value: T) => FieldBuilder<T>;
  required: () => FieldBuilder<T>;
  optional: () => FieldBuilder<T>;
  showIf: (condition: (parent: Record<string, unknown>, inputs?: Record<string, unknown>) => boolean) => FieldBuilder<T>;
  group: (name: string) => FieldBuilder<T>;
  collapsible: (value?: boolean) => FieldBuilder<T>;
  metadata: (data: Record<string, unknown>) => FieldBuilder<T>;
  validate: (validator: (value: T) => void | string | boolean) => FieldBuilder<T>;
  transform: (transformer: (value: T, inputs?: Record<string, unknown>) => unknown) => FieldBuilder<T>;
  getDefinition: () => FieldDefinition;
}

/**
 * String field builder interface
 */
export interface StringFieldBuilder extends FieldBuilder<string> {
  minLength: (length: number) => StringFieldBuilder;
  maxLength: (length: number) => StringFieldBuilder;
  pattern: (regex: RegExp) => StringFieldBuilder;
  format: (format: string) => StringFieldBuilder;
  multiline: (value?: boolean) => StringFieldBuilder;
  secret: (value?: boolean) => StringFieldBuilder;
  fromEnv: (envName: string) => StringFieldBuilder;
}

/**
 * Number field builder interface
 */
export interface NumberFieldBuilder extends FieldBuilder<number> {
  min: (value: number) => NumberFieldBuilder;
  max: (value: number) => NumberFieldBuilder;
  step: (value: number) => NumberFieldBuilder;
  integer: () => NumberFieldBuilder;
  fromEnv: (envName: string) => NumberFieldBuilder;
}

/**
 * Boolean field builder interface
 */
export interface BooleanFieldBuilder extends FieldBuilder<boolean> {
  // Boolean-specific methods
}

/**
 * Options field builder interface
 */
export interface OptionsFieldBuilder extends FieldBuilder<string | string[]> {
  options: (options: Array<{ label: string; value: string; icon?: string }>) => OptionsFieldBuilder;
  multiple: (value?: boolean) => OptionsFieldBuilder;
  fromEnv: (envName: string) => OptionsFieldBuilder;
}

/**
 * Object field builder interface
 */
export interface ObjectFieldBuilder extends FieldBuilder<Record<string, unknown>> {
  properties: (props: Record<string, FieldDefinition>) => ObjectFieldBuilder;
}

/**
 * Array field builder interface
 */
export interface ArrayFieldBuilder extends FieldBuilder<unknown[]> {
  items: (itemType: FieldDefinition) => ArrayFieldBuilder;
  minItems: (count: number) => ArrayFieldBuilder;
  maxItems: (count: number) => ArrayFieldBuilder;
}

/**
 * Stream field builder interface
 */
export interface StreamFieldBuilder extends FieldBuilder<unknown> {
  format: (format: string | ((inputs: Record<string, unknown>) => string)) => StreamFieldBuilder;
  chunkSize: (size: number) => StreamFieldBuilder;
}

/**
 * File field builder interface
 */
export interface FileFieldBuilder extends FieldBuilder<unknown> {
  accept: (mimeTypes: string[]) => FileFieldBuilder;
  maxSize: (sizeInBytes: number) => FileFieldBuilder;
  multiple: (value?: boolean) => FileFieldBuilder;
}

/**
 * Auth types
 */
export enum AuthType {
  API_KEY = 'apiKey',
  OAUTH2 = 'oauth2',
  BASIC = 'basic',
  BEARER = 'bearer',
  CUSTOM = 'custom',
}

/**
 * Auth placement
 */
export enum AuthPlacement {
  HEADER = 'header',
  QUERY = 'query',
  COOKIE = 'cookie',
  BODY = 'body',
}

/**
 * Base auth definition
 */
export interface AuthDefinition {
  type: AuthType;
  description?: string;
  required?: boolean;
  metadata?: Record<string, unknown>;
}

/**
 * API Key auth definition
 */
export interface ApiKeyAuthDefinition extends AuthDefinition {
  type: AuthType.API_KEY;
  provider?: string;
  placement: AuthPlacement;
  name: string;
  prefix?: string;
}

/**
 * OAuth2 auth definition
 */
export interface OAuth2AuthDefinition extends AuthDefinition {
  type: AuthType.OAUTH2;
  provider?: string;
  authorizationUrl: string;
  tokenUrl: string;
  scopes?: string[];
  refreshUrl?: string;
}

/**
 * Basic auth definition
 */
export interface BasicAuthDefinition extends AuthDefinition {
  type: AuthType.BASIC;
  provider?: string;
}

/**
 * Bearer auth definition
 */
export interface BearerAuthDefinition extends AuthDefinition {
  type: AuthType.BEARER;
  provider?: string;
}

/**
 * Custom auth definition
 */
export interface CustomAuthDefinition extends AuthDefinition {
  type: AuthType.CUSTOM;
  provider?: string;
  fields: Record<string, FieldDefinition>;
}

/**
 * Auth builder interface
 */
export interface AuthBuilder<T extends AuthDefinition> {
  description: (desc: string) => AuthBuilder<T>;
  required: () => AuthBuilder<T>;
  optional: () => AuthBuilder<T>;
  provider: (name: string) => AuthBuilder<T>;
  metadata: (data: Record<string, unknown>) => AuthBuilder<T>;
  getDefinition: () => T;
}

/**
 * API Key auth builder interface
 */
export interface ApiKeyAuthBuilder extends AuthBuilder<ApiKeyAuthDefinition> {
  placement: (place: AuthPlacement) => ApiKeyAuthBuilder;
  name: (name: string) => ApiKeyAuthBuilder;
  prefix: (prefix: string) => ApiKeyAuthBuilder;
}

/**
 * OAuth2 auth builder interface
 */
export interface OAuth2AuthBuilder extends AuthBuilder<OAuth2AuthDefinition> {
  authorizationUrl: (url: string) => OAuth2AuthBuilder;
  tokenUrl: (url: string) => OAuth2AuthBuilder;
  scopes: (scopes: string[]) => OAuth2AuthBuilder;
  refreshUrl: (url: string) => OAuth2AuthBuilder;
}

/**
 * Basic auth builder interface
 */
export interface BasicAuthBuilder extends AuthBuilder<BasicAuthDefinition> {
  // Basic auth specific methods
}

/**
 * Bearer auth builder interface
 */
export interface BearerAuthBuilder extends AuthBuilder<BearerAuthDefinition> {
  // Bearer auth specific methods
}

/**
 * Custom auth builder interface
 */
export interface CustomAuthBuilder extends AuthBuilder<CustomAuthDefinition> {
  fields: (fields: Record<string, FieldDefinition>) => CustomAuthBuilder;
}
