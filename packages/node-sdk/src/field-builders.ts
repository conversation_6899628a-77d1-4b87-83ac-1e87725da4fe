/**
 * @file Legacy field builders for backward compatibility
 *
 * This file provides backward compatibility for the original field builder API.
 * It re-exports all field builders from the new organized structure.
 *
 * **For new code, please use the organized imports:**
 * ```typescript
 * import { string, number, boolean } from '@ezworkflow/node-sdk';
 * ```
 *
 * **This legacy import pattern still works:**
 * ```typescript
 * import { string, number, boolean } from '@ezworkflow/node-sdk/field-builders';
 * ```
 *
 * @deprecated Use direct imports from '@ezworkflow/node-sdk' instead
 * @since 0.1.0
 */

// Re-export all field builders from the new organized structure
export * from "@/builders/fields/field-factories";

// Re-export types for convenience
export type {
  FieldBuilder,
  StringFieldBuilder,
  NumberFieldBuilder,
  BooleanFieldBuilder,
  OptionsFieldBuilder,
  ObjectFieldBuilder,
  ArrayFieldBuilder,
  StreamFieldBuilder,
  FileFieldBuilder,
} from "@/types/field-types";

export type { FieldDefinition } from "@/types/node-types";
export { DataType, FieldType } from "@/types/core-types";
