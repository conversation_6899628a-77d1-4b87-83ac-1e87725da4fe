/**
 * Enhanced Logger utility for ezWorkflow
 * 
 * Provides comprehensive logging capabilities with:
 * - Type-safe log entries
 * - Sensitive data filtering
 * - In-memory log storage and retrieval
 * - Multiple log levels
 * - Request-based log grouping
 */

/**
 * Supported log levels
 */
export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

/**
 * Structure of a log entry
 */
export interface LogEntry {
  /** Unique identifier for the request/session */
  requestId: string;
  /** Log level */
  level: LogLevel;
  /** Log message */
  message: string;
  /** Timestamp when the log was created */
  timestamp: Date;
  /** Optional metadata associated with the log */
  metadata?: Record<string, any>;
}

/**
 * Options for retrieving logs
 */
export interface GetLogsOptions {
  /** Filter by specific request ID */
  requestId?: string;
  /** Filter by log level */
  level?: LogLevel;
  /** Filter by minimum log level */
  minLevel?: LogLevel;
  /** Maximum number of logs to return */
  limit?: number;
  /** Return logs from this date onwards */
  since?: Date;
}

/**
 * Configuration for sensitive data filtering
 */
export interface SensitiveDataConfig {
  /** Field names that should be masked */
  sensitiveFields: string[];
  /** Custom patterns to match and mask */
  patterns: RegExp[];
  /** Character to use for masking */
  maskChar: string;
}

/**
 * Enhanced Logger class with comprehensive logging capabilities
 */
export class Logger {
  private static instance: Logger | null = null;
  private static logs: LogEntry[] = [];
  private static readonly LOG_LEVEL_PRIORITY: Record<LogLevel, number> = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3,
  };

  /**
   * Default configuration for sensitive data filtering
   */
  private static readonly DEFAULT_SENSITIVE_CONFIG: SensitiveDataConfig = {
    sensitiveFields: [
      'password',
      'token',
      'secret',
      'key',
      'authorization',
      'auth',
      'bearer',
      'apikey',
      'api_key',
      'access_token',
      'refresh_token',
      'session',
      'cookie',
      'ssn',
      'social_security',
      'credit_card',
      'card_number',
      'cvv',
      'pin',
    ],
    patterns: [
      /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, // Email addresses
      /\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b/g, // Credit card numbers
      /\b\d{3}-\d{2}-\d{4}\b/g, // SSN format
      /Bearer\s+[A-Za-z0-9\-._~+/]+=*/gi, // Bearer tokens
      /sk-[a-zA-Z0-9]{48}/g, // OpenAI API keys
      /xoxb-[0-9]+-[0-9]+-[0-9]+-[a-z0-9]+/g, // Slack bot tokens
    ],
    maskChar: '*',
  };

  /**
   * Creates a new Logger instance for a specific request
   * @param requestId - Unique identifier for the request/session
   */
  constructor(private readonly requestId: string) {}

  /**
   * Gets or creates a singleton instance of the Logger
   * @param requestId - Request ID for the logger instance
   * @returns Logger instance
   */
  public static getInstance(requestId: string): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger(requestId);
    }
    return Logger.instance;
  }

  /**
   * Logs an info level message
   * @param message - The message to log
   * @param metadata - Optional metadata to include with the log
   */
  public info(message: string, metadata?: Record<string, any>): void {
    this.log('info', message, metadata);
  }

  /**
   * Logs a warning level message
   * @param message - The message to log
   * @param metadata - Optional metadata to include with the log
   */
  public warn(message: string, metadata?: Record<string, any>): void {
    this.log('warn', message, metadata);
  }

  /**
   * Logs an error level message
   * @param message - The message to log
   * @param metadata - Optional metadata to include with the log
   */
  public error(message: string, metadata?: Record<string, any>): void {
    this.log('error', message, metadata);
  }

  /**
   * Logs a debug level message
   * @param message - The message to log
   * @param metadata - Optional metadata to include with the log
   */
  public debug(message: string, metadata?: Record<string, any>): void {
    this.log('debug', message, metadata);
  }

  /**
   * Core logging method that handles all log levels
   * @param level - The log level
   * @param message - The message to log
   * @param metadata - Optional metadata to include with the log
   */
  private log(level: LogLevel, message: string, metadata?: Record<string, any>): void {
    // Filter sensitive data from message and metadata
    const sanitizedMessage = this.filterSensitiveData(message);
    const sanitizedMetadata = metadata ? this.filterSensitiveData(JSON.stringify(metadata)) : undefined;

    const logEntry: LogEntry = {
      requestId: this.requestId,
      level,
      message: sanitizedMessage,
      timestamp: new Date(),
      metadata: sanitizedMetadata ? JSON.parse(sanitizedMetadata) : undefined,
    };

    // Store the log entry
    Logger.logs.push(logEntry);

    // Output to console with formatting
    this.outputToConsole(logEntry);
  }

  /**
   * Outputs a log entry to the console with appropriate formatting
   * @param logEntry - The log entry to output
   */
  private outputToConsole(logEntry: LogEntry): void {
    const timestamp = logEntry.timestamp.toISOString();
    const prefix = `[${logEntry.requestId}] [${timestamp}] [${logEntry.level.toUpperCase()}]`;
    const message = `${prefix} ${logEntry.message}`;

    switch (logEntry.level) {
      case 'debug':
        console.debug(message, logEntry.metadata || '');
        break;
      case 'info':
        console.info(message, logEntry.metadata || '');
        break;
      case 'warn':
        console.warn(message, logEntry.metadata || '');
        break;
      case 'error':
        console.error(message, logEntry.metadata || '');
        break;
    }
  }

  /**
   * Filters sensitive data from text content
   * @param content - The content to filter
   * @returns Filtered content with sensitive data masked
   */
  private filterSensitiveData(content: string): string {
    const config = Logger.DEFAULT_SENSITIVE_CONFIG;
    let filteredContent = content;

    // Apply pattern-based filtering
    config.patterns.forEach(pattern => {
      filteredContent = filteredContent.replace(pattern, (match) => 
        config.maskChar.repeat(Math.min(match.length, 8))
      );
    });

    // Apply field-based filtering for JSON-like content
    config.sensitiveFields.forEach(field => {
      const fieldPattern = new RegExp(
        `("${field}"|'${field}'|${field})\\s*[:=]\\s*["']?([^"',\\s}]+)["']?`,
        'gi'
      );
      filteredContent = filteredContent.replace(fieldPattern, (match, fieldName, value) => {
        const maskedValue = config.maskChar.repeat(Math.min(value.length, 8));
        return match.replace(value, maskedValue);
      });
    });

    return filteredContent;
  }

  /**
   * Retrieves logs based on specified criteria
   * @param options - Filtering and pagination options
   * @returns Array of log entries matching the criteria
   */
  public static getLogs(options: GetLogsOptions = {}): LogEntry[] {
    let filteredLogs = [...Logger.logs];

    // Filter by request ID
    if (options.requestId) {
      filteredLogs = filteredLogs.filter(log => log.requestId === options.requestId);
    }

    // Filter by exact log level
    if (options.level) {
      filteredLogs = filteredLogs.filter(log => log.level === options.level);
    }

    // Filter by minimum log level
    if (options.minLevel) {
      const minPriority = Logger.LOG_LEVEL_PRIORITY[options.minLevel];
      filteredLogs = filteredLogs.filter(log => 
        Logger.LOG_LEVEL_PRIORITY[log.level] >= minPriority
      );
    }

    // Filter by date
    if (options.since) {
      filteredLogs = filteredLogs.filter(log => log.timestamp >= options.since!);
    }

    // Sort by timestamp (newest first)
    filteredLogs.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());

    // Apply limit
    if (options.limit && options.limit > 0) {
      filteredLogs = filteredLogs.slice(0, options.limit);
    }

    return filteredLogs;
  }

  /**
   * Gets all logs for the current request ID
   * @returns Array of log entries for this request
   */
  public getRequestLogs(): LogEntry[] {
    return Logger.getLogs({ requestId: this.requestId });
  }

  /**
   * Clears all stored logs
   */
  public static clearLogs(): void {
    Logger.logs = [];
  }

  /**
   * Gets the total number of stored logs
   * @returns Number of logs in storage
   */
  public static getLogCount(): number {
    return Logger.logs.length;
  }

  /**
   * Gets logs grouped by request ID
   * @returns Object with requestId as keys and log arrays as values
   */
  public static getLogsByRequest(): Record<string, LogEntry[]> {
    const grouped: Record<string, LogEntry[]> = {};
    
    Logger.logs.forEach(log => {
      if (!grouped[log.requestId]) {
        grouped[log.requestId] = [];
      }
      grouped[log.requestId].push(log);
    });

    return grouped;
  }

  /**
   * Gets log statistics
   * @returns Object containing various log statistics
   */
  public static getLogStats(): {
    total: number;
    byLevel: Record<LogLevel, number>;
    byRequest: Record<string, number>;
    oldestLog?: Date;
    newestLog?: Date;
  } {
    const stats = {
      total: Logger.logs.length,
      byLevel: { debug: 0, info: 0, warn: 0, error: 0 } as Record<LogLevel, number>,
      byRequest: {} as Record<string, number>,
      oldestLog: undefined as Date | undefined,
      newestLog: undefined as Date | undefined,
    };

    if (Logger.logs.length === 0) {
      return stats;
    }

    Logger.logs.forEach(log => {
      // Count by level
      stats.byLevel[log.level]++;
      
      // Count by request
      stats.byRequest[log.requestId] = (stats.byRequest[log.requestId] || 0) + 1;
    });

    // Find oldest and newest logs
    const sortedLogs = [...Logger.logs].sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());
    stats.oldestLog = sortedLogs[0]?.timestamp;
    stats.newestLog = sortedLogs[sortedLogs.length - 1]?.timestamp;

    return stats;
  }
}