/**
 * @file Registry for all ezWorkflow components
 *
 * This file provides a centralized registry for all ezWorkflow component types:
 * - Nodes: Components that define workflow steps
 * - Executors: Components that implement the logic for nodes
 * - Triggers: Components that initiate workflows
 * - UI Components: Components for the user interface
 */

import { Node, NodeExecutor, TriggerDefinition } from "./types";
import { DashboardWidgetDefinition } from "./defines/dashboard-widget";
import { ModalDefinition } from "./defines/modal";
import { DrawerDefinition } from "./defines/drawer";
import { EmailBuilderBlockDefinition } from "./defines/email-builder-block";
import { PageDefinition } from "./defines/page";
import { FunnelBuilderBlockDefinition } from "./defines/funnel-builder-block";

/**
 * Component types in the ezWorkflow system
 */
export enum ComponentType {
  NODE = "node",
  EXECUTOR = "executor",
  TRIGGER = "trigger",
  DASHBOARD_WIDGET = "dashboard_widget",
  MODAL = "modal",
  DRAWER = "drawer",
  PAGE = "page",
  FUNNEL_BUILDER_BLOCK = "funnel_builder_block",
  EMAIL_BUILDER_BLOCK = "email_builder_block",
  UNKNOWN = "unknown",
}

/**
 * Registry for all ezWorkflow components
 *
 * This class provides methods for registering, retrieving, and managing
 * all types of ezWorkflow components: nodes, executors, triggers, and UI components.
 */
class ComponentRegistry {
  private nodes: Map<string, Node> = new Map();
  private executors: Map<string, NodeExecutor> = new Map();
  private triggers: Map<string, TriggerDefinition> = new Map();
  private dashboardWidgets: Map<string, DashboardWidgetDefinition> = new Map();
  private modals: Map<string, ModalDefinition> = new Map();
  private drawers: Map<string, DrawerDefinition> = new Map();
  private pages: Map<string, PageDefinition> = new Map();
  private funnelBuilderBlocks: Map<string, FunnelBuilderBlockDefinition> =
    new Map();
  private emailBuilderBlocks: Map<string, EmailBuilderBlockDefinition> =
    new Map();

  /**
   * Register a node
   *
   * @param node Node to register
   */
  registerNode(node: Node): void {
    this.nodes.set(node.definition.id, node);
  }

  /**
   * Get a node by ID
   *
   * @param id Node ID
   * @returns Node or undefined if not found
   */
  getNode(id: string): Node | undefined {
    return this.nodes.get(id);
  }

  /**
   * Check if a node exists
   *
   * @param id Node ID
   * @returns Whether the node exists
   */
  hasNode(id: string): boolean {
    return this.nodes.has(id);
  }

  /**
   * Get all nodes
   *
   * @returns All nodes
   */
  getAllNodes(): Node[] {
    return Array.from(this.nodes.values());
  }

  /**
   * Register an executor
   *
   * @param id Executor ID
   * @param executor Executor to register
   */
  registerExecutor(id: string, executor: NodeExecutor): void {
    this.executors.set(id, executor);
  }

  /**
   * Get an executor by ID
   *
   * @param id Executor ID
   * @returns Executor or undefined if not found
   */
  getExecutor(id: string): NodeExecutor | undefined {
    return this.executors.get(id);
  }

  /**
   * Check if an executor exists
   *
   * @param id Executor ID
   * @returns Whether the executor exists
   */
  hasExecutor(id: string): boolean {
    return this.executors.has(id);
  }

  /**
   * Get all executors
   *
   * @returns All executors
   */
  getAllExecutors(): NodeExecutor[] {
    return Array.from(this.executors.values());
  }

  /**
   * Register a trigger
   *
   * @param trigger Trigger to register
   */
  registerTrigger(trigger: TriggerDefinition): void {
    this.triggers.set(trigger.id, trigger);
  }

  /**
   * Get a trigger by ID
   *
   * @param id Trigger ID
   * @returns Trigger or undefined if not found
   */
  getTrigger(id: string): TriggerDefinition | undefined {
    return this.triggers.get(id);
  }

  /**
   * Check if a trigger exists
   *
   * @param id Trigger ID
   * @returns Whether the trigger exists
   */
  hasTrigger(id: string): boolean {
    return this.triggers.has(id);
  }

  /**
   * Get all triggers
   *
   * @returns All triggers
   */
  getAllTriggers(): TriggerDefinition[] {
    return Array.from(this.triggers.values());
  }

  /**
   * Register a dashboard widget
   *
   * @param dashboardWidget Dashboard widget to register
   */
  registerDashboardWidget(dashboardWidget: DashboardWidgetDefinition): void {
    this.dashboardWidgets.set(dashboardWidget.id, dashboardWidget);
  }

  /**
   * Get a dashboard widget by ID
   *
   * @param id Dashboard widget ID
   * @returns Dashboard widget or undefined if not found
   */
  getDashboardWidget(id: string): DashboardWidgetDefinition | undefined {
    return this.dashboardWidgets.get(id);
  }

  /**
   * Check if a dashboard widget exists
   *
   * @param id Dashboard widget ID
   * @returns Whether the dashboard widget exists
   */
  hasDashboardWidget(id: string): boolean {
    return this.dashboardWidgets.has(id);
  }

  /**
   * Get all dashboard widgets
   *
   * @returns All dashboard widgets
   */
  getAllDashboardWidgets(): DashboardWidgetDefinition[] {
    return Array.from(this.dashboardWidgets.values());
  }

  /**
   * Register a modal
   *
   * @param modal Modal to register
   */
  registerModal(modal: ModalDefinition): void {
    this.modals.set(modal.id, modal);
  }

  /**
   * Get a modal by ID
   *
   * @param id Modal ID
   * @returns Modal or undefined if not found
   */
  getModal(id: string): ModalDefinition | undefined {
    return this.modals.get(id);
  }

  /**
   * Check if a modal exists
   *
   * @param id Modal ID
   * @returns Whether the modal exists
   */
  hasModal(id: string): boolean {
    return this.modals.has(id);
  }

  /**
   * Get all modals
   *
   * @returns All modals
   */
  getAllModals(): ModalDefinition[] {
    return Array.from(this.modals.values());
  }

  /**
   * Register a drawer
   *
   * @param drawer Drawer to register
   */
  registerDrawer(drawer: DrawerDefinition): void {
    this.drawers.set(drawer.id, drawer);
  }

  /**
   * Get a drawer by ID
   *
   * @param id Drawer ID
   * @returns Drawer or undefined if not found
   */
  getDrawer(id: string): DrawerDefinition | undefined {
    return this.drawers.get(id);
  }

  /**
   * Get all drawers
   *
   * @returns All drawers
   */
  getAllDrawers(): DrawerDefinition[] {
    return Array.from(this.drawers.values());
  }

  /**
   * Check if a drawer exists
   *
   * @param id Drawer ID
   * @returns Whether the drawer exists
   */
  hasDrawer(id: string): boolean {
    return this.drawers.has(id);
  }

  /**
   * Register a page
   *
   * @param page Page to register
   */
  registerPage(page: PageDefinition): void {
    this.pages.set(page.id, page);
  }

  /**
   * Get a page by ID
   *
   * @param id Page ID
   * @returns Page or undefined if not found
   */
  getPage(id: string): PageDefinition | undefined {
    return this.pages.get(id);
  }

  /**
   * Get all pages
   *
   * @returns All pages
   */
  getAllPages(): PageDefinition[] {
    return Array.from(this.pages.values());
  }

  /**
   * Check if a page exists
   *
   * @param id Page ID
   * @returns Whether the page exists
   */
  hasPage(id: string): boolean {
    return this.pages.has(id);
  }

  /**
   * Register a funnel builder block
   *
   * @param funnelBuilderBlock Funnel builder block to register
   */
  registerFunnelBuilderBlock(
    funnelBuilderBlock: FunnelBuilderBlockDefinition
  ): void {
    this.funnelBuilderBlocks.set(funnelBuilderBlock.id, funnelBuilderBlock);
  }

  /**
   * Get a funnel builder block by ID
   *
   * @param id Funnel builder block ID
   * @returns Funnel builder block or undefined if not found
   */
  getFunnelBuilderBlock(id: string): FunnelBuilderBlockDefinition | undefined {
    return this.funnelBuilderBlocks.get(id);
  }

  /**
   * Get all funnel builder blocks
   *
   * @returns All components
   */
  getAllFunnelBuilderBlocks(): FunnelBuilderBlockDefinition[] {
    return Array.from(this.funnelBuilderBlocks.values());
  }

  /**
   * Check if a funnel builder block exists
   *
   * @param id Funnel builder block ID
   * @returns Whether the funnel builder block exists
   */
  hasFunnelBuilderBlock(id: string): boolean {
    return this.funnelBuilderBlocks.has(id);
  }

  /**
   * Get all components
   *
   * @returns All components
   */
  getAllComponents(): {
    nodes: Node[];
    executors: NodeExecutor[];
    triggers: TriggerDefinition[];
    dashboardWidgets: DashboardWidgetDefinition[];
    modals: ModalDefinition[];
    drawers: DrawerDefinition[];
    pages: PageDefinition[];
    funnelBuilderBlocks: FunnelBuilderBlockDefinition[];
    emailBuilderBlocks: EmailBuilderBlockDefinition[];
  } {
    return {
      nodes: this.getAllNodes(),
      executors: this.getAllExecutors(),
      triggers: this.getAllTriggers(),
      dashboardWidgets: this.getAllDashboardWidgets(),
      modals: this.getAllModals(),
      drawers: this.getAllDrawers(),
      pages: this.getAllPages(),
      funnelBuilderBlocks: this.getAllFunnelBuilderBlocks(),
      emailBuilderBlocks: this.getAllEmailBuilderBlocks(),
    };
  }

  /**
   * Register an email builder block
   *
   * @param emailBuilderBlock Email builder block to register
   */
  registerEmailBuilderBlock(
    emailBuilderBlock: EmailBuilderBlockDefinition
  ): void {
    this.emailBuilderBlocks.set(emailBuilderBlock.id, emailBuilderBlock);
  }

  /**
   * Get an email builder block by ID
   *
   * @param id Email builder block ID
   * @returns Email builder block or undefined if not found
   */
  getEmailBuilderBlock(id: string): EmailBuilderBlockDefinition | undefined {
    return this.emailBuilderBlocks.get(id);
  }

  /**
   * Get all email builder blocks
   *
   * @returns All email builder blocks
   */
  getAllEmailBuilderBlocks(): EmailBuilderBlockDefinition[] {
    return Array.from(this.emailBuilderBlocks.values());
  }

  /**
   * Check if an email builder block exists
   *
   * @param id Email builder block ID
   * @returns Whether the email builder block exists
   */
  hasEmailBuilderBlock(id: string): boolean {
    return this.emailBuilderBlocks.has(id);
  }

  /**
   * Get a component by ID regardless of its type
   *
   * @param id Component ID
   * @returns The component if found, or undefined if not found
   */
  getComponent(
    id: string
  ):
    | Node
    | NodeExecutor
    | TriggerDefinition
    | DashboardWidgetDefinition
    | ModalDefinition
    | DrawerDefinition
    | PageDefinition
    | FunnelBuilderBlockDefinition
    | EmailBuilderBlockDefinition
    | undefined {
    // Check each registry in order
    return (
      this.getNode(id) ||
      this.getExecutor(id) ||
      this.getTrigger(id) ||
      this.getDashboardWidget(id) ||
      this.getModal(id) ||
      this.getDrawer(id) ||
      this.getPage(id) ||
      this.getFunnelBuilderBlock(id) ||
      this.getEmailBuilderBlock(id)
    );
  }

  /**
   * Get the type of a component by its ID
   *
   * @param id Component ID
   * @returns The component type, or UNKNOWN if not found
   */
  getComponentType(id: string): ComponentType {
    if (this.hasNode(id)) {
      return ComponentType.NODE;
    } else if (this.hasExecutor(id)) {
      return ComponentType.EXECUTOR;
    } else if (this.hasTrigger(id)) {
      return ComponentType.TRIGGER;
    } else if (this.hasDashboardWidget(id)) {
      return ComponentType.DASHBOARD_WIDGET;
    } else if (this.hasModal(id)) {
      return ComponentType.MODAL;
    } else if (this.hasDrawer(id)) {
      return ComponentType.DRAWER;
    } else if (this.hasPage(id)) {
      return ComponentType.PAGE;
    } else if (this.hasFunnelBuilderBlock(id)) {
      return ComponentType.FUNNEL_BUILDER_BLOCK;
    } else if (this.hasEmailBuilderBlock(id)) {
      return ComponentType.EMAIL_BUILDER_BLOCK;
    } else {
      return ComponentType.UNKNOWN;
    }
  }

  /**
   * Get the count of components by type
   *
   * @returns Object containing the count of each component type
   */
  getComponentCounts(): Record<ComponentType, number> {
    return {
      [ComponentType.NODE]: this.nodes.size,
      [ComponentType.EXECUTOR]: this.executors.size,
      [ComponentType.TRIGGER]: this.triggers.size,
      [ComponentType.DASHBOARD_WIDGET]: this.dashboardWidgets.size,
      [ComponentType.MODAL]: this.modals.size,
      [ComponentType.DRAWER]: this.drawers.size,
      [ComponentType.PAGE]: this.pages.size,
      [ComponentType.FUNNEL_BUILDER_BLOCK]: this.funnelBuilderBlocks.size,
      [ComponentType.EMAIL_BUILDER_BLOCK]: this.emailBuilderBlocks.size,
      [ComponentType.UNKNOWN]: 0,
    };
  }

  /**
   * Clear components of a specific type from the registry
   *
   * @param type The type of components to clear
   */
  clearByType(type: ComponentType): void {
    switch (type) {
      case ComponentType.NODE:
        this.nodes.clear();
        break;
      case ComponentType.EXECUTOR:
        this.executors.clear();
        break;
      case ComponentType.TRIGGER:
        this.triggers.clear();
        break;
      case ComponentType.DASHBOARD_WIDGET:
        this.dashboardWidgets.clear();
        break;
      case ComponentType.MODAL:
        this.modals.clear();
        break;
      case ComponentType.DRAWER:
        this.drawers.clear();
        break;
      case ComponentType.PAGE:
        this.pages.clear();
        break;
      case ComponentType.FUNNEL_BUILDER_BLOCK:
        this.funnelBuilderBlocks.clear();
        break;
      case ComponentType.EMAIL_BUILDER_BLOCK:
        this.emailBuilderBlocks.clear();
        break;
      default:
        // Do nothing for UNKNOWN type
        break;
    }
  }

  /**
   * Clear all components from the registry
   */
  clear(): void {
    this.nodes.clear();
    this.executors.clear();
    this.triggers.clear();
    this.dashboardWidgets.clear();
    this.modals.clear();
    this.drawers.clear();
    this.pages.clear();
    this.funnelBuilderBlocks.clear();
    this.emailBuilderBlocks.clear();
  }
}

/**
 * Global component registry
 */
export const componentRegistry = new ComponentRegistry();

/**
 * @deprecated Use componentRegistry instead
 * Maintained for backward compatibility
 */
export const nodeRegistry = componentRegistry;
