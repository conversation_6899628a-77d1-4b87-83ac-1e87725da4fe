/**
 * @file Authentication type definitions for the ezWorkflow Node SDK
 * 
 * This module contains type definitions for authentication mechanisms,
 * including auth builders and various authentication method definitions.
 * 
 * @since 0.1.0
 */

import { FieldDefinition } from './node-types';

/**
 * Authentication types supported by the platform
 * 
 * Defines the different authentication mechanisms that nodes can use
 * to access external services and APIs.
 * 
 * @enum AuthType
 * @since 0.1.0
 */
export enum AuthType {
  /** API Key authentication */
  API_KEY = 'apiKey',
  /** OAuth 2.0 authentication */
  OAUTH2 = 'oauth2',
  /** HTTP Basic authentication */
  BASIC = 'basic',
  /** Bearer token authentication */
  BEARER = 'bearer',
  /** Custom authentication method */
  CUSTOM = 'custom',
}

/**
 * Authentication placement options
 * 
 * Defines where authentication credentials can be placed in HTTP requests.
 * 
 * @enum AuthPlacement
 * @since 0.1.0
 */
export enum AuthPlacement {
  /** HTTP header */
  HEADER = 'header',
  /** URL query parameter */
  QUERY = 'query',
  /** HTTP cookie */
  COOKIE = 'cookie',
  /** Request body */
  BODY = 'body',
}

/**
 * Base authentication definition
 * 
 * Common properties shared by all authentication types.
 * 
 * @interface AuthDefinition
 * @since 0.1.0
 */
export interface AuthDefinition {
  /** Type of authentication */
  type: AuthType;
  /** Human-readable description */
  description?: string;
  /** Whether authentication is required */
  required?: boolean;
  /** Additional metadata */
  metadata?: Record<string, unknown>;
}

/**
 * API Key authentication definition
 * 
 * Defines API key authentication with placement and naming options.
 * 
 * @interface ApiKeyAuthDefinition
 * @extends AuthDefinition
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const apiKeyAuth: ApiKeyAuthDefinition = {
 *   type: AuthType.API_KEY,
 *   description: 'API key for service authentication',
 *   placement: AuthPlacement.HEADER,
 *   name: 'X-API-Key',
 *   prefix: 'Bearer',
 *   required: true
 * };
 * ```
 */
export interface ApiKeyAuthDefinition extends AuthDefinition {
  type: AuthType.API_KEY;
  /** Service provider name */
  provider?: string;
  /** Where to place the API key */
  placement: AuthPlacement;
  /** Name of the header/parameter/field */
  name: string;
  /** Optional prefix for the key value */
  prefix?: string;
}

/**
 * OAuth 2.0 authentication definition
 * 
 * Defines OAuth 2.0 authentication with authorization and token URLs.
 * 
 * @interface OAuth2AuthDefinition
 * @extends AuthDefinition
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const oauth2Auth: OAuth2AuthDefinition = {
 *   type: AuthType.OAUTH2,
 *   description: 'OAuth 2.0 authentication',
 *   authorizationUrl: 'https://api.example.com/oauth/authorize',
 *   tokenUrl: 'https://api.example.com/oauth/token',
 *   scopes: ['read', 'write'],
 *   refreshUrl: 'https://api.example.com/oauth/refresh'
 * };
 * ```
 */
export interface OAuth2AuthDefinition extends AuthDefinition {
  type: AuthType.OAUTH2;
  /** Service provider name */
  provider?: string;
  /** OAuth authorization URL */
  authorizationUrl: string;
  /** OAuth token URL */
  tokenUrl: string;
  /** Required OAuth scopes */
  scopes?: string[];
  /** OAuth refresh token URL */
  refreshUrl?: string;
}

/**
 * HTTP Basic authentication definition
 * 
 * Defines HTTP Basic authentication (username/password).
 * 
 * @interface BasicAuthDefinition
 * @extends AuthDefinition
 * @since 0.1.0
 */
export interface BasicAuthDefinition extends AuthDefinition {
  type: AuthType.BASIC;
  /** Service provider name */
  provider?: string;
}

/**
 * Bearer token authentication definition
 * 
 * Defines Bearer token authentication.
 * 
 * @interface BearerAuthDefinition
 * @extends AuthDefinition
 * @since 0.1.0
 */
export interface BearerAuthDefinition extends AuthDefinition {
  type: AuthType.BEARER;
  /** Service provider name */
  provider?: string;
}

/**
 * Custom authentication definition
 * 
 * Defines custom authentication with configurable fields.
 * 
 * @interface CustomAuthDefinition
 * @extends AuthDefinition
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const customAuth: CustomAuthDefinition = {
 *   type: AuthType.CUSTOM,
 *   description: 'Custom API authentication',
 *   fields: {
 *     clientId: {
 *       type: DataType.STRING,
 *       description: 'Client ID',
 *       required: true
 *     },
 *     clientSecret: {
 *       type: DataType.STRING,
 *       description: 'Client Secret',
 *       required: true,
 *       metadata: { secret: true }
 *     }
 *   }
 * };
 * ```
 */
export interface CustomAuthDefinition extends AuthDefinition {
  type: AuthType.CUSTOM;
  /** Service provider name */
  provider?: string;
  /** Custom authentication fields */
  fields: Record<string, FieldDefinition>;
}

/**
 * Base authentication builder interface
 * 
 * Provides common methods for all authentication builders.
 * 
 * @interface AuthBuilder
 * @template T - The authentication definition type
 * @since 0.1.0
 */
export interface AuthBuilder<T extends AuthDefinition> {
  /** Set the authentication description */
  description: (desc: string) => AuthBuilder<T>;
  /** Mark authentication as required */
  required: () => AuthBuilder<T>;
  /** Mark authentication as optional */
  optional: () => AuthBuilder<T>;
  /** Set the service provider name */
  provider: (name: string) => AuthBuilder<T>;
  /** Add custom metadata */
  metadata: (data: Record<string, unknown>) => AuthBuilder<T>;
  /** Get the final authentication definition */
  getDefinition: () => T;
}

/**
 * API Key authentication builder interface
 * 
 * Provides methods specific to API key authentication configuration.
 * 
 * @interface ApiKeyAuthBuilder
 * @extends AuthBuilder<ApiKeyAuthDefinition>
 * @since 0.1.0
 */
export interface ApiKeyAuthBuilder extends AuthBuilder<ApiKeyAuthDefinition> {
  /** Set where to place the API key */
  placement: (place: AuthPlacement) => ApiKeyAuthBuilder;
  /** Set the name of the header/parameter */
  name: (name: string) => ApiKeyAuthBuilder;
  /** Set an optional prefix for the key value */
  prefix: (prefix: string) => ApiKeyAuthBuilder;
}

/**
 * OAuth 2.0 authentication builder interface
 * 
 * Provides methods specific to OAuth 2.0 authentication configuration.
 * 
 * @interface OAuth2AuthBuilder
 * @extends AuthBuilder<OAuth2AuthDefinition>
 * @since 0.1.0
 */
export interface OAuth2AuthBuilder extends AuthBuilder<OAuth2AuthDefinition> {
  /** Set the authorization URL */
  authorizationUrl: (url: string) => OAuth2AuthBuilder;
  /** Set the token URL */
  tokenUrl: (url: string) => OAuth2AuthBuilder;
  /** Set the required scopes */
  scopes: (scopes: string[]) => OAuth2AuthBuilder;
  /** Set the refresh token URL */
  refreshUrl: (url: string) => OAuth2AuthBuilder;
}

/**
 * Basic authentication builder interface
 * 
 * Provides methods specific to HTTP Basic authentication configuration.
 * 
 * @interface BasicAuthBuilder
 * @extends AuthBuilder<BasicAuthDefinition>
 * @since 0.1.0
 */
export interface BasicAuthBuilder extends AuthBuilder<BasicAuthDefinition> {
  // Basic auth specific methods can be added here in the future
}

/**
 * Bearer authentication builder interface
 * 
 * Provides methods specific to Bearer token authentication configuration.
 * 
 * @interface BearerAuthBuilder
 * @extends AuthBuilder<BearerAuthDefinition>
 * @since 0.1.0
 */
export interface BearerAuthBuilder extends AuthBuilder<BearerAuthDefinition> {
  // Bearer auth specific methods can be added here in the future
}

/**
 * Custom authentication builder interface
 * 
 * Provides methods specific to custom authentication configuration.
 * 
 * @interface CustomAuthBuilder
 * @extends AuthBuilder<CustomAuthDefinition>
 * @since 0.1.0
 */
export interface CustomAuthBuilder extends AuthBuilder<CustomAuthDefinition> {
  /** Define custom authentication fields */
  fields: (fields: Record<string, FieldDefinition>) => CustomAuthBuilder;
}
