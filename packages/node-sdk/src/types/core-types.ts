/**
 * @file Core types and enums for the ezWorkflow Node SDK
 * 
 * This module contains fundamental types and enumerations that are used throughout
 * the ezWorkflow Node SDK. These include data types, field types, trigger types,
 * and basic interfaces that form the foundation of the SDK.
 * 
 * @since 0.1.0
 */

/**
 * Logger interface for node execution
 * 
 * Provides standardized logging methods for nodes during execution.
 * All log messages are captured and can be returned as part of the execution result.
 * 
 * @interface Logger
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * // Using the logger in a node executor
 * export default defineExecutor(myNode, async ({ inputs, logger }) => {
 *   logger.info('Starting node execution');
 *   logger.debug(`Processing input: ${inputs.data}`);
 *   
 *   try {
 *     // ... node logic
 *     logger.info('Node execution completed successfully');
 *   } catch (error) {
 *     logger.error(`Node execution failed: ${error.message}`);
 *   }
 * });
 * ```
 */
export interface Logger {
  /** Log informational messages */
  info: (message: string) => void;
  /** Log warning messages */
  warn: (message: string) => void;
  /** Log error messages */
  error: (message: string) => void;
  /** Log debug messages (typically only shown in development) */
  debug: (message: string) => void;
}

/**
 * Data types supported by node inputs and outputs
 * 
 * These types define the fundamental data structures that can be passed
 * between nodes in a workflow. Each type has specific validation and
 * serialization behavior.
 * 
 * @enum DataType
 * @since 0.1.0
 */
export enum DataType {
  /** String/text data */
  STRING = 'string',
  /** Numeric data (integers and floats) */
  NUMBER = 'number',
  /** Boolean true/false values */
  BOOLEAN = 'boolean',
  /** Complex object data structures */
  OBJECT = 'object',
  /** Array/list data structures */
  ARRAY = 'array',
  /** Streaming data (for large datasets) */
  STREAM = 'stream',
  /** File data with metadata */
  FILE = 'file',
  /** Any type (no validation) */
  ANY = 'any',
}

/**
 * Field types for node configuration and UI components
 * 
 * These types define how fields are rendered and validated in the
 * ezWorkflow platform's user interface. Each type corresponds to
 * a specific UI component with its own behavior and validation rules.
 * 
 * @enum FieldType
 * @since 0.1.0
 */
export enum FieldType {
  /** Single-line text input */
  TEXT = 'text',
  /** Numeric input with validation */
  NUMBER = 'number',
  /** Checkbox or toggle input */
  BOOLEAN = 'boolean',
  /** Dropdown selection (single choice) */
  SELECT = 'select',
  /** Multi-select dropdown (multiple choices) */
  MULTISELECT = 'multiselect',
  /** Code editor with syntax highlighting */
  CODE = 'code',
  /** JSON editor with validation */
  JSON = 'json',
  /** File upload input */
  FILE = 'file',
  /** Color picker input */
  COLOR = 'color',
  /** Date picker input */
  DATE = 'date',
  /** Date and time picker input */
  DATETIME = 'datetime',
}

/**
 * Trigger types supported by the platform
 * 
 * Defines the different ways that workflows can be triggered to execute.
 * Each type has specific configuration requirements and behavior.
 * 
 * @enum TriggerType
 * @since 0.1.0
 */
export enum TriggerType {
  /** HTTP webhook trigger */
  HTTP = 'http',
  /** WebSocket connection trigger */
  SOCKET = 'socket',
}

/**
 * Execution context provided to node executors
 * 
 * Contains all the data and utilities needed for a node to execute,
 * including inputs, configuration, authentication, and logging.
 * 
 * @interface ExecutionContext
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * export default defineExecutor(myNode, async (context: ExecutionContext) => {
 *   const { inputs, config, auth, logger } = context;
 *   
 *   logger.info('Node execution started');
 *   
 *   // Access input values
 *   const userInput = inputs.message as string;
 *   
 *   // Access configuration
 *   const apiUrl = config?.apiUrl as string;
 *   
 *   // Access authentication
 *   const apiKey = auth?.apiKey as string;
 *   
 *   // ... perform node logic
 *   
 *   return {
 *     success: true,
 *     outputs: { result: processedData }
 *   };
 * });
 * ```
 */
export interface ExecutionContext {
  /** Input values provided to the node */
  inputs: Record<string, unknown>;
  /** Configuration values set for the node */
  config?: Record<string, unknown>;
  /** Authentication data for the node */
  auth?: Record<string, unknown>;
  /** Logger instance for recording execution information */
  logger: Logger;
}

/**
 * Execution result returned from node executors
 * 
 * Standardized response format for node execution results.
 * Contains success status, outputs, and optional error information.
 * 
 * @interface ExecutionResult
 * @template O - Type of the outputs object
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * // Successful execution
 * const result: ExecutionResult = {
 *   success: true,
 *   outputs: {
 *     processedText: "Hello, World!",
 *     wordCount: 2
 *   }
 * };
 * 
 * // Failed execution
 * const errorResult: ExecutionResult = {
 *   success: false,
 *   outputs: {},
 *   error: "Invalid input provided",
 *   logs: ["Input validation failed", "Execution aborted"]
 * };
 * ```
 */
export interface ExecutionResult<O = Record<string, unknown>> {
  /** Whether the execution was successful */
  success: boolean;
  /** Output values produced by the node */
  outputs: O;
  /** Error message if execution failed */
  error?: string;
  /** Additional log messages from execution */
  logs?: string[];
}

/**
 * Node executor function type
 * 
 * Defines the signature for functions that implement node execution logic.
 * Executors receive an execution context and return a promise of execution results.
 * 
 * @template O - Type of the outputs object
 * @since 0.1.0
 */
export type NodeExecutor<O = Record<string, unknown>> = (
  context: ExecutionContext
) => Promise<ExecutionResult<O>>;
