/**
 * @file Node-specific type definitions for the ezWorkflow Node SDK
 * 
 * This module contains type definitions specifically related to workflow nodes,
 * including node definitions, field definitions, and related interfaces.
 * 
 * @since 0.1.0
 */

import { DataType, FieldType, NodeExecutor } from './core-types';

/**
 * Base field definition interface
 * 
 * Defines the common properties that all field types share, including
 * validation, display options, and metadata.
 * 
 * @interface FieldDefinition
 * @since 0.1.0
 */
export interface FieldDefinition {
  /** The data or field type */
  type: DataType | FieldType;
  /** Human-readable description of the field */
  description?: string;
  /** Whether the field is required */
  required?: boolean;
  /** Default value for the field */
  default?: unknown;
  /** Group name for organizing fields in the UI */
  group?: string;
  /** Whether the field group should be collapsible */
  collapsible?: boolean;
  /** Conditional display logic */
  showIf?: (parent: Record<string, unknown>, inputs?: Record<string, unknown>) => boolean;
  /** Additional metadata for the field */
  metadata?: Record<string, unknown>;
}

/**
 * Input field definition
 * 
 * Defines fields that accept input data for a node.
 * Input fields are used to configure what data a node receives when executed.
 * 
 * @interface InputFieldDefinition
 * @extends FieldDefinition
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const textInput: InputFieldDefinition = {
 *   type: DataType.STRING,
 *   description: 'Text to process',
 *   required: true,
 *   metadata: {
 *     minLength: 1,
 *     maxLength: 1000
 *   }
 * };
 * ```
 */
export interface InputFieldDefinition extends FieldDefinition {
  // Input-specific properties can be added here in the future
}

/**
 * Output field definition
 * 
 * Defines fields that represent output data from a node.
 * Output fields describe what data a node produces after execution.
 * 
 * @interface OutputFieldDefinition
 * @extends FieldDefinition
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const processedOutput: OutputFieldDefinition = {
 *   type: DataType.STRING,
 *   description: 'Processed text result',
 *   metadata: {
 *     format: 'text/plain'
 *   }
 * };
 * ```
 */
export interface OutputFieldDefinition extends FieldDefinition {
  // Output-specific properties can be added here in the future
}

/**
 * Configuration field definition
 * 
 * Defines fields that configure how a node behaves.
 * Config fields are set once when the node is configured and remain
 * constant across executions.
 * 
 * @interface ConfigFieldDefinition
 * @extends FieldDefinition
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const apiEndpoint: ConfigFieldDefinition = {
 *   type: DataType.STRING,
 *   description: 'API endpoint URL',
 *   required: true,
 *   metadata: {
 *     format: 'url'
 *   }
 * };
 * ```
 */
export interface ConfigFieldDefinition extends FieldDefinition {
  // Config-specific properties can be added here in the future
}

/**
 * Node definition interface
 * 
 * Defines the complete specification for a workflow node, including
 * its metadata, inputs, outputs, configuration, and authentication requirements.
 * 
 * @interface NodeDefinition
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const textProcessorNode: NodeDefinition = {
 *   id: 'text-processor-v1',
 *   name: 'Text Processor',
 *   description: 'Processes text with various transformations',
 *   version: '1.0.0',
 *   category: ['Text Processing', 'Utilities'],
 *   tags: ['text', 'transformation', 'utility'],
 *   inputs: {
 *     text: {
 *       type: DataType.STRING,
 *       description: 'Text to process',
 *       required: true
 *     }
 *   },
 *   outputs: {
 *     result: {
 *       type: DataType.STRING,
 *       description: 'Processed text'
 *     }
 *   },
 *   config: {
 *     operation: {
 *       type: FieldType.SELECT,
 *       description: 'Processing operation',
 *       metadata: {
 *         options: [
 *           { label: 'Uppercase', value: 'upper' },
 *           { label: 'Lowercase', value: 'lower' }
 *         ]
 *       }
 *     }
 *   }
 * };
 * ```
 */
export interface NodeDefinition {
  /** Unique identifier for the node */
  id: string;
  /** Human-readable name */
  name: string;
  /** Detailed description of what the node does */
  description: string;
  /** Version string (semantic versioning recommended) */
  version: string;
  /** Category or categories for organizing nodes */
  category: string | string[];
  /** Tags for searchability and filtering */
  tags?: string[];
  /** Input field definitions */
  inputs: Record<string, InputFieldDefinition>;
  /** Output field definitions */
  outputs: Record<string, OutputFieldDefinition>;
  /** Configuration field definitions */
  config?: Record<string, ConfigFieldDefinition>;
  /** Authentication requirements */
  auth?: Record<string, unknown>;
  /** Additional metadata */
  metadata?: Record<string, unknown>;
}

/**
 * Complete node with definition and executor
 * 
 * Represents a fully functional node that includes both its definition
 * (metadata and field specifications) and its executor (implementation logic).
 * 
 * @interface Node
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const completeNode: Node = {
 *   definition: textProcessorDefinition,
 *   executor: async ({ inputs, config, logger }) => {
 *     const text = inputs.text as string;
 *     const operation = config?.operation as string;
 *     
 *     logger.info(`Processing text with operation: ${operation}`);
 *     
 *     let result: string;
 *     switch (operation) {
 *       case 'upper':
 *         result = text.toUpperCase();
 *         break;
 *       case 'lower':
 *         result = text.toLowerCase();
 *         break;
 *       default:
 *         result = text;
 *     }
 *     
 *     return {
 *       success: true,
 *       outputs: { result }
 *     };
 *   }
 * };
 * ```
 */
export interface Node {
  /** Node definition with metadata and field specifications */
  definition: NodeDefinition;
  /** Optional executor function for the node */
  executor?: NodeExecutor;
}

/**
 * Trigger definition interface
 * 
 * Defines how workflows can be triggered to execute, including
 * the trigger type, configuration, and expected data format.
 * 
 * @interface TriggerDefinition
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const webhookTrigger: TriggerDefinition = {
 *   id: 'webhook-trigger-v1',
 *   name: 'Webhook Trigger',
 *   version: '1.0.0',
 *   categories: ['HTTP', 'Webhooks'],
 *   tags: ['webhook', 'http', 'api'],
 *   type: TriggerType.HTTP,
 *   registarUrl: 'https://api.example.com/webhooks',
 *   expect: {
 *     payload: {
 *       type: DataType.OBJECT,
 *       description: 'Webhook payload data',
 *       required: true
 *     }
 *   }
 * };
 * ```
 */
export interface TriggerDefinition {
  /** Unique identifier for the trigger */
  id: string;
  /** Human-readable name */
  name: string;
  /** Version string */
  version: string;
  /** Categories for organizing triggers */
  categories: string[];
  /** Tags for searchability */
  tags: string[];
  /** Type of trigger */
  type: import('./core-types').TriggerType;
  /** URL for registering the trigger */
  registarUrl: string;
  /** Expected data format */
  expect: Record<string, unknown>;
}
