/**
 * @file Server-related type definitions for the ezWorkflow Node SDK
 * 
 * This module contains type definitions specifically related to the ezWorkflow server,
 * including server options, middleware configuration, and related interfaces.
 * 
 * @since 0.1.0
 */

/**
 * Server configuration options for the ezWorkflow server
 * 
 * Defines all the configuration options available when creating an ezWorkflow server instance.
 * These options control authentication, permissions, metadata, and middleware behavior.
 * 
 * @interface ServerOptions
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const serverOptions: ServerOptions = {
 *   apiKey: 'your-secret-api-key',
 *   permissions: ['read', 'execute'],
 *   appType: 'partner',
 *   version: '1.0.0',
 *   author: {
 *     name: 'Your Company',
 *     email: '<EMAIL>',
 *     url: 'https://yourcompany.com'
 *   },
 *   categories: ['API Integration', 'Data Processing'],
 *   tags: ['api', 'data', 'integration'],
 *   middleware: {
 *     logger: true,
 *     timeout: 30000,
 *     compress: true
 *   }
 * };
 * ```
 */
export interface ServerOptions {
  /**
   * API key for authentication
   * 
   * If provided, all requests must include this key in the Authorization header.
   * The server will automatically set up bearer token authentication middleware.
   * 
   * @since 0.1.0
   */
  apiKey?: string;

  /**
   * Permissions array defining what operations are allowed
   * 
   * Available permissions:
   * - 'read': Allow reading node, executor, trigger, and UI component information
   * - 'execute': Allow executing nodes
   * - 'admin': Allow all operations
   * 
   * If not provided, all operations are allowed.
   * 
   * @since 0.1.0
   */
  permissions?: string[];

  /**
   * Application type
   * 
   * Defines whether this is a partner application (external integration)
   * or an account application (internal to the platform).
   * 
   * @since 0.1.0
   */
  appType?: "partner" | "account";

  /**
   * Application version
   * 
   * The version of the application. Should follow semantic versioning.
   * 
   * @since 0.1.0
   */
  version?: string;

  /**
   * Application author information
   * 
   * Can be a simple string or a detailed object with contact information.
   * 
   * @since 0.1.0
   */
  author?:
    | string
    | {
        /** The name of the author/organization */
        name: string;
        /** Contact email address */
        email?: string;
        /** Website or profile URL */
        url?: string;
        /** Support contact URL or email */
        support?: string;
        /** Logo image URL */
        logo?: string;
      };

  /**
   * Application categories
   * 
   * Categories help organize and discover applications in the platform.
   * Use descriptive categories that match your application's purpose.
   * 
   * @since 0.1.0
   */
  categories?: string[];

  /**
   * Application tags
   * 
   * Tags provide additional metadata for searchability and filtering.
   * Use specific, relevant tags that describe your application's functionality.
   * 
   * @since 0.1.0
   */
  tags?: string[];

  /**
   * Whether to use project configuration from ezw.json
   * 
   * If true, will attempt to read configuration from ezw.json file.
   * If false, will only use options provided to the constructor.
   * 
   * @default true
   * @since 0.1.0
   */
  useProjectConfig?: boolean;

  /**
   * Middleware configuration options
   * 
   * Controls the behavior of various middleware components in the server.
   * 
   * @since 0.1.0
   */
  middleware?: MiddlewareOptions;
}

/**
 * Middleware configuration options
 * 
 * Defines configuration for various middleware components that can be
 * enabled or disabled in the ezWorkflow server.
 * 
 * @interface MiddlewareOptions
 * @since 0.1.0
 */
export interface MiddlewareOptions {
  /**
   * Enable request logging
   * 
   * When enabled, logs all incoming requests with method, URL, and response time.
   * 
   * @default true
   * @since 0.1.0
   */
  logger?: boolean;

  /**
   * Enable request ID generation
   * 
   * When enabled, generates unique IDs for each request for tracing and debugging.
   * 
   * @default true
   * @since 0.1.0
   */
  requestId?: boolean;

  /**
   * Enable context storage
   * 
   * When enabled, provides request-scoped storage for sharing data between middleware.
   * 
   * @default true
   * @since 0.1.0
   */
  contextStorage?: boolean;

  /**
   * Request timeout in milliseconds
   * 
   * Maximum time to wait for a request to complete before timing out.
   * 
   * @default 29000 (29 seconds)
   * @since 0.1.0
   */
  timeout?: number;

  /**
   * Enable response compression
   * 
   * When enabled, compresses responses to reduce bandwidth usage.
   * Automatically disabled for Cloudflare Workers and Deno runtimes.
   * 
   * @default true
   * @since 0.1.0
   */
  compress?: boolean;

  /**
   * Enable caching for supported runtimes
   * 
   * When enabled, caches responses for better performance.
   * Only available for Cloudflare Workers and Deno runtimes.
   * 
   * @default true
   * @since 0.1.0
   */
  cache?: boolean;

  /**
   * Cache configuration options
   * 
   * Additional configuration for the caching middleware.
   * 
   * @since 0.1.0
   */
  cacheConfig?: CacheOptions;
}

/**
 * Cache configuration options
 * 
 * Defines configuration for the caching middleware.
 * 
 * @interface CacheOptions
 * @since 0.1.0
 */
export interface CacheOptions {
  /**
   * Name of the cache instance
   * 
   * @default "ezw-sdk-cache"
   * @since 0.1.0
   */
  cacheName?: string;

  /**
   * Cache control header value
   * 
   * Controls how long responses should be cached.
   * 
   * @default "max-age=29"
   * @since 0.1.0
   */
  cacheControl?: string;
}
