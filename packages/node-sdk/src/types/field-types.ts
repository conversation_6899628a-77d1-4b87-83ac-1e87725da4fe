/**
 * @file Field builder type definitions for the ezWorkflow Node SDK
 * 
 * This module contains type definitions for field builders, which provide
 * a fluent API for defining node inputs, outputs, and configuration fields.
 * 
 * @since 0.1.0
 */

import { FieldDefinition } from './node-types';

/**
 * Base field builder interface
 * 
 * Provides the common methods available to all field builders for setting
 * validation rules, display options, and metadata.
 * 
 * @interface FieldBuilder
 * @template T - The TypeScript type of the field value
 * @since 0.1.0
 */
export interface FieldBuilder<T> {
  /** Set the field description */
  description: (desc: string) => FieldBuilder<T>;
  /** Set the default value */
  default: (value: T) => FieldBuilder<T>;
  /** Mark the field as required */
  required: () => FieldBuilder<T>;
  /** Mark the field as optional */
  optional: () => FieldBuilder<T>;
  /** Set conditional display logic */
  showIf: (condition: (parent: Record<string, unknown>, inputs?: Record<string, unknown>) => boolean) => FieldBuilder<T>;
  /** Set the field group for UI organization */
  group: (name: string) => FieldBuilder<T>;
  /** Make the field group collapsible */
  collapsible: (value?: boolean) => FieldBuilder<T>;
  /** Add custom metadata */
  metadata: (data: Record<string, unknown>) => FieldBuilder<T>;
  /** Add validation logic */
  validate: (validator: (value: T) => void | string | boolean) => FieldBuilder<T>;
  /** Add transformation logic */
  transform: (transformer: (value: T, inputs?: Record<string, unknown>) => unknown) => FieldBuilder<T>;
  /** Get the final field definition */
  getDefinition: () => FieldDefinition;
}

/**
 * String field builder interface
 * 
 * Provides methods specific to string/text fields, including length validation,
 * pattern matching, and formatting options.
 * 
 * @interface StringFieldBuilder
 * @extends FieldBuilder<string>
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const emailField = string()
 *   .description('Email address')
 *   .required()
 *   .pattern(/^[^\s@]+@[^\s@]+\.[^\s@]+$/)
 *   .format('email');
 * ```
 */
export interface StringFieldBuilder extends FieldBuilder<string> {
  /** Set minimum length validation */
  minLength: (length: number) => StringFieldBuilder;
  /** Set maximum length validation */
  maxLength: (length: number) => StringFieldBuilder;
  /** Set regex pattern validation */
  pattern: (regex: RegExp) => StringFieldBuilder;
  /** Set string format (email, url, etc.) */
  format: (format: string) => StringFieldBuilder;
  /** Enable multiline text input */
  multiline: (value?: boolean) => StringFieldBuilder;
  /** Mark as secret/password field */
  secret: (value?: boolean) => StringFieldBuilder;
  /** Load value from environment variable */
  fromEnv: (envName: string) => StringFieldBuilder;
}

/**
 * Number field builder interface
 * 
 * Provides methods specific to numeric fields, including range validation
 * and step configuration.
 * 
 * @interface NumberFieldBuilder
 * @extends FieldBuilder<number>
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const ageField = number()
 *   .description('Age in years')
 *   .min(0)
 *   .max(120)
 *   .integer()
 *   .required();
 * ```
 */
export interface NumberFieldBuilder extends FieldBuilder<number> {
  /** Set minimum value */
  min: (value: number) => NumberFieldBuilder;
  /** Set maximum value */
  max: (value: number) => NumberFieldBuilder;
  /** Set step value for input controls */
  step: (value: number) => NumberFieldBuilder;
  /** Restrict to integer values only */
  integer: () => NumberFieldBuilder;
  /** Load value from environment variable */
  fromEnv: (envName: string) => NumberFieldBuilder;
}

/**
 * Boolean field builder interface
 * 
 * Provides methods specific to boolean/checkbox fields.
 * 
 * @interface BooleanFieldBuilder
 * @extends FieldBuilder<boolean>
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const enabledField = boolean()
 *   .description('Enable this feature')
 *   .default(false);
 * ```
 */
export interface BooleanFieldBuilder extends FieldBuilder<boolean> {
  // Boolean-specific methods can be added here in the future
}

/**
 * Options field builder interface
 * 
 * Provides methods for creating dropdown/select fields with predefined options.
 * Supports both single and multiple selection modes.
 * 
 * @interface OptionsFieldBuilder
 * @extends FieldBuilder<string | string[]>
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const priorityField = options([
 *   { label: 'Low', value: 'low', icon: 'mdi:arrow-down' },
 *   { label: 'Medium', value: 'medium', icon: 'mdi:arrow-right' },
 *   { label: 'High', value: 'high', icon: 'mdi:arrow-up' }
 * ])
 *   .description('Task priority')
 *   .default('medium');
 * ```
 */
export interface OptionsFieldBuilder extends FieldBuilder<string | string[]> {
  /** Set the available options */
  options: (options: Array<{ label: string; value: string; icon?: string }>) => OptionsFieldBuilder;
  /** Enable multiple selection */
  multiple: (value?: boolean) => OptionsFieldBuilder;
  /** Load options from environment variable */
  fromEnv: (envName: string) => OptionsFieldBuilder;
}

/**
 * Object field builder interface
 * 
 * Provides methods for creating complex object fields with nested properties.
 * 
 * @interface ObjectFieldBuilder
 * @extends FieldBuilder<Record<string, unknown>>
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const userField = object({
 *   name: string().required(),
 *   email: string().format('email').required(),
 *   age: number().min(0).optional()
 * })
 *   .description('User information');
 * ```
 */
export interface ObjectFieldBuilder extends FieldBuilder<Record<string, unknown>> {
  /** Define the object properties */
  properties: (props: Record<string, FieldDefinition>) => ObjectFieldBuilder;
}

/**
 * Array field builder interface
 * 
 * Provides methods for creating array/list fields with item type validation.
 * 
 * @interface ArrayFieldBuilder
 * @extends FieldBuilder<unknown[]>
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const tagsField = array(string())
 *   .description('List of tags')
 *   .minItems(1)
 *   .maxItems(10);
 * ```
 */
export interface ArrayFieldBuilder extends FieldBuilder<unknown[]> {
  /** Define the type of array items */
  items: (itemType: FieldDefinition) => ArrayFieldBuilder;
  /** Set minimum number of items */
  minItems: (count: number) => ArrayFieldBuilder;
  /** Set maximum number of items */
  maxItems: (count: number) => ArrayFieldBuilder;
}

/**
 * Stream field builder interface
 * 
 * Provides methods for creating streaming data fields for handling large datasets.
 * 
 * @interface StreamFieldBuilder
 * @extends FieldBuilder<unknown>
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const dataStreamField = stream()
 *   .description('Large dataset stream')
 *   .format('application/json')
 *   .chunkSize(1024);
 * ```
 */
export interface StreamFieldBuilder extends FieldBuilder<unknown> {
  /** Set the stream format */
  format: (format: string | ((inputs: Record<string, unknown>) => string)) => StreamFieldBuilder;
  /** Set the chunk size for streaming */
  chunkSize: (size: number) => StreamFieldBuilder;
}

/**
 * File field builder interface
 * 
 * Provides methods for creating file upload fields with validation.
 * 
 * @interface FileFieldBuilder
 * @extends FieldBuilder<unknown>
 * @since 0.1.0
 * 
 * @example
 * ```typescript
 * const imageField = file()
 *   .description('Profile image')
 *   .accept(['image/jpeg', 'image/png'])
 *   .maxSize(5 * 1024 * 1024) // 5MB
 *   .required();
 * ```
 */
export interface FileFieldBuilder extends FieldBuilder<unknown> {
  /** Set accepted MIME types */
  accept: (mimeTypes: string[]) => FileFieldBuilder;
  /** Set maximum file size in bytes */
  maxSize: (sizeInBytes: number) => FileFieldBuilder;
  /** Enable multiple file selection */
  multiple: (value?: boolean) => FileFieldBuilder;
}
