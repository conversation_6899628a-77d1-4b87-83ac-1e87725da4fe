/**
 * @file Centralized type definitions for the ezWorkflow Node SDK
 *
 * This module provides a single entry point for all type definitions used throughout
 * the ezWorkflow Node SDK. It re-exports types from specialized modules to maintain
 * backward compatibility while providing better organization.
 *
 * @since 0.1.0
 */

// Core types
export * from "./core-types";
export * from "./node-types";
export * from "./field-types";
export * from "./auth-types";
export * from "./server-types";

// Legacy exports for backward compatibility (removed to avoid conflicts)
// The new organized structure provides all necessary types

// Ensure key types are available at the top level
export type { Node, NodeDefinition, TriggerDefinition } from "./node-types";
export type {
  NodeExecutor,
  ExecutionContext,
  ExecutionResult,
  Logger,
} from "./core-types";
