/**
 * @file Legacy type exports for backward compatibility
 * 
 * This module re-exports all types from the original types.ts file to maintain
 * backward compatibility. New code should import from the specific type modules
 * instead of using these legacy exports.
 * 
 * @deprecated Use specific type modules instead (core-types, node-types, etc.)
 * @since 0.1.0
 */

// Re-export everything from the original types.ts for backward compatibility
export * from '../types-legacy';
