/**
 * @file Auth builders for defining node authentication
 */

import {
  AuthType,
  AuthPlacement,
  AuthDefinition,
  ApiKeyAuthDefinition,
  OAuth2AuthDefinition,
  BasicAuthDefinition,
  BearerAuthDefinition,
  CustomAuthDefinition,
  AuthBuilder,
  ApiKeyAuthBuilder,
  OAuth2AuthBuilder,
  BasicAuthBuilder,
  BearerAuthBuilder,
  CustomAuthBuilder,
  FieldDefinition,
} from './types';

/**
 * Base auth builder implementation
 */
class BaseAuthBuilder<T extends AuthDefinition> implements AuthBuilder<T> {
  protected definition: T;

  constructor(type: AuthType) {
    this.definition = {
      type,
      required: true,
    } as T;
  }

  description(desc: string): this {
    this.definition.description = desc;
    return this;
  }

  required(): this {
    this.definition.required = true;
    return this;
  }

  optional(): this {
    this.definition.required = false;
    return this;
  }

  provider(name: string): this {
    (this.definition as any).provider = name;
    return this;
  }

  metadata(data: Record<string, unknown>): this {
    this.definition.metadata = { ...(this.definition.metadata || {}), ...data };
    return this;
  }

  getDefinition(): T {
    return { ...this.definition };
  }
}

/**
 * API Key auth builder implementation
 */
class ApiKeyAuthBuilderImpl extends BaseAuthBuilder<ApiKeyAuthDefinition> implements ApiKeyAuthBuilder {
  constructor() {
    super(AuthType.API_KEY);
    this.definition.placement = AuthPlacement.HEADER;
    this.definition.name = 'Authorization';
  }

  placement(place: AuthPlacement): this {
    this.definition.placement = place;
    return this;
  }

  name(name: string): this {
    this.definition.name = name;
    return this;
  }

  prefix(prefix: string): this {
    this.definition.prefix = prefix;
    return this;
  }
}

/**
 * OAuth2 auth builder implementation
 */
class OAuth2AuthBuilderImpl extends BaseAuthBuilder<OAuth2AuthDefinition> implements OAuth2AuthBuilder {
  constructor() {
    super(AuthType.OAUTH2);
    this.definition.authorizationUrl = '';
    this.definition.tokenUrl = '';
  }

  authorizationUrl(url: string): this {
    this.definition.authorizationUrl = url;
    return this;
  }

  tokenUrl(url: string): this {
    this.definition.tokenUrl = url;
    return this;
  }

  scopes(scopes: string[]): this {
    this.definition.scopes = scopes;
    return this;
  }

  refreshUrl(url: string): this {
    this.definition.refreshUrl = url;
    return this;
  }
}

/**
 * Basic auth builder implementation
 */
class BasicAuthBuilderImpl extends BaseAuthBuilder<BasicAuthDefinition> implements BasicAuthBuilder {
  constructor() {
    super(AuthType.BASIC);
  }
}

/**
 * Bearer auth builder implementation
 */
class BearerAuthBuilderImpl extends BaseAuthBuilder<BearerAuthDefinition> implements BearerAuthBuilder {
  constructor() {
    super(AuthType.BEARER);
  }
}

/**
 * Custom auth builder implementation
 */
class CustomAuthBuilderImpl extends BaseAuthBuilder<CustomAuthDefinition> implements CustomAuthBuilder {
  constructor() {
    super(AuthType.CUSTOM);
    this.definition.fields = {};
  }

  fields(fields: Record<string, FieldDefinition>): this {
    this.definition.fields = fields;
    return this;
  }
}

/**
 * Create an API Key auth builder
 */
export const apiKey = (): ApiKeyAuthBuilder => new ApiKeyAuthBuilderImpl();

/**
 * Create an OAuth2 auth builder
 */
export const oauth2 = (): OAuth2AuthBuilder => new OAuth2AuthBuilderImpl();

/**
 * Create a Basic auth builder
 */
export const basic = (): BasicAuthBuilder => new BasicAuthBuilderImpl();

/**
 * Create a Bearer auth builder
 */
export const bearer = (): BearerAuthBuilder => new BearerAuthBuilderImpl();

/**
 * Create a Custom auth builder
 */
export const custom = (): CustomAuthBuilder => new CustomAuthBuilderImpl();
