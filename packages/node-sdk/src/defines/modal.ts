/**
 * @file Modal dialog component definition utilities for ezWorkflow
 *
 * This module provides functionality for defining modal dialog components that can be
 * integrated into the ezWorkflow platform's user interface. Modals are overlay components
 * that appear on top of the main interface to display focused content, forms, confirmations,
 * or other interactive elements without navigating away from the current page.
 * 
 * Modal components extend the base UI component definition with modal-specific properties
 * such as size, positioning, interaction behavior, and visual styling options.
 */

import { componentRegistry } from '../registry';
import { UiComponentDefinition } from '../ui';

/**
 * Modal dialog component definition interface
 *
 * This interface extends the base UI component definition with modal-specific properties
 * that control the modal's appearance, behavior, and user interaction patterns.
 * The screenType is omitted since modals always have a fixed screen type.
 * 
 * @interface ModalDefinition
 * @extends Omit<UiComponentDefinition, 'screenType'>
 */
export interface ModalDefinition extends Omit<UiComponentDefinition, 'screenType'> {
  /** 
   * Size of the modal dialog
   * - 'small': Compact modal for simple forms or confirmations (typically 400-500px wide)
   * - 'medium': Standard modal for most use cases (typically 600-800px wide)
   * - 'large': Wide modal for complex forms or data tables (typically 900-1200px wide)
   */
  modalSize: 'small' | 'medium' | 'large';

  /** 
   * Whether the modal can be closed by clicking outside the modal content area
   * When true, clicking on the backdrop/overlay will close the modal.
   * @default true
   */
  closeOnClickOutside?: boolean;

  /** 
   * Whether the modal displays a close button (X) in the header
   * When true, a close button will be shown in the modal header.
   * @default true
   */
  showCloseButton?: boolean;

  /** 
   * Whether the modal can be dragged around the screen by its header
   * When true, users can click and drag the modal to reposition it.
   * @default false
   */
  draggable?: boolean;

  /** 
   * Whether the modal can be resized by dragging its edges or corners
   * When true, resize handles will be available for user interaction.
   * @default false
   */
  resizable?: boolean;

  /** 
   * Whether the modal should be centered on the screen
   * When true, the modal will be positioned in the center of the viewport.
   * When false, the modal may be positioned based on other criteria.
   * @default true
   */
  centered?: boolean;
}

/**
 * Define a modal dialog component for the ezWorkflow platform
 *
 * This function creates a new modal dialog component definition that can be integrated
 * into the ezWorkflow platform's user interface. Modals provide overlay interfaces for
 * focused interactions such as forms, confirmations, data entry, settings, and other
 * tasks that require user attention without leaving the current context.
 * 
 * The function validates required fields, applies sensible defaults for optional properties,
 * registers the modal with the component registry, and makes it available for use throughout
 * the platform.
 *
 * @param params - Modal dialog component definition parameters
 * @returns Complete ModalDefinition object with validation and registration
 * 
 * @throws {Error} When required fields (id, name, url) are missing
 * @throws {Error} When modalSize is not one of the valid options
 * 
 * @example
 * ```typescript
 * import { defineModal } from '@ezworkflow/node-sdk';
 * 
 * // Define a user profile editing modal
 * const userProfileModal = defineModal({
 *   id: 'user-profile-editor',
 *   name: 'User Profile Editor',
 *   menuTitle: {
 *     'en': 'Edit Profile',
 *     'es': 'Editar Perfil',
 *     'fr': 'Modifier le Profil'
 *   },
 *   description: {
 *     'en': 'Edit user profile information and preferences',
 *     'es': 'Editar información del perfil de usuario y preferencias',
 *     'fr': 'Modifier les informations de profil utilisateur et les préférences'
 *   },
 *   version: '1.0.0',
 *   categories: ['User Management', 'Settings'],
 *   type: 'account',
 *   visibility: 'account',
 *   tags: ['profile', 'user', 'settings'],
 *   icon: 'mdi:account-edit',
 *   url: 'https://my-app.com/modals/user-profile-editor',
 *   modalSize: 'medium',
 *   closeOnClickOutside: true,
 *   showCloseButton: true,
 *   draggable: false,
 *   resizable: false,
 *   centered: true
 * });
 * ```
 * 
 * @example
 * ```typescript
 * // Define a data visualization modal
 * const chartViewerModal = defineModal({
 *   id: 'chart-viewer',
 *   name: 'Chart Viewer',
 *   menuTitle: {
 *     'en': 'View Chart',
 *     'es': 'Ver Gráfico',
 *     'fr': 'Voir le Graphique'
 *   },
 *   description: {
 *     'en': 'Display interactive charts and data visualizations',
 *     'es': 'Mostrar gráficos interactivos y visualizaciones de datos',
 *     'fr': 'Afficher des graphiques interactifs et des visualisations de données'
 *   },
 *   version: '1.0.0',
 *   categories: ['Analytics', 'Visualization'],
 *   type: 'account',
 *   visibility: 'account',
 *   tags: ['chart', 'visualization', 'analytics'],
 *   icon: 'mdi:chart-bar',
 *   url: 'https://my-app.com/modals/chart-viewer',
 *   modalSize: 'large',
 *   closeOnClickOutside: false,
 *   showCloseButton: true,
 *   draggable: true,
 *   resizable: true,
 *   centered: true,
 *   metadata: {
 *     supportedChartTypes: ['bar', 'line', 'pie', 'scatter'],
 *     maxDataPoints: 10000
 *   }
 * });
 * ```
 * 
 * @example
 * ```typescript
 * // Define a simple confirmation modal
 * const confirmationModal = defineModal({
 *   id: 'delete-confirmation',
 *   name: 'Delete Confirmation',
 *   menuTitle: {
 *     'en': 'Confirm Delete',
 *     'es': 'Confirmar Eliminación',
 *     'fr': 'Confirmer la Suppression'
 *   },
 *   description: {
 *     'en': 'Confirm deletion of items with warning message',
 *     'es': 'Confirmar eliminación de elementos con mensaje de advertencia',
 *     'fr': 'Confirmer la suppression d\'éléments avec message d\'avertissement'
 *   },
 *   version: '1.0.0',
 *   categories: ['Confirmation', 'Utility'],
 *   type: 'account',
 *   visibility: 'account',
 *   tags: ['confirmation', 'delete', 'warning'],
 *   icon: 'mdi:alert-circle',
 *   url: 'https://my-app.com/modals/delete-confirmation',
 *   modalSize: 'small',
 *   closeOnClickOutside: false,
 *   showCloseButton: false,
 *   draggable: false,
 *   resizable: false,
 *   centered: true
 * });
 * ```
 */
export const defineModal = (params: ModalDefinition): ModalDefinition => {
  // Validate required fields
  if (!params.id) {
    throw new Error('Modal dialog ID is required. Please provide a unique identifier for the modal.');
  }

  if (!params.name) {
    throw new Error('Modal dialog name is required. Please provide a human-readable name for the modal.');
  }

  if (!params.url) {
    throw new Error('Modal URL is required. Please provide the URL where the modal component is hosted.');
  }

  // Validate modal size
  const validSizes = ['small', 'medium', 'large'];
  if (!validSizes.includes(params.modalSize)) {
    throw new Error(`Modal size must be one of: ${validSizes.join(', ')}. Received: ${params.modalSize}`);
  }

  // Create a UI component definition with modal dialog specific properties
  const uiComponentDefinition: ModalDefinition = {
    ...params,
    // Set default values for optional properties
    closeOnClickOutside: params.closeOnClickOutside ?? true,
    showCloseButton: params.showCloseButton ?? true,
    draggable: params.draggable ?? false,
    resizable: params.resizable ?? false,
    centered: params.centered ?? true,
  };

  // Register the modal dialog in the registry
  componentRegistry.registerModal(uiComponentDefinition);

  // Return the validated modal dialog definition
  return uiComponentDefinition;
};
