/**
 * @file Drawer component definition utilities for ezWorkflow
 *
 * This module provides functionality for defining drawer components that can be
 * integrated into the ezWorkflow platform's user interface. Drawers are slide-out
 * panels that appear from the edges of the screen to display additional content,
 * navigation, settings, or tools without obscuring the main interface completely.
 * 
 * Drawer components extend the base UI component definition with drawer-specific
 * properties such as size, position, interaction behavior, and visual styling options.
 */

import { componentRegistry } from '../registry';
import { UiComponentDefinition } from '../ui';

/**
 * Drawer component definition interface
 *
 * This interface extends the base UI component definition with drawer-specific properties
 * that control the drawer's appearance, position, behavior, and user interaction patterns.
 * 
 * @interface DrawerDefinition
 * @extends UiComponentDefinition
 */
export interface DrawerDefinition extends UiComponentDefinition {
  /** 
   * Size of the drawer panel
   * - 'small': Narrow drawer for simple navigation or tools (typically 250-300px)
   * - 'medium': Standard drawer for most use cases (typically 350-450px)
   * - 'large': Wide drawer for complex content or forms (typically 500-700px)
   */
  drawerSize: 'small' | 'medium' | 'large';

  /** 
   * Position where the drawer slides out from
   * - 'left': Slides out from the left edge of the screen
   * - 'right': Slides out from the right edge of the screen
   * - 'top': Slides down from the top edge of the screen
   * - 'bottom': Slides up from the bottom edge of the screen
   * @default 'right'
   */
  position?: 'left' | 'right' | 'top' | 'bottom';

  /** 
   * Whether the drawer can be closed by clicking outside the drawer content area
   * When true, clicking on the backdrop/overlay will close the drawer.
   * @default true
   */
  closeOnClickOutside?: boolean;

  /** 
   * Whether the drawer displays a close button (X) in the header
   * When true, a close button will be shown in the drawer header.
   * @default true
   */
  showCloseButton?: boolean;

  /** 
   * Whether the drawer can be resized by dragging its edge
   * When true, users can drag the drawer's edge to adjust its size.
   * @default false
   */
  resizable?: boolean;
}

/**
 * Define a drawer component for the ezWorkflow platform
 *
 * This function creates a new drawer component definition that can be integrated
 * into the ezWorkflow platform's user interface. Drawers provide slide-out interfaces
 * for navigation, tools, settings, detailed views, and other content that supplements
 * the main interface without completely covering it.
 * 
 * The function validates required fields, applies sensible defaults for optional properties,
 * registers the drawer with the component registry, and makes it available for use throughout
 * the platform.
 *
 * @param params - Drawer component definition parameters
 * @returns Complete DrawerDefinition object with validation and registration
 * 
 * @throws {Error} When required fields (id, name, menuTitle, drawerSize, url) are missing
 * @throws {Error} When drawerSize is not one of the valid options
 * @throws {Error} When position is not one of the valid options
 * @throws {Error} When menuTitle is empty or invalid
 * 
 * @example
 * ```typescript
 * import { defineDrawer } from '@ezworkflow/node-sdk';
 * 
 * // Define a navigation drawer
 * const navigationDrawer = defineDrawer({
 *   id: 'main-navigation',
 *   name: 'Main Navigation',
 *   menuTitle: {
 *     'en': 'Navigation',
 *     'es': 'Navegación',
 *     'fr': 'Navigation'
 *   },
 *   description: {
 *     'en': 'Main navigation menu with workflow categories and tools',
 *     'es': 'Menú de navegación principal con categorías de flujo de trabajo y herramientas',
 *     'fr': 'Menu de navigation principal avec catégories de flux de travail et outils'
 *   },
 *   version: '1.0.0',
 *   categories: ['Navigation', 'Menu'],
 *   type: 'account',
 *   visibility: 'account',
 *   tags: ['navigation', 'menu', 'sidebar'],
 *   icon: 'mdi:menu',
 *   url: 'https://my-app.com/drawers/main-navigation',
 *   drawerSize: 'medium',
 *   position: 'left',
 *   closeOnClickOutside: true,
 *   showCloseButton: false,
 *   resizable: false
 * });
 * ```
 * 
 * @example
 * ```typescript
 * // Define a properties panel drawer
 * const propertiesDrawer = defineDrawer({
 *   id: 'properties-panel',
 *   name: 'Properties Panel',
 *   menuTitle: {
 *     'en': 'Properties',
 *     'es': 'Propiedades',
 *     'fr': 'Propriétés'
 *   },
 *   description: {
 *     'en': 'Edit properties and settings for selected workflow elements',
 *     'es': 'Editar propiedades y configuraciones para elementos de flujo de trabajo seleccionados',
 *     'fr': 'Modifier les propriétés et paramètres des éléments de flux de travail sélectionnés'
 *   },
 *   version: '1.0.0',
 *   categories: ['Editor', 'Properties'],
 *   type: 'account',
 *   visibility: 'account',
 *   tags: ['properties', 'editor', 'settings'],
 *   icon: 'mdi:cog-outline',
 *   url: 'https://my-app.com/drawers/properties-panel',
 *   drawerSize: 'large',
 *   position: 'right',
 *   closeOnClickOutside: false,
 *   showCloseButton: true,
 *   resizable: true,
 *   metadata: {
 *     supportedElements: ['nodes', 'triggers', 'connections'],
 *     autoSave: true
 *   }
 * });
 * ```
 * 
 * @example
 * ```typescript
 * // Define a notifications drawer
 * const notificationsDrawer = defineDrawer({
 *   id: 'notifications-panel',
 *   name: 'Notifications Panel',
 *   menuTitle: {
 *     'en': 'Notifications',
 *     'es': 'Notificaciones',
 *     'fr': 'Notifications'
 *   },
 *   description: {
 *     'en': 'View recent notifications and system alerts',
 *     'es': 'Ver notificaciones recientes y alertas del sistema',
 *     'fr': 'Voir les notifications récentes et les alertes système'
 *   },
 *   version: '1.0.0',
 *   categories: ['Notifications', 'System'],
 *   type: 'account',
 *   visibility: 'account',
 *   tags: ['notifications', 'alerts', 'system'],
 *   icon: 'mdi:bell',
 *   url: 'https://my-app.com/drawers/notifications-panel',
 *   drawerSize: 'small',
 *   position: 'top',
 *   closeOnClickOutside: true,
 *   showCloseButton: true,
 *   resizable: false
 * });
 * ```
 */
export const defineDrawer = (params: DrawerDefinition): DrawerDefinition => {
  // Validate required fields
  if (!params.id) {
    throw new Error('Drawer ID is required. Please provide a unique identifier for the drawer.');
  }

  if (!params.name) {
    throw new Error('Drawer name is required. Please provide a human-readable name for the drawer.');
  }

  if (!params.url) {
    throw new Error('Drawer URL is required. Please provide the URL where the drawer component is hosted.');
  }

  if (!params.menuTitle || Object.keys(params.menuTitle).length === 0) {
    throw new Error('Drawer menuTitle is required and must have at least one language. Example: { "en": "My Drawer" }');
  }

  if (!params.drawerSize) {
    throw new Error('Drawer size is required. Please specify one of: small, medium, large');
  }

  // Validate drawer size
  const validSizes = ['small', 'medium', 'large'];
  if (!validSizes.includes(params.drawerSize)) {
    throw new Error(`Drawer size must be one of: ${validSizes.join(', ')}. Received: ${params.drawerSize}`);
  }

  // Validate position if provided
  if (params.position) {
    const validPositions = ['left', 'right', 'top', 'bottom'];
    if (!validPositions.includes(params.position)) {
      throw new Error(`Drawer position must be one of: ${validPositions.join(', ')}. Received: ${params.position}`);
    }
  }

  // Create a UI component definition with drawer specific properties
  const uiComponentDefinition: DrawerDefinition = {
    ...params,
    // Set default values for optional properties
    position: params.position ?? 'right',
    closeOnClickOutside: params.closeOnClickOutside ?? true,
    showCloseButton: params.showCloseButton ?? true,
    resizable: params.resizable ?? false,
  };

  // Register the drawer in the registry
  componentRegistry.registerDrawer(uiComponentDefinition);

  // Return the validated drawer definition
  return uiComponentDefinition;
};
