/**
 * @file Dashboard widget component definition utilities for ezWorkflow
 *
 * This module provides functionality for defining dashboard widget components that can be
 * integrated into the ezWorkflow platform's dashboard interface. Dashboard widgets are
 * modular, reusable components that display data, metrics, controls, or other information
 * in a grid-based dashboard layout.
 * 
 * Widget components extend the base UI component definition with widget-specific properties
 * such as size, dimensions, fullscreen support, and layout behavior within dashboard grids.
 */

import { componentRegistry } from '../registry';
import { UiComponentDefinition } from '../ui';

/**
 * Dashboard widget component definition interface
 *
 * This interface extends the base UI component definition with widget-specific properties
 * that control the widget's size, layout behavior, and interaction capabilities within
 * dashboard environments.
 * 
 * @interface DashboardWidgetDefinition
 * @extends UiComponentDefinition
 */
export interface DashboardWidgetDefinition extends UiComponentDefinition {
  /** 
   * Size of the dashboard widget
   * - 'small': Compact widget for simple metrics or indicators (typically 1x1 grid cells)
   * - 'medium': Standard widget for charts or moderate content (typically 2x2 grid cells)
   * - 'large': Wide widget for complex visualizations or tables (typically 3x2 or 4x2 grid cells)
   * - 'custom': Custom dimensions specified by customWidth and customHeight
   */
  widgetSize: 'small' | 'medium' | 'large' | 'custom';

  /** 
   * Custom width for the widget (only used when widgetSize is 'custom')
   * Can be specified in pixels (e.g., '400px'), percentages (e.g., '50%'), 
   * or grid units (e.g., '3fr').
   * @example '400px', '50%', '3fr'
   */
  customWidth?: string;

  /** 
   * Custom height for the widget (only used when widgetSize is 'custom')
   * Can be specified in pixels (e.g., '300px'), percentages (e.g., '40%'),
   * or grid units (e.g., '2fr').
   * @example '300px', '40%', '2fr'
   */
  customHeight?: string;

  /** 
   * Whether the widget supports fullscreen mode
   * When true, users can expand the widget to fill the entire screen for detailed viewing.
   * @default false
   */
  fullscreenSupport?: boolean;

  /** 
   * Whether the widget can be resized by users in the dashboard
   * When true, users can drag widget edges to adjust its size.
   * @default false
   */
  resizable?: boolean;

  /** 
   * Minimum width constraint for resizable widgets
   * Only applies when resizable is true.
   * @example '200px', '150px'
   */
  minWidth?: string;

  /** 
   * Minimum height constraint for resizable widgets
   * Only applies when resizable is true.
   * @example '150px', '100px'
   */
  minHeight?: string;

  /** 
   * Whether the widget supports refresh/reload functionality
   * When true, users can manually refresh the widget's data.
   * @default true
   */
  refreshable?: boolean;

  /** 
   * Auto-refresh interval in seconds
   * When specified, the widget will automatically refresh its data at this interval.
   * Set to 0 or undefined to disable auto-refresh.
   * @example 30, 60, 300
   */
  autoRefreshInterval?: number;
}

/**
 * Define a dashboard widget component for the ezWorkflow platform
 *
 * This function creates a new dashboard widget component definition that can be integrated
 * into the ezWorkflow platform's dashboard interface. Dashboard widgets provide modular,
 * reusable components for displaying data, metrics, controls, visualizations, and other
 * information in customizable dashboard layouts.
 * 
 * The function validates required fields, applies sensible defaults for optional properties,
 * handles custom sizing validation, registers the widget with the component registry, and
 * makes it available for use in dashboard configurations.
 *
 * @param params - Dashboard widget component definition parameters
 * @returns Complete DashboardWidgetDefinition object with validation and registration
 * 
 * @throws {Error} When required fields (id, name, url) are missing
 * @throws {Error} When widgetSize is not one of the valid options
 * @throws {Error} When custom dimensions are missing for custom-sized widgets
 * @throws {Error} When auto-refresh interval is invalid
 * 
 * @example
 * ```typescript
 * import { defineDashboardWidget } from '@ezworkflow/node-sdk';
 * 
 * // Define a metrics widget
 * const metricsWidget = defineDashboardWidget({
 *   id: 'workflow-metrics',
 *   name: 'Workflow Metrics',
 *   menuTitle: {
 *     'en': 'Metrics',
 *     'es': 'Métricas',
 *     'fr': 'Métriques'
 *   },
 *   description: {
 *     'en': 'Display key workflow performance metrics and statistics',
 *     'es': 'Mostrar métricas clave de rendimiento del flujo de trabajo y estadísticas',
 *     'fr': 'Afficher les métriques clés de performance du flux de travail et les statistiques'
 *   },
 *   version: '1.0.0',
 *   categories: ['Analytics', 'Metrics'],
 *   type: 'account',
 *   visibility: 'account',
 *   tags: ['metrics', 'analytics', 'performance'],
 *   icon: 'mdi:chart-line',
 *   url: 'https://my-app.com/widgets/workflow-metrics',
 *   widgetSize: 'medium',
 *   fullscreenSupport: true,
 *   resizable: true,
 *   minWidth: '300px',
 *   minHeight: '200px',
 *   refreshable: true,
 *   autoRefreshInterval: 60
 * });
 * ```
 * 
 * @example
 * ```typescript
 * // Define a custom-sized data table widget
 * const dataTableWidget = defineDashboardWidget({
 *   id: 'execution-history-table',
 *   name: 'Execution History Table',
 *   menuTitle: {
 *     'en': 'Execution History',
 *     'es': 'Historial de Ejecución',
 *     'fr': 'Historique d\'Exécution'
 *   },
 *   description: {
 *     'en': 'Detailed table of workflow execution history and results',
 *     'es': 'Tabla detallada del historial de ejecución del flujo de trabajo y resultados',
 *     'fr': 'Tableau détaillé de l\'historique d\'exécution du flux de travail et des résultats'
 *   },
 *   version: '1.0.0',
 *   categories: ['Data', 'History'],
 *   type: 'account',
 *   visibility: 'account',
 *   tags: ['table', 'history', 'execution'],
 *   icon: 'mdi:table',
 *   url: 'https://my-app.com/widgets/execution-history-table',
 *   widgetSize: 'custom',
 *   customWidth: '800px',
 *   customHeight: '400px',
 *   fullscreenSupport: true,
 *   resizable: false,
 *   refreshable: true,
 *   autoRefreshInterval: 30,
 *   metadata: {
 *     maxRows: 100,
 *     sortable: true,
 *     filterable: true
 *   }
 * });
 * ```
 * 
 * @example
 * ```typescript
 * // Define a simple status indicator widget
 * const statusWidget = defineDashboardWidget({
 *   id: 'system-status',
 *   name: 'System Status',
 *   menuTitle: {
 *     'en': 'Status',
 *     'es': 'Estado',
 *     'fr': 'Statut'
 *   },
 *   description: {
 *     'en': 'Current system status and health indicators',
 *     'es': 'Estado actual del sistema e indicadores de salud',
 *     'fr': 'État actuel du système et indicateurs de santé'
 *   },
 *   version: '1.0.0',
 *   categories: ['System', 'Status'],
 *   type: 'account',
 *   visibility: 'account',
 *   tags: ['status', 'health', 'system'],
 *   icon: 'mdi:heart-pulse',
 *   url: 'https://my-app.com/widgets/system-status',
 *   widgetSize: 'small',
 *   fullscreenSupport: false,
 *   resizable: false,
 *   refreshable: true,
 *   autoRefreshInterval: 10
 * });
 * ```
 */
export const defineDashboardWidget = (params: DashboardWidgetDefinition): DashboardWidgetDefinition => {
  // Validate required fields
  if (!params.id) {
    throw new Error('Dashboard widget ID is required. Please provide a unique identifier for the widget.');
  }

  if (!params.name) {
    throw new Error('Dashboard widget name is required. Please provide a human-readable name for the widget.');
  }

  if (!params.url) {
    throw new Error('Dashboard widget URL is required. Please provide the URL where the widget component is hosted.');
  }

  // Validate widget size
  const validSizes = ['small', 'medium', 'large', 'custom'];
  if (!validSizes.includes(params.widgetSize)) {
    throw new Error(`Widget size must be one of: ${validSizes.join(', ')}. Received: ${params.widgetSize}`);
  }

  // If widget size is custom, validate custom dimensions
  if (params.widgetSize === 'custom') {
    if (!params.customWidth || !params.customHeight) {
      throw new Error('Custom width and height are required when widget size is "custom". Please provide both customWidth and customHeight properties.');
    }
  }

  // Validate auto-refresh interval if provided
  if (params.autoRefreshInterval !== undefined) {
    if (params.autoRefreshInterval < 0 || !Number.isInteger(params.autoRefreshInterval)) {
      throw new Error('Auto-refresh interval must be a non-negative integer (seconds). Use 0 to disable auto-refresh.');
    }
  }

  // Create a UI component definition with dashboard widget specific properties
  const uiComponentDefinition: DashboardWidgetDefinition = {
    ...params,
    // Set default values for optional properties
    version: params.version || '1.0.0',
    fullscreenSupport: params.fullscreenSupport ?? false,
    resizable: params.resizable ?? false,
    refreshable: params.refreshable ?? true,
  };

  // Register the dashboard widget in the registry
  componentRegistry.registerDashboardWidget(uiComponentDefinition);

  // Return the validated dashboard widget definition
  return uiComponentDefinition;
};
