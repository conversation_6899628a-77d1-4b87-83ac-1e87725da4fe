/**
 * @file Page component definition utilities for ezWorkflow
 *
 * This module provides functionality for defining page components that can be integrated
 * into the ezWorkflow platform's user interface. Pages are full-screen UI components that
 * can be accessed through navigation and provide custom interfaces for workflow management,
 * data visualization, configuration, and other platform features.
 * 
 * Page components extend the base UI component definition with page-specific properties
 * such as routing, authentication requirements, navigation settings, and role-based access control.
 */

import { componentRegistry } from '../registry';
import { UiComponentDefinition } from '../ui';

/**
 * Page component definition interface
 *
 * This interface extends the base UI component definition with page-specific properties
 * that control how the page is displayed, accessed, and integrated into the platform's
 * navigation structure.
 * 
 * @interface PageDefinition
 * @extends UiComponentDefinition
 */
export interface PageDefinition extends UiComponentDefinition {
  /** 
   * Route path for the page within the application
   * This defines the URL path where the page will be accessible.
   * @example '/dashboard/analytics' or '/settings/integrations'
   */
  route?: string;

  /** 
   * Whether the page requires user authentication to access
   * When true, unauthenticated users will be redirected to login.
   * @default false
   */
  requiresAuth?: boolean;

  /** 
   * Array of user roles that have access to this page
   * If specified, only users with these roles can access the page.
   * @example ['admin', 'manager'] or ['user', 'viewer']
   */
  allowedRoles?: string[];

  /** 
   * Whether the page should be included in the main navigation menu
   * When false, the page can still be accessed directly but won't appear in navigation.
   * @default true
   */
  showInNavigation?: boolean;

  /** 
   * Navigation group for organizing related pages together
   * Pages with the same navigation group will be grouped in the navigation menu.
   * @example 'Settings' or 'Analytics' or 'Administration'
   */
  navigationGroup?: string;

  /** 
   * Navigation order within its group (lower numbers appear first)
   * Used to control the order of pages in navigation menus.
   * @example 1, 2, 3, etc.
   */
  navigationOrder?: number;
}

/**
 * Define a page component for the ezWorkflow platform
 *
 * This function creates a new page component definition that can be integrated into
 * the ezWorkflow platform's user interface. Pages provide custom interfaces for
 * workflow management, data visualization, configuration, and other platform features.
 * 
 * The function validates required fields, registers the page with the component registry,
 * and makes it available for use in the platform's navigation and routing system.
 *
 * @param params - Page component definition parameters
 * @returns Complete PageDefinition object with validation and registration
 * 
 * @throws {Error} When required fields (id, name, url) are missing
 * 
 * @example
 * ```typescript
 * import { definePage } from '@ezworkflow/node-sdk';
 * 
 * // Define a dashboard page
 * const dashboardPage = definePage({
 *   id: 'analytics-dashboard',
 *   name: 'Analytics Dashboard',
 *   menuTitle: {
 *     'en': 'Analytics',
 *     'es': 'Analíticas',
 *     'fr': 'Analytique'
 *   },
 *   description: {
 *     'en': 'View workflow analytics and performance metrics',
 *     'es': 'Ver análisis de flujo de trabajo y métricas de rendimiento',
 *     'fr': 'Afficher les analyses de flux de travail et les métriques de performance'
 *   },
 *   version: '1.0.0',
 *   categories: ['Analytics', 'Dashboard'],
 *   type: 'account',
 *   visibility: 'account',
 *   tags: ['analytics', 'dashboard', 'metrics'],
 *   icon: 'mdi:chart-line',
 *   url: 'https://my-app.com/pages/analytics-dashboard',
 *   route: '/dashboard/analytics',
 *   requiresAuth: true,
 *   allowedRoles: ['admin', 'analyst', 'manager'],
 *   showInNavigation: true,
 *   navigationGroup: 'Analytics',
 *   navigationOrder: 1
 * });
 * ```
 * 
 * @example
 * ```typescript
 * // Define a settings page
 * const settingsPage = definePage({
 *   id: 'integration-settings',
 *   name: 'Integration Settings',
 *   menuTitle: {
 *     'en': 'Integrations',
 *     'es': 'Integraciones',
 *     'fr': 'Intégrations'
 *   },
 *   description: {
 *     'en': 'Configure external service integrations',
 *     'es': 'Configurar integraciones de servicios externos',
 *     'fr': 'Configurer les intégrations de services externes'
 *   },
 *   version: '1.0.0',
 *   categories: ['Settings', 'Configuration'],
 *   type: 'account',
 *   visibility: 'account',
 *   tags: ['settings', 'integrations', 'configuration'],
 *   icon: 'mdi:cog',
 *   url: 'https://my-app.com/pages/integration-settings',
 *   route: '/settings/integrations',
 *   requiresAuth: true,
 *   allowedRoles: ['admin'],
 *   showInNavigation: true,
 *   navigationGroup: 'Settings',
 *   navigationOrder: 2,
 *   metadata: {
 *     helpUrl: 'https://docs.my-app.com/integrations',
 *     category: 'configuration'
 *   }
 * });
 * ```
 * 
 * @example
 * ```typescript
 * // Define a public page (no authentication required)
 * const publicPage = definePage({
 *   id: 'workflow-gallery',
 *   name: 'Workflow Gallery',
 *   menuTitle: {
 *     'en': 'Gallery',
 *     'es': 'Galería',
 *     'fr': 'Galerie'
 *   },
 *   description: {
 *     'en': 'Browse public workflow templates',
 *     'es': 'Explorar plantillas de flujo de trabajo públicas',
 *     'fr': 'Parcourir les modèles de flux de travail publics'
 *   },
 *   version: '1.0.0',
 *   categories: ['Templates', 'Gallery'],
 *   type: 'partner',
 *   visibility: 'partner',
 *   tags: ['gallery', 'templates', 'public'],
 *   icon: 'mdi:view-gallery',
 *   url: 'https://my-app.com/pages/workflow-gallery',
 *   route: '/gallery',
 *   requiresAuth: false,
 *   showInNavigation: true,
 *   navigationGroup: 'Discover',
 *   navigationOrder: 1
 * });
 * ```
 */
export const definePage = (params: PageDefinition): PageDefinition => {
  // Validate required fields
  if (!params.id) {
    throw new Error('Page ID is required. Please provide a unique identifier for the page.');
  }

  if (!params.name) {
    throw new Error('Page name is required. Please provide a human-readable name for the page.');
  }

  if (!params.url) {
    throw new Error('Page URL is required. Please provide the URL where the page component is hosted.');
  }

  // Validate route format if provided
  if (params.route && !params.route.startsWith('/')) {
    throw new Error('Page route must start with a forward slash (/). Example: "/dashboard/analytics"');
  }

  // Validate navigation order if provided
  if (params.navigationOrder !== undefined && (params.navigationOrder < 0 || !Number.isInteger(params.navigationOrder))) {
    throw new Error('Navigation order must be a non-negative integer.');
  }

  // Create a UI component definition with page specific properties
  const uiComponentDefinition: PageDefinition = {
    ...params,
    // Set default values for optional properties
    requiresAuth: params.requiresAuth ?? false,
    showInNavigation: params.showInNavigation ?? true,
  };

  // Register the page in the registry
  componentRegistry.registerPage(uiComponentDefinition);

  // Return the validated page definition
  return uiComponentDefinition;
};
