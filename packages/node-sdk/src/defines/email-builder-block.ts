/**
 * @file Email builder block component definition utilities for ezWorkflow
 *
 * This module provides functionality for defining email builder block components that can be
 * integrated into the ezWorkflow platform's email builder interface. Email builder blocks are
 * modular, reusable components that users can drag and drop to construct email templates,
 * including text blocks, images, buttons, dividers, and other email content elements.
 * 
 * Block components extend the base UI component definition with email-specific properties
 * such as block type, interaction behavior, default content, styling, and configuration settings.
 */

import { componentRegistry } from '../registry';
import { UiComponentDefinition } from '../ui';

/**
 * Configuration setting for email builder blocks
 * 
 * @interface BlockSetting
 */
interface BlockSetting {
  /** Unique name/key for the setting */
  name: string;
  /** Type of input control for the setting */
  type: 'text' | 'number' | 'color' | 'select' | 'toggle' | 'image' | 'textarea' | 'url';
  /** Display label for the setting */
  label: string;
  /** Optional description explaining the setting's purpose */
  description?: string;
  /** Default value for the setting */
  defaultValue?: string | number | boolean;
  /** Options for select-type settings */
  options?: Array<{ label: string; value: string }>;
  /** Minimum value for number-type settings */
  min?: number;
  /** Maximum value for number-type settings */
  max?: number;
  /** Whether the setting is required */
  required?: boolean;
  /** Placeholder text for input fields */
  placeholder?: string;
}

/**
 * Email builder block component definition interface
 *
 * This interface extends the base UI component definition with email builder block-specific
 * properties that control the block's behavior, appearance, and configuration options within
 * the email builder environment.
 * 
 * @interface EmailBuilderBlockDefinition
 * @extends UiComponentDefinition
 */
export interface EmailBuilderBlockDefinition extends UiComponentDefinition {
  /** 
   * Type of the email builder block
   * - 'text': Text content blocks (paragraphs, headings, etc.)
   * - 'image': Image blocks with optional links and alt text
   * - 'button': Call-to-action buttons with links and styling
   * - 'divider': Horizontal dividers and separators
   * - 'spacer': Empty space blocks for layout control
   * - 'social': Social media icons and links
   * - 'custom': Custom HTML or specialized content blocks
   */
  blockType: 'text' | 'image' | 'button' | 'divider' | 'spacer' | 'social' | 'custom';

  /** 
   * Whether the block can be dragged and repositioned within the email
   * @default true
   */
  draggable?: boolean;

  /** 
   * Whether the block can be resized by users
   * @default false
   */
  resizable?: boolean;

  /** 
   * Whether the block can be deleted from the email
   * @default true
   */
  deletable?: boolean;

  /** 
   * Whether the block can be duplicated/copied
   * @default true
   */
  duplicatable?: boolean;

  /** 
   * Default content for the block when first added to an email
   * For text blocks: default text content
   * For image blocks: default image URL or placeholder
   * For buttons: default button text
   * @example "Click here to learn more" or "https://example.com/image.jpg"
   */
  defaultContent?: string;

  /** 
   * Default CSS styles applied to the block
   * @example { "color": "#333333", "font-size": "16px", "text-align": "center" }
   */
  defaultStyles?: Record<string, string>;

  /** 
   * Configuration settings available for customizing the block
   * These settings appear in the email builder's property panel when the block is selected.
   */
  settings?: BlockSetting[];

  /** 
   * Whether the block supports responsive design
   * When true, the block will adapt to different screen sizes
   * @default true
   */
  responsive?: boolean;

  /** 
   * Preview template for displaying the block in the builder
   * Can include placeholders that will be replaced with actual content
   * @example "<div style='{{styles}}'>{{content}}</div>"
   */
  previewTemplate?: string;

  /** 
   * Output template for generating the final email HTML
   * Should include all necessary email-safe HTML and inline styles
   * @example "<table><tr><td style='{{styles}}'>{{content}}</td></tr></table>"
   */
  outputTemplate?: string;
}

/**
 * Define an email builder block component for the ezWorkflow platform
 *
 * This function creates a new email builder block component definition that can be integrated
 * into the ezWorkflow platform's email builder interface. Email builder blocks provide modular,
 * reusable components that users can drag and drop to construct professional email templates
 * with various content types and styling options.
 * 
 * The function validates required fields, applies sensible defaults for optional properties,
 * validates block settings, registers the block with the component registry, and makes it
 * available for use in email builder configurations.
 *
 * @param params - Email builder block component definition parameters
 * @returns Complete EmailBuilderBlockDefinition object with validation and registration
 * 
 * @throws {Error} When required fields are missing or invalid
 * @throws {Error} When blockType is not one of the valid options
 * @throws {Error} When settings configuration is invalid
 * 
 * @example
 * ```typescript
 * import { defineEmailBuilderBlock } from '@ezworkflow/node-sdk';
 * 
 * // Define a text block
 * const textBlock = defineEmailBuilderBlock({
 *   id: 'rich-text-block',
 *   name: 'Rich Text Block',
 *   menuTitle: {
 *     'en': 'Text',
 *     'es': 'Texto',
 *     'fr': 'Texte'
 *   },
 *   description: {
 *     'en': 'Add formatted text content to your email',
 *     'es': 'Agregar contenido de texto formateado a su correo electrónico',
 *     'fr': 'Ajouter du contenu texte formaté à votre email'
 *   },
 *   version: '1.0.0',
 *   categories: ['Content', 'Text'],
 *   type: 'account',
 *   visibility: 'account',
 *   tags: ['text', 'content', 'formatting'],
 *   icon: 'mdi:format-text',
 *   url: 'https://my-app.com/email-blocks/rich-text',
 *   blockType: 'text',
 *   defaultContent: 'Enter your text here...',
 *   defaultStyles: {
 *     'font-family': 'Arial, sans-serif',
 *     'font-size': '16px',
 *     'color': '#333333',
 *     'line-height': '1.5'
 *   },
 *   settings: [
 *     {
 *       name: 'fontSize',
 *       type: 'number',
 *       label: 'Font Size',
 *       description: 'Text size in pixels',
 *       defaultValue: 16,
 *       min: 8,
 *       max: 72
 *     },
 *     {
 *       name: 'textAlign',
 *       type: 'select',
 *       label: 'Text Alignment',
 *       defaultValue: 'left',
 *       options: [
 *         { label: 'Left', value: 'left' },
 *         { label: 'Center', value: 'center' },
 *         { label: 'Right', value: 'right' }
 *       ]
 *     },
 *     {
 *       name: 'textColor',
 *       type: 'color',
 *       label: 'Text Color',
 *       defaultValue: '#333333'
 *     }
 *   ]
 * });
 * ```
 * 
 * @example
 * ```typescript
 * // Define a button block
 * const buttonBlock = defineEmailBuilderBlock({
 *   id: 'cta-button-block',
 *   name: 'Call-to-Action Button',
 *   menuTitle: {
 *     'en': 'Button',
 *     'es': 'Botón',
 *     'fr': 'Bouton'
 *   },
 *   description: {
 *     'en': 'Add clickable buttons to drive user actions',
 *     'es': 'Agregar botones clicables para impulsar acciones del usuario',
 *     'fr': 'Ajouter des boutons cliquables pour encourager les actions utilisateur'
 *   },
 *   version: '1.0.0',
 *   categories: ['Interactive', 'CTA'],
 *   type: 'account',
 *   visibility: 'account',
 *   tags: ['button', 'cta', 'link', 'action'],
 *   icon: 'mdi:gesture-tap-button',
 *   url: 'https://my-app.com/email-blocks/cta-button',
 *   blockType: 'button',
 *   defaultContent: 'Click Here',
 *   defaultStyles: {
 *     'background-color': '#007bff',
 *     'color': '#ffffff',
 *     'padding': '12px 24px',
 *     'border-radius': '4px',
 *     'text-decoration': 'none',
 *     'display': 'inline-block'
 *   },
 *   settings: [
 *     {
 *       name: 'buttonText',
 *       type: 'text',
 *       label: 'Button Text',
 *       defaultValue: 'Click Here',
 *       required: true
 *     },
 *     {
 *       name: 'buttonUrl',
 *       type: 'url',
 *       label: 'Button URL',
 *       description: 'Where the button should link to',
 *       placeholder: 'https://example.com',
 *       required: true
 *     },
 *     {
 *       name: 'backgroundColor',
 *       type: 'color',
 *       label: 'Background Color',
 *       defaultValue: '#007bff'
 *     },
 *     {
 *       name: 'textColor',
 *       type: 'color',
 *       label: 'Text Color',
 *       defaultValue: '#ffffff'
 *     }
 *   ],
 *   previewTemplate: '<a href="#" style="{{styles}}">{{content}}</a>',
 *   outputTemplate: '<table><tr><td><a href="{{buttonUrl}}" style="{{styles}}">{{content}}</a></td></tr></table>'
 * });
 * ```
 */
export const defineEmailBuilderBlock = (params: EmailBuilderBlockDefinition): EmailBuilderBlockDefinition => {
  // Validate required fields
  if (!params.id) {
    throw new Error('Email builder block ID is required. Please provide a unique identifier for the block.');
  }

  if (!params.name) {
    throw new Error('Email builder block name is required. Please provide a human-readable name for the block.');
  }

  if (!params.url) {
    throw new Error('Email builder block URL is required. Please provide the URL where the block component is hosted.');
  }

  if (!params.menuTitle || Object.keys(params.menuTitle).length === 0) {
    throw new Error('Email builder block menuTitle is required and must have at least one language. Example: { "en": "My Block" }');
  }

  if (!params.description || Object.keys(params.description).length === 0) {
    throw new Error('Email builder block description is required and must have at least one language. Example: { "en": "Description of my block" }');
  }

  if (!params.version) {
    throw new Error('Email builder block version is required. Please provide a semantic version like "1.0.0".');
  }

  if (!params.tags || params.tags.length === 0) {
    throw new Error('Email builder block tags are required and must have at least one tag. Example: ["text", "content"]');
  }

  if (!params.blockType) {
    throw new Error('Email builder block type is required. Please specify one of: text, image, button, divider, spacer, social, custom');
  }

  // Validate block type
  const validBlockTypes = ['text', 'image', 'button', 'divider', 'spacer', 'social', 'custom'];
  if (!validBlockTypes.includes(params.blockType)) {
    throw new Error(`Block type must be one of: ${validBlockTypes.join(', ')}. Received: ${params.blockType}`);
  }

  // Validate settings if provided
  if (params.settings) {
    const validSettingTypes = ['text', 'number', 'color', 'select', 'toggle', 'image', 'textarea', 'url'];
    for (const setting of params.settings) {
      if (!setting.name || !setting.type || !setting.label) {
        throw new Error('Each setting must have name, type, and label properties.');
      }
      if (!validSettingTypes.includes(setting.type)) {
        throw new Error(`Setting type must be one of: ${validSettingTypes.join(', ')}. Received: ${setting.type} for setting "${setting.name}"`);
      }
      if (setting.type === 'select' && (!setting.options || setting.options.length === 0)) {
        throw new Error(`Select-type setting "${setting.name}" must have options array with at least one option.`);
      }
    }
  }

  // Create a UI component definition with email builder block specific properties
  const uiComponentDefinition: EmailBuilderBlockDefinition = {
    ...params,
    // Set default values for optional properties
    draggable: params.draggable ?? true,
    resizable: params.resizable ?? false,
    deletable: params.deletable ?? true,
    duplicatable: params.duplicatable ?? true,
    responsive: params.responsive ?? true,
  };

  // Register the email builder block in the registry
  componentRegistry.registerEmailBuilderBlock(uiComponentDefinition);

  // Return the validated email builder block definition
  return uiComponentDefinition;
};
