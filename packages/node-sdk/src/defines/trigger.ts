/**
 * @file Trigger definition utilities for ezWorkflow
 *
 * This module provides functionality for defining workflow triggers - components that initiate
 * workflow execution in response to external events. Triggers can be HTTP-based (webhooks) or
 * socket-based (real-time connections) and define the expected data structure for incoming events.
 *
 * Triggers serve as the entry points for workflows, converting external events into structured
 * data that can be processed by workflow nodes. They include lifecycle hooks for handling
 * enable/disable events and error conditions.
 */

import { TriggerDefinition, TriggerType } from "../types";
import {
  string,
  number,
  boolean,
  options,
  object,
  array,
  stream,
  file,
} from "../field-builders";
import { componentRegistry } from "../registry";

// Create field builders object for the expect function
const fieldBuilders = {
  string,
  number,
  boolean,
  options,
  object,
  array,
  stream,
  file,
};

// Type for field builder functions
type FieldBuilderFunction = (...args: any[]) => { getDefinition(): any };
type FieldBuildersType = Record<string, FieldBuilderFunction>;

/**
 * Parameters for defining a workflow trigger
 *
 * @interface TriggerDefinitionParams
 */
interface TriggerDefinitionParams {
  /**
   * Unique identifier for the trigger. If not provided, a random ID will be generated.
   * @example 'webhook-processor' or 'realtime-data-feed'
   */
  id?: string;

  /**
   * Human-readable name for the trigger that will be displayed in the UI
   * @example 'Webhook Processor' or 'Real-time Data Feed'
   */
  name: string;

  /**
   * Semantic version of the trigger following semver format
   * @example '1.0.0' or '2.1.3-beta'
   */
  version: string;

  /**
   * Categories this trigger belongs to. Used for organization and discovery in the UI.
   * @example ['Webhooks', 'API'] or ['Real-time', 'Data Processing']
   */
  categories: string[];

  /**
   * Tags for additional categorization and searchability
   * @example ['webhook', 'http', 'api'] or ['socket', 'realtime', 'streaming']
   */
  tags: string[];

  /**
   * Type of trigger - either HTTP-based (webhook) or socket-based (real-time)
   * - 'http': Trigger responds to HTTP requests (webhooks, API calls)
   * - 'socket': Trigger maintains persistent connections for real-time data
   */
  type: "http" | "socket";

  /**
   * URL where the trigger can be registered or configured.
   * For HTTP triggers, this might be the webhook endpoint.
   * For socket triggers, this might be the connection endpoint.
   * @example 'https://api.example.com/webhooks' or 'wss://realtime.example.com/feed'
   */
  registarUrl: string;

  /**
   * Function that defines the expected data structure for incoming trigger events.
   * The function receives field builders and should return an object mapping field names to field definitions.
   *
   * @param fields - Object containing all available field builder functions
   * @returns Object mapping expected field names to their definitions
   *
   * @example
   * ```typescript
   * expect: ({ object, array, string, number, boolean }) => ({
   *   event: string().description('Event type').required(),
   *   timestamp: number().description('Event timestamp').required(),
   *   data: object({
   *     id: string().required(),
   *     name: string().required(),
   *     active: boolean().default(true)
   *   }).description('Event payload').required(),
   *   tags: array(string()).description('Event tags').optional()
   * })
   * ```
   */
  expect?: (
    fields: FieldBuildersType
  ) => Record<string, { getDefinition(): any }>;

  /**
   * Lifecycle hook called when the trigger is enabled in a workflow.
   * Use this to set up any necessary resources, register webhooks, or establish connections.
   *
   * @example
   * ```typescript
   * onEnabled: async () => {
   *   console.log('Setting up webhook endpoint...');
   *   await registerWebhook(registarUrl);
   * }
   * ```
   */
  onEnabled: () => void | Promise<void>;

  /**
   * Lifecycle hook called when the trigger is disabled or removed from a workflow.
   * Use this to clean up resources, unregister webhooks, or close connections.
   *
   * @example
   * ```typescript
   * onDisabled: async () => {
   *   console.log('Cleaning up webhook endpoint...');
   *   await unregisterWebhook(registarUrl);
   * }
   * ```
   */
  onDisabled: () => void | Promise<void>;

  /**
   * Error handler called when the trigger encounters an error during operation.
   * Use this to log errors, send notifications, or implement retry logic.
   *
   * @param error - The error that occurred
   *
   * @example
   * ```typescript
   * onError: async (error) => {
   *   console.error('Trigger error:', error.message);
   *   await notifyAdministrators(error);
   * }
   * ```
   */
  onError: (error: Error) => void | Promise<void>;
}

/**
 * Define a workflow trigger for the ezWorkflow platform
 *
 * This function creates a new trigger definition that can initiate workflow execution
 * in response to external events. Triggers serve as the entry points for workflows,
 * converting external events (HTTP requests, socket messages, etc.) into structured
 * data that can be processed by workflow nodes.
 *
 * The function automatically registers the trigger with the component registry, making
 * it available for use in workflows and discoverable through the platform's APIs.
 *
 * @param params - Configuration object containing all trigger definition parameters
 * @returns A complete TriggerDefinition object with registration
 *
 * @throws {Error} When field definitions are invalid or malformed
 *
 * @example
 * ```typescript
 * import { defineTrigger } from '@ezworkflow/node-sdk';
 *
 * // Define a webhook trigger for processing form submissions
 * const formSubmissionTrigger = defineTrigger({
 *   id: 'form-submission-webhook',
 *   name: 'Form Submission Webhook',
 *   version: '1.0.0',
 *   categories: ['Webhooks', 'Forms'],
 *   tags: ['webhook', 'form', 'submission'],
 *   type: 'http',
 *   registarUrl: 'https://api.myapp.com/webhooks/form-submission',
 *
 *   expect: ({ object, string, boolean, array }) => ({
 *     formId: string()
 *       .description('Unique identifier of the submitted form')
 *       .required(),
 *     submissionData: object({
 *       name: string().required(),
 *       email: string().required(),
 *       message: string().optional(),
 *       newsletter: boolean().default(false)
 *     })
 *       .description('Form submission data')
 *       .required(),
 *     metadata: object({
 *       timestamp: string().required(),
 *       userAgent: string().optional(),
 *       ipAddress: string().optional()
 *     })
 *       .description('Submission metadata')
 *       .optional()
 *   }),
 *
 *   onEnabled: async () => {
 *     console.log('Registering form submission webhook...');
 *     // Register webhook with external service
 *   },
 *
 *   onDisabled: async () => {
 *     console.log('Unregistering form submission webhook...');
 *     // Clean up webhook registration
 *   },
 *
 *   onError: async (error) => {
 *     console.error('Form submission trigger error:', error.message);
 *     // Handle error (logging, notifications, etc.)
 *   }
 * });
 * ```
 *
 * @example
 * ```typescript
 * // Define a real-time socket trigger for live data feeds
 * const liveDataTrigger = defineTrigger({
 *   id: 'live-data-feed',
 *   name: 'Live Data Feed',
 *   version: '1.0.0',
 *   categories: ['Real-time', 'Data'],
 *   tags: ['socket', 'realtime', 'streaming'],
 *   type: 'socket',
 *   registarUrl: 'wss://data.example.com/live-feed',
 *
 *   expect: ({ object, string, number, array }) => ({
 *     eventType: string()
 *       .description('Type of data event')
 *       .required(),
 *     timestamp: number()
 *       .description('Event timestamp in milliseconds')
 *       .required(),
 *     data: object()
 *       .description('Event data payload')
 *       .required(),
 *     sequence: number()
 *       .description('Event sequence number')
 *       .optional()
 *   }),
 *
 *   onEnabled: async () => {
 *     console.log('Establishing real-time connection...');
 *     // Set up socket connection
 *   },
 *
 *   onDisabled: async () => {
 *     console.log('Closing real-time connection...');
 *     // Close socket connection
 *   },
 *
 *   onError: async (error) => {
 *     console.error('Real-time feed error:', error.message);
 *     // Handle connection errors, implement reconnection logic
 *   }
 * });
 * ```
 */
export const defineTrigger = (
  params: TriggerDefinitionParams
): TriggerDefinition => {
  // Process expected fields
  const expectFields: Record<string, unknown> = {};
  if (params.expect) {
    const expectDefs = params.expect(fieldBuilders);
    for (const [key, builder] of Object.entries(expectDefs)) {
      if (typeof builder.getDefinition === "function") {
        expectFields[key] = builder.getDefinition();
      } else {
        throw new Error(
          `Invalid expect field definition for '${key}'. Field builders must have a getDefinition() method.`
        );
      }
    }
  }

  // Map type string to enum
  const triggerType =
    params.type === "http" ? TriggerType.HTTP : TriggerType.SOCKET;

  // Generate ID if not provided
  const id =
    params.id || `trigger-${Math.random().toString(36).substring(2, 15)}`;

  // Create trigger definition
  const triggerDefinition: TriggerDefinition = {
    id,
    name: params.name,
    version: params.version,
    categories: params.categories,
    tags: params.tags,
    type: triggerType,
    registarUrl: params.registarUrl,
    expect: expectFields,
  };

  // Register the trigger in the registry
  componentRegistry.registerTrigger(triggerDefinition);

  return triggerDefinition;
};
