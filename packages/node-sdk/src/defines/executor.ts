/**
 * @file Node executor definition utilities for ezWorkflow
 *
 * This module provides functionality for defining node executors - the actual implementation
 * logic that runs when a node is executed in a workflow. Executors receive inputs, configuration,
 * and authentication data, then perform the node's operation and return outputs.
 *
 * The module includes sophisticated TypeScript type inference that automatically provides
 * type safety based on the node definition, ensuring that executor functions receive
 * correctly typed parameters matching the node's input, config, and auth specifications.
 */

import {
  ExecutionContext,
  ExecutionResult,
  NodeExecutor,
  Logger,
  Node,
} from "../types";
import { componentRegistry } from "../registry";

/**
 * Type utility to extract input types from a node definition
 *
 * This conditional type examines a node (either as an object or ID string) and extracts
 * the types of its input fields for use in executor function type checking.
 *
 * @template N - The node type (Node object or string ID)
 */
type NodeInputs<N> = N extends Node
  ? Record<keyof N["definition"]["inputs"], unknown>
  : N extends string
    ? ReturnType<typeof componentRegistry.getNode> extends Node
      ? Record<
          keyof ReturnType<
            typeof componentRegistry.getNode
          >["definition"]["inputs"],
          unknown
        >
      : Record<string, unknown>
    : Record<string, unknown>;

/**
 * Type utility to extract config types from a node definition
 *
 * This conditional type examines a node and extracts the types of its configuration
 * fields. Returns 'never' if the node has no config fields.
 *
 * @template N - The node type (Node object or string ID)
 */
type NodeConfig<N> = N extends Node
  ? N["definition"]["config"] extends Record<string, unknown>
    ? Record<keyof N["definition"]["config"], unknown>
    : never
  : N extends string
    ? ReturnType<typeof componentRegistry.getNode> extends Node
      ? ReturnType<
          typeof componentRegistry.getNode
        >["definition"]["config"] extends Record<string, unknown>
        ? Record<
            keyof ReturnType<
              typeof componentRegistry.getNode
            >["definition"]["config"],
            unknown
          >
        : never
      : never
    : never;

/**
 * Type utility to extract auth types from a node definition
 *
 * This conditional type examines a node and extracts the types of its authentication
 * fields. Returns 'never' if the node has no auth requirements.
 *
 * @template N - The node type (Node object or string ID)
 */
type NodeAuth<N> = N extends Node
  ? N["definition"]["auth"] extends Record<string, unknown>
    ? Record<string, unknown>
    : never
  : N extends string
    ? ReturnType<typeof componentRegistry.getNode> extends Node
      ? ReturnType<
          typeof componentRegistry.getNode
        >["definition"]["auth"] extends Record<string, unknown>
        ? Record<string, unknown>
        : never
      : never
    : never;

/**
 * Type utility to extract output types from a node definition
 *
 * This conditional type examines a node and extracts the types of its output
 * fields for use in executor function return type checking.
 *
 * @template N - The node type (Node object or string ID)
 */
type NodeOutputs<N> = N extends Node
  ? Record<keyof N["definition"]["outputs"], unknown>
  : N extends string
    ? ReturnType<typeof componentRegistry.getNode> extends Node
      ? Record<
          keyof ReturnType<
            typeof componentRegistry.getNode
          >["definition"]["outputs"],
          unknown
        >
      : Record<string, unknown>
    : Record<string, unknown>;

/**
 * Executor function type for nodes with only inputs (no config or auth)
 *
 * @template I - Input types
 * @template O - Output types
 */
type ExecutorFnWithInputOnly<I, O> = (context: {
  /** Input values provided to the node */
  inputs: I;
  /** Logger instance for recording execution information */
  logger: Logger;
}) => Promise<{
  /** Whether the execution was successful */
  success: boolean;
  /** Output values produced by the node */
  outputs: O;
  /** Error message if execution failed */
  error?: string;
  /** Additional log messages from execution */
  logs?: string[];
}>;

/**
 * Executor function type for nodes with inputs and config (no auth)
 *
 * @template I - Input types
 * @template C - Config types
 * @template O - Output types
 */
type ExecutorFnWithInputAndConfig<I, C, O> = (context: {
  /** Input values provided to the node */
  inputs: I;
  /** Configuration values set for the node */
  config: C;
  /** Logger instance for recording execution information */
  logger: Logger;
}) => Promise<{
  /** Whether the execution was successful */
  success: boolean;
  /** Output values produced by the node */
  outputs: O;
  /** Error message if execution failed */
  error?: string;
  /** Additional log messages from execution */
  logs?: string[];
}>;

/**
 * Executor function type for nodes with inputs and auth (no config)
 *
 * @template I - Input types
 * @template A - Auth types
 * @template O - Output types
 */
type ExecutorFnWithInputAndAuth<I, A, O> = (context: {
  /** Input values provided to the node */
  inputs: I;
  /** Logger instance for recording execution information */
  logger: Logger;
  /** Authentication data for the node */
  auth: A;
}) => Promise<{
  /** Whether the execution was successful */
  success: boolean;
  /** Output values produced by the node */
  outputs: O;
  /** Error message if execution failed */
  error?: string;
  /** Additional log messages from execution */
  logs?: string[];
}>;

/**
 * Executor function type for nodes with inputs, config, and auth
 *
 * @template I - Input types
 * @template C - Config types
 * @template A - Auth types
 * @template O - Output types
 */
type ExecutorFnWithAll<I, C, A, O> = (context: {
  /** Input values provided to the node */
  inputs: I;
  /** Configuration values set for the node */
  config: C;
  /** Logger instance for recording execution information */
  logger: Logger;
  /** Authentication data for the node */
  auth: A;
}) => Promise<{
  /** Whether the execution was successful */
  success: boolean;
  /** Output values produced by the node */
  outputs: O;
  /** Error message if execution failed */
  error?: string;
  /** Additional log messages from execution */
  logs?: string[];
}>;

/**
 * Type selector that chooses the appropriate executor function type based on
 * what fields are available in the node (config and/or auth)
 *
 * @template I - Input types
 * @template C - Config types (or never if no config)
 * @template A - Auth types (or never if no auth)
 * @template O - Output types
 */
type SelectExecutorFn<I, C, A, O> = C extends never
  ? A extends never
    ? ExecutorFnWithInputOnly<I, O>
    : ExecutorFnWithInputAndAuth<I, A, O>
  : A extends never
    ? ExecutorFnWithInputAndConfig<I, C, O>
    : ExecutorFnWithAll<I, C, A, O>;

/**
 * Define an executor function for a workflow node
 *
 * This function creates an executor that implements the actual logic for a node.
 * The executor receives inputs, configuration, and authentication data based on
 * the node's definition, performs the required operations, and returns outputs.
 *
 * The function provides full TypeScript type safety by automatically inferring
 * the correct parameter types from the node definition. This ensures that the
 * executor function receives exactly the inputs, config, and auth fields that
 * were defined for the node.
 *
 * @template N - The node type (Node object or string ID)
 * @template I - Inferred input types from the node
 * @template C - Inferred config types from the node (never if no config)
 * @template A - Inferred auth types from the node (never if no auth)
 * @template O - Inferred output types from the node
 *
 * @param nodeId - The node ID (string) or node object this executor is for
 * @param executor - The executor function that implements the node's logic
 * @returns A wrapped executor function that can be called by the workflow engine
 *
 * @example
 * ```typescript
 * import { defineExecutor } from '@ezworkflow/node-sdk';
 *
 * // Define an executor for a text processing node
 * const textProcessorExecutor = defineExecutor('text-processor', async ({ inputs, config, logger }) => {
 *   logger.info(`Processing text: ${inputs.text}`);
 *
 *   let result = inputs.text;
 *
 *   // Apply transformation based on operation
 *   switch (inputs.operation) {
 *     case 'upper':
 *       result = result.toUpperCase();
 *       break;
 *     case 'lower':
 *       result = result.toLowerCase();
 *       break;
 *     case 'title':
 *       result = result.replace(/\w\S+/g, function(txt) {
 *         return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
 *       });
 *       break;
 *   }
 *
 *   // Trim whitespace if configured
 *   if (config.trimWhitespace) {
 *     result = result.trim();
 *   }
 *
 *   logger.info(`Processing complete. Result length: ${result.length}`);
 *
 *   return {
 *     success: true,
 *     outputs: {
 *       result,
 *       length: result.length
 *     }
 *   };
 * });
 * ```
 *
 * @example
 * ```typescript
 * // Define an executor with authentication
 * const apiExecutor = defineExecutor('api-caller', async ({ inputs, auth, logger }) => {
 *   try {
 *     logger.info(`Making API call to: ${inputs.endpoint}`);
 *
 *     const response = await fetch(inputs.endpoint, {
 *       method: 'POST',
 *       headers: {
 *         'Authorization': `Bearer ${auth.apiKey}`,
 *         'Content-Type': 'application/json'
 *       },
 *       body: JSON.stringify(inputs.payload)
 *     });
 *
 *     const data = await response.json();
 *
 *     logger.info(`API call completed with status: ${response.status}`);
 *
 *     return {
 *       success: response.ok,
 *       outputs: {
 *         response: data,
 *         statusCode: response.status
 *       },
 *       error: response.ok ? undefined : `API call failed with status ${response.status}`
 *     };
 *   } catch (error) {
 *     logger.error(`API call failed: ${error.message}`);
 *     return {
 *       success: false,
 *       outputs: {
 *         response: {},
 *         statusCode: 0
 *       },
 *       error: error.message
 *     };
 *   }
 * });
 * ```
 */
export const defineExecutor = <
  // Accept either a node object or a node ID string
  N extends Node | string,
  // Infer input types from the node if provided, otherwise use a generic record
  I = NodeInputs<N>,
  // Infer config types from the node if provided, otherwise use never
  C = NodeConfig<N>,
  // Infer auth types from the node if provided, otherwise use never
  A = NodeAuth<N>,
  // Infer output types from the node if provided, otherwise use a generic record
  O = NodeOutputs<N>,
>(
  // The node ID or node object
  nodeId: N,
  // The executor function with properly typed parameters based on what's in the node
  executor: SelectExecutorFn<I, C, A, O>
): NodeExecutor<O> => {
  // Get the node ID
  const id = typeof nodeId === "string" ? nodeId : nodeId.definition.id;

  // Log a warning if the node is not registered
  if (typeof nodeId === "string" && !componentRegistry.hasNode(id)) {
    console.warn(
      `Node with ID ${id} is not registered. Type inference may not work correctly.`
    );
  }

  // Return a wrapped executor function
  return async (context: ExecutionContext): Promise<ExecutionResult<O>> => {
    try {
      // Create the executor context based on what's available in the node
      const executorContext: any = {
        inputs: context.inputs as I,
        logger: context.logger,
      };

      // Add config and auth if they're available
      if (context.config) {
        executorContext.config = context.config as C;
      }

      if (context.auth) {
        executorContext.auth = context.auth as A;
      }

      // Execute the user-provided executor
      const result = await executor(executorContext);

      // Return the result
      return {
        success: result.success,
        outputs: result.outputs,
        error: result.error,
        logs: result.logs,
      };
    } catch (error) {
      // Handle any uncaught errors
      return {
        success: false,
        outputs: {} as O,
        error: error instanceof Error ? error.message : String(error),
        logs: [],
      };
    }
  };
};
