/**
 * @file Centralized exports for all component definition functions
 * 
 * This module provides a single entry point for all ezWorkflow component definition functions.
 * It exports all the define functions for creating nodes, executors, triggers, and UI components,
 * making it easier to import and use these functions throughout the codebase.
 * 
 * @since 0.1.0
 */

// Core component definitions
export { defineNode } from './node';
export { defineExecutor } from './executor';
export { defineTrigger } from './trigger';

// UI component definitions
export { definePage } from './page';
export { defineModal } from './modal';
export { defineDrawer } from './drawer';
export { defineDashboardWidget } from './dashboard-widget';
export { defineEmailBuilderBlock } from './email-builder-block';
export { defineFunnelBuilderBlock } from './funnel-builder-block';

// Re-export types that are commonly used with define functions
export type { NodeDefinition, Node } from '../types';
export type { TriggerDefinition } from '../types';
export type { PageDefinition } from './page';
export type { ModalDefinition } from './modal';
export type { DrawerDefinition } from './drawer';
export type { DashboardWidgetDefinition } from './dashboard-widget';
export type { EmailBuilderBlockDefinition } from './email-builder-block';
export type { FunnelBuilderBlockDefinition } from './funnel-builder-block';
