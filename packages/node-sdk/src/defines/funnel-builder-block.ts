/**
 * @file Funnel builder block component definition utilities for ezWorkflow
 *
 * This module provides functionality for defining funnel builder block components that can be
 * integrated into the ezWorkflow platform's funnel builder interface. Funnel builder blocks are
 * modular, reusable components that users can drag and drop to construct sales funnels, landing pages,
 * and conversion-focused web pages with various layout elements and interactive components.
 * 
 * Block components extend the base UI component definition with funnel-specific properties
 * such as block type, layout behavior, default content, styling, and configuration settings.
 */

import { componentRegistry } from '../registry';
import { UiComponentDefinition } from '../ui';

/**
 * Configuration setting for funnel builder blocks
 * 
 * @interface BlockSetting
 */
interface BlockSetting {
  /** Unique name/key for the setting */
  name: string;
  /** Type of input control for the setting */
  type: 'text' | 'number' | 'color' | 'select' | 'toggle' | 'image' | 'textarea' | 'url' | 'spacing' | 'alignment';
  /** Display label for the setting */
  label: string;
  /** Optional description explaining the setting's purpose */
  description?: string;
  /** Default value for the setting */
  defaultValue?: string | number | boolean;
  /** Options for select-type settings */
  options?: Array<{ label: string; value: string }>;
  /** Minimum value for number-type settings */
  min?: number;
  /** Maximum value for number-type settings */
  max?: number;
  /** Whether the setting is required */
  required?: boolean;
  /** Placeholder text for input fields */
  placeholder?: string;
}

/**
 * Funnel builder block component definition interface
 *
 * This interface extends the base UI component definition with funnel builder block-specific
 * properties that control the block's behavior, layout, and configuration options within
 * the funnel builder environment.
 * 
 * @interface FunnelBuilderBlockDefinition
 * @extends UiComponentDefinition
 */
export interface FunnelBuilderBlockDefinition extends UiComponentDefinition {
  /** 
   * Type of the funnel builder block
   * - 'section': Full-width container sections for organizing content
   * - 'column': Column layout blocks for multi-column designs
   * - 'row': Horizontal row containers for organizing elements
   * - 'button': Call-to-action buttons and interactive elements
   * - 'divider': Visual separators and spacing elements
   * - 'spacer': Empty space blocks for layout control
   * - 'social': Social media integration and sharing components
   * - 'custom': Custom HTML or specialized funnel components
   */
  blockType: 'section' | 'column' | 'row' | 'button' | 'divider' | 'spacer' | 'social' | 'custom';

  /** 
   * Whether the block can be dragged and repositioned within the funnel
   * @default true
   */
  draggable?: boolean;

  /** 
   * Whether the block can be resized by users
   * @default false
   */
  resizable?: boolean;

  /** 
   * Whether the block can be deleted from the funnel
   * @default true
   */
  deletable?: boolean;

  /** 
   * Whether the block can be duplicated/copied
   * @default true
   */
  duplicatable?: boolean;

  /** 
   * Default content for the block when first added to a funnel
   * For section blocks: default section content or structure
   * For button blocks: default button text
   * For custom blocks: default HTML or configuration
   * @example "Get Started Today" or "<div>Custom content</div>"
   */
  defaultContent?: string;

  /** 
   * Default CSS styles applied to the block
   * @example { "background-color": "#ffffff", "padding": "20px", "text-align": "center" }
   */
  defaultStyles?: Record<string, string>;

  /** 
   * Configuration settings available for customizing the block
   * These settings appear in the funnel builder's property panel when the block is selected.
   */
  settings?: BlockSetting[];

  /** 
   * Whether the block supports responsive design
   * When true, the block will adapt to different screen sizes
   * @default true
   */
  responsive?: boolean;

  /** 
   * Whether the block can contain other blocks (container behavior)
   * When true, other blocks can be dropped inside this block
   * @default false
   */
  isContainer?: boolean;

  /** 
   * Maximum number of child blocks this container can hold
   * Only applies when isContainer is true
   * @example 3, 6, 12
   */
  maxChildren?: number;

  /** 
   * Allowed child block types for container blocks
   * Only applies when isContainer is true
   * @example ['button', 'divider'] or ['column']
   */
  allowedChildren?: string[];

  /** 
   * Preview template for displaying the block in the builder
   * Can include placeholders that will be replaced with actual content
   * @example "<div class='funnel-section' style='{{styles}}'>{{content}}</div>"
   */
  previewTemplate?: string;

  /** 
   * Output template for generating the final funnel HTML
   * Should include all necessary HTML structure and responsive styles
   * @example "<section class='funnel-section' style='{{styles}}'>{{content}}</section>"
   */
  outputTemplate?: string;

  /** 
   * Whether the block supports A/B testing variations
   * When true, users can create multiple versions of the block for testing
   * @default false
   */
  abTestingSupport?: boolean;

  /** 
   * Analytics tracking configuration for the block
   * Defines what events and interactions should be tracked
   */
  analytics?: {
    /** Whether to track views of this block */
    trackViews?: boolean;
    /** Whether to track clicks on this block */
    trackClicks?: boolean;
    /** Custom events to track */
    customEvents?: string[];
  };
}

/**
 * Define a funnel builder block component for the ezWorkflow platform
 *
 * This function creates a new funnel builder block component definition that can be integrated
 * into the ezWorkflow platform's funnel builder interface. Funnel builder blocks provide modular,
 * reusable components that users can drag and drop to construct high-converting sales funnels,
 * landing pages, and marketing pages with various layout and interactive elements.
 * 
 * The function validates required fields, applies sensible defaults for optional properties,
 * validates block settings and container configurations, registers the block with the component
 * registry, and makes it available for use in funnel builder configurations.
 *
 * @param params - Funnel builder block component definition parameters
 * @returns Complete FunnelBuilderBlockDefinition object with validation and registration
 * 
 * @throws {Error} When required fields are missing or invalid
 * @throws {Error} When blockType is not one of the valid options
 * @throws {Error} When container configuration is invalid
 * @throws {Error} When settings configuration is invalid
 * 
 * @example
 * ```typescript
 * import { defineFunnelBuilderBlock } from '@ezworkflow/node-sdk';
 * 
 * // Define a hero section block
 * const heroSectionBlock = defineFunnelBuilderBlock({
 *   id: 'hero-section',
 *   name: 'Hero Section',
 *   menuTitle: {
 *     'en': 'Hero Section',
 *     'es': 'Sección Hero',
 *     'fr': 'Section Hero'
 *   },
 *   description: {
 *     'en': 'Full-width hero section with headline, subtext, and call-to-action',
 *     'es': 'Sección hero de ancho completo con titular, subtexto y llamada a la acción',
 *     'fr': 'Section hero pleine largeur avec titre, sous-texte et appel à l\'action'
 *   },
 *   version: '1.0.0',
 *   categories: ['Layout', 'Hero'],
 *   type: 'account',
 *   visibility: 'account',
 *   tags: ['hero', 'section', 'layout', 'cta'],
 *   icon: 'mdi:view-headline',
 *   url: 'https://my-app.com/funnel-blocks/hero-section',
 *   blockType: 'section',
 *   isContainer: true,
 *   maxChildren: 5,
 *   allowedChildren: ['button', 'divider', 'spacer'],
 *   defaultStyles: {
 *     'background-color': '#f8f9fa',
 *     'padding': '80px 20px',
 *     'text-align': 'center',
 *     'min-height': '500px'
 *   },
 *   settings: [
 *     {
 *       name: 'backgroundColor',
 *       type: 'color',
 *       label: 'Background Color',
 *       defaultValue: '#f8f9fa'
 *     },
 *     {
 *       name: 'textAlignment',
 *       type: 'select',
 *       label: 'Text Alignment',
 *       defaultValue: 'center',
 *       options: [
 *         { label: 'Left', value: 'left' },
 *         { label: 'Center', value: 'center' },
 *         { label: 'Right', value: 'right' }
 *       ]
 *     }
 *   ],
 *   abTestingSupport: true,
 *   analytics: {
 *     trackViews: true,
 *     trackClicks: false,
 *     customEvents: ['hero_viewed', 'cta_clicked']
 *   }
 * });
 * ```
 * 
 * @example
 * ```typescript
 * // Define a conversion button block
 * const conversionButtonBlock = defineFunnelBuilderBlock({
 *   id: 'conversion-button',
 *   name: 'Conversion Button',
 *   menuTitle: {
 *     'en': 'CTA Button',
 *     'es': 'Botón CTA',
 *     'fr': 'Bouton CTA'
 *   },
 *   description: {
 *     'en': 'High-converting call-to-action button with tracking',
 *     'es': 'Botón de llamada a la acción de alta conversión con seguimiento',
 *     'fr': 'Bouton d\'appel à l\'action à haute conversion avec suivi'
 *   },
 *   version: '1.0.0',
 *   categories: ['Interactive', 'Conversion'],
 *   type: 'account',
 *   visibility: 'account',
 *   tags: ['button', 'cta', 'conversion', 'tracking'],
 *   icon: 'mdi:cursor-pointer',
 *   url: 'https://my-app.com/funnel-blocks/conversion-button',
 *   blockType: 'button',
 *   defaultContent: 'Get Started Now',
 *   defaultStyles: {
 *     'background-color': '#28a745',
 *     'color': '#ffffff',
 *     'padding': '15px 30px',
 *     'border-radius': '8px',
 *     'font-size': '18px',
 *     'font-weight': 'bold',
 *     'border': 'none',
 *     'cursor': 'pointer'
 *   },
 *   settings: [
 *     {
 *       name: 'buttonText',
 *       type: 'text',
 *       label: 'Button Text',
 *       defaultValue: 'Get Started Now',
 *       required: true
 *     },
 *     {
 *       name: 'buttonUrl',
 *       type: 'url',
 *       label: 'Button URL',
 *       description: 'Where the button should redirect',
 *       placeholder: 'https://example.com/signup',
 *       required: true
 *     },
 *     {
 *       name: 'buttonSize',
 *       type: 'select',
 *       label: 'Button Size',
 *       defaultValue: 'large',
 *       options: [
 *         { label: 'Small', value: 'small' },
 *         { label: 'Medium', value: 'medium' },
 *         { label: 'Large', value: 'large' }
 *       ]
 *     }
 *   ],
 *   abTestingSupport: true,
 *   analytics: {
 *     trackViews: true,
 *     trackClicks: true,
 *     customEvents: ['button_hover', 'conversion_attempt']
 *   }
 * });
 * ```
 */
export const defineFunnelBuilderBlock = (params: FunnelBuilderBlockDefinition): FunnelBuilderBlockDefinition => {
  // Validate required fields
  if (!params.id) {
    throw new Error('Funnel builder block ID is required. Please provide a unique identifier for the block.');
  }

  if (!params.name) {
    throw new Error('Funnel builder block name is required. Please provide a human-readable name for the block.');
  }

  if (!params.url) {
    throw new Error('Funnel builder block URL is required. Please provide the URL where the block component is hosted.');
  }

  if (!params.blockType) {
    throw new Error('Funnel builder block type is required. Please specify one of: section, column, row, button, divider, spacer, social, custom');
  }

  // Validate block type
  const validBlockTypes = ['section', 'column', 'row', 'button', 'divider', 'spacer', 'social', 'custom'];
  if (!validBlockTypes.includes(params.blockType)) {
    throw new Error(`Block type must be one of: ${validBlockTypes.join(', ')}. Received: ${params.blockType}`);
  }

  // Validate container configuration
  if (params.isContainer) {
    if (params.maxChildren !== undefined && (params.maxChildren < 1 || !Number.isInteger(params.maxChildren))) {
      throw new Error('maxChildren must be a positive integer when isContainer is true.');
    }
    if (params.allowedChildren && params.allowedChildren.length === 0) {
      throw new Error('allowedChildren cannot be an empty array when specified.');
    }
  }

  // Validate settings if provided
  if (params.settings) {
    const validSettingTypes = ['text', 'number', 'color', 'select', 'toggle', 'image', 'textarea', 'url', 'spacing', 'alignment'];
    for (const setting of params.settings) {
      if (!setting.name || !setting.type || !setting.label) {
        throw new Error('Each setting must have name, type, and label properties.');
      }
      if (!validSettingTypes.includes(setting.type)) {
        throw new Error(`Setting type must be one of: ${validSettingTypes.join(', ')}. Received: ${setting.type} for setting "${setting.name}"`);
      }
      if (setting.type === 'select' && (!setting.options || setting.options.length === 0)) {
        throw new Error(`Select-type setting "${setting.name}" must have options array with at least one option.`);
      }
    }
  }

  // Create a UI component definition with funnel builder block specific properties
  const uiComponentDefinition: FunnelBuilderBlockDefinition = {
    ...params,
    // Set default values for optional properties
    screenType: 'fullPage',
    draggable: params.draggable ?? true,
    resizable: params.resizable ?? false,
    deletable: params.deletable ?? true,
    duplicatable: params.duplicatable ?? true,
    responsive: params.responsive ?? true,
    isContainer: params.isContainer ?? false,
    abTestingSupport: params.abTestingSupport ?? false,
    analytics: params.analytics ?? {
      trackViews: false,
      trackClicks: false,
      customEvents: []
    }
  };

  // Register the funnel builder block in the registry
  componentRegistry.registerFunnelBuilderBlock(uiComponentDefinition);

  // Return the validated funnel builder block definition
  return uiComponentDefinition;
};
