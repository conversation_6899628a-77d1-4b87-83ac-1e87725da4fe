import { Hono } from 'hono'
import { contextStorage } from 'hono/context-storage'
import { cache } from 'hono/cache'
import { logger } from 'hono/logger';
import { requestId } from 'hono/request-id'
// import { z } from 'zod'
// import { zValidator } from '@hono/zod-validator'
import { timeout } from 'hono/timeout'
import { getConfig } from '@ezworkflow/project-config'
import { bearerAuth } from 'hono/bearer-auth'
import { compress } from 'hono/compress'
// import { secureHeaders } from 'hono/secure-headers'
// import { upgradeWebSocket } from 'hono/cloudflare-workers'
// import { upgradeWebSocket as denoUpgradeWebSocket } from 'hono/deno'
// import { createBunWebSocket } from 'hono/bun'
// import type { ServerWebSocket } from 'bun'


const sdkApp = () => {
    const app = new Hono()
    const config = getConfig()

    app.use(logger())
    app.use(requestId())
    app.use(contextStorage())
    app.use(timeout(29 * 1000))

    /**
     * Currently we only support caching in cloudflare workers and deno.
     */
    if (config.runtime === 'cloudflare-workers' || config.runtime === 'deno') {
        app.get("*", cache({
            cacheName: "ezw-sdk-cache",
            cacheControl: "max-age=29"
        }))
    } else {
        /**
         * In cloudflare workers and deno we don't need to compress the response
         * because the worker and deno will handle it automatically.
         */
        app.use(compress())
    }

    if (config.apiKey) {
        app.use(bearerAuth({
            token: config.apiKey
        }))
    }

    return app
}

export default sdkApp