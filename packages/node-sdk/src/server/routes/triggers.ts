/**
 * @file Trigger-related routes
 */

import { Hono } from 'hono';
import { ServerContext, requirePermission, withErrorHandling } from './middleware';

/**
 * Create trigger routes
 * 
 * @param context Server context
 * @returns Hono router with trigger routes
 */
export default function createTriggerRoutes(context: ServerContext): Hono {
  const router = new Hono();

  // Get all triggers
  router.get('/', 
    requirePermission(context, 'read'),
    withErrorHandling((c) => {
      const triggersObj: Record<string, any> = {};

      for (const [id, trigger] of context.triggers.entries()) {
        const { name, version, categories, tags, type } = trigger;

        triggersObj[id] = {
          name,
          version,
          categories,
          tags,
          type,
        };
      }

      return c.json({ triggers: triggersObj });
    })
  );

  // Get trigger by ID
  router.get('/:id',
    requirePermission(context, 'read'),
    withErrorHandling((c) => {
      const id = c.req.param('id');
      const trigger = context.triggers.get(id);

      if (!trigger) {
        return c.json({ error: `Trigger with ID ${id} not found` }, 404);
      }

      return c.json({ trigger });
    })
  );

  return router;
}
