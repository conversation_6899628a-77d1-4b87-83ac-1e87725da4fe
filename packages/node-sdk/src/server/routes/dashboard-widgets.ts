/**
 * @file Dashboard widget routes
 */

import { Hono } from 'hono';
import { ServerContext, requirePermission, withErrorHandling } from './middleware';

/**
 * Create dashboard widget routes
 * 
 * @param context Server context
 * @returns Hono router with dashboard widget routes
 */
export default function createDashboardWidgetRoutes(context: ServerContext): Hono {
  const router = new Hono();

  // Get all dashboard widgets
  router.get('/',
    requirePermission(context, 'read'),
    withErrorHandling((c) => {
      const widgetsObj: Record<string, any> = {};

      for (const [id, widget] of context.dashboardWidgets.entries()) {
        const { name, version, categories, tags, visibility, widgetSize } = widget;

        widgetsObj[id] = {
          name,
          version,
          categories,
          tags,
          visibility,
          widgetSize,
        };
      }

      return c.json({ dashboardWidgets: widgetsObj });
    })
  );

  // Get dashboard widget by ID
  router.get('/:id',
    requirePermission(context, 'read'),
    withErrorHandling((c) => {
      const id = c.req.param('id');
      const widget = context.dashboardWidgets.get(id);

      if (!widget) {
        return c.json(
          { error: `Dashboard widget with ID ${id} not found` },
          404
        );
      }

      return c.json({ dashboardWidget: widget });
    })
  );

  return router;
}
