/**
 * @file Drawer routes
 */

import { Hono } from 'hono';
import { ServerContext, requirePermission, withErrorHandling } from './middleware';

/**
 * Create drawer routes
 * 
 * @param context Server context
 * @returns Hono router with drawer routes
 */
export default function createDrawerRoutes(context: ServerContext): Hono {
  const router = new Hono();

  // Get all drawers
  router.get('/',
    requirePermission(context, 'read'),
    withErrorHandling((c) => {
      const drawersObj: Record<string, any> = {};

      for (const [id, drawer] of context.drawers.entries()) {
        const { name, version, categories, tags, visibility, drawerSize, position } = drawer;

        drawersObj[id] = {
          name,
          version,
          categories,
          tags,
          visibility,
          drawerSize,
          position,
        };
      }

      return c.json({ drawers: drawersObj });
    })
  );

  // Get drawer by ID
  router.get('/:id',
    requirePermission(context, 'read'),
    withErrorHandling((c) => {
      const id = c.req.param('id');
      const drawer = context.drawers.get(id);

      if (!drawer) {
        return c.json({ error: `Drawer with ID ${id} not found` }, 404);
      }

      return c.json({ drawer });
    })
  );

  return router;
}
