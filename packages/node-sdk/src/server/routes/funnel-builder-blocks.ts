/**
 * @file Funnel builder block routes
 */

import { Hono } from 'hono';
import { ServerContext, requirePermission, withErrorHandling } from './middleware';

/**
 * Create funnel builder block routes
 * 
 * @param context Server context
 * @returns Hono router with funnel builder block routes
 */
export default function createFunnelBuilderBlockRoutes(context: ServerContext): Hono {
  const router = new Hono();

  // Get all funnel builder blocks
  router.get('/',
    requirePermission(context, 'read'),
    withErrorHandling((c) => {
      const blocksObj: Record<string, any> = {};

      for (const [id, block] of context.funnelsBuilderBlocks.entries()) {
        const { name, version, categories, tags, visibility, blockType } = block;

        blocksObj[id] = {
          name,
          version,
          categories,
          tags,
          visibility,
          blockType,
        };
      }

      return c.json({ funnelBuilderBlocks: blocksObj });
    })
  );

  // Get funnel builder block by ID
  router.get('/:id',
    requirePermission(context, 'read'),
    withErrorHandling((c) => {
      const id = c.req.param('id');
      const block = context.funnelsBuilderBlocks.get(id);

      if (!block) {
        return c.json(
          { error: `Funnel builder block with ID ${id} not found` },
          404
        );
      }

      return c.json({ funnelBuilderBlock: block });
    })
  );

  return router;
}
