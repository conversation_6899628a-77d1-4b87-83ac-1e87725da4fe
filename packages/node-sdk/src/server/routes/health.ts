/**
 * @file Health and status routes
 */

import { Hono } from 'hono';
import { ServerContext, withErrorHandling } from './middleware';

/**
 * Create health routes
 * 
 * @param context Server context
 * @returns Hono router with health routes
 */
export default function createHealthRoutes(context: ServerContext): Hono {
  const router = new Hono();

  // Health check endpoint - no authentication required
  router.get('/', withErrorHandling((c) => {
    return c.json({ status: 'ok' });
  }));

  return router;
}
