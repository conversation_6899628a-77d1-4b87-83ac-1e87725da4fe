/**
 * @file Node-related routes
 */

import { Hono } from "hono";
import { ServerContext, withErrorHandling } from "./middleware";

/**
 * Create node routes
 *
 * @param context Server context
 * @returns Hono router with node routes
 */
export default function createNodeRoutes(context: ServerContext): Hono {
  const router = new Hono();

  // Get all nodes
  router.get(
    "/",
    withErrorHandling((c) => {
      const nodesObj: Record<
        string,
        {
          name: string;
          description: string;
          categories: string[];
          tags: string[];
        }
      > = {};

      for (const [id, node] of context.nodes.entries()) {
        const { name, description, category, tags } = node.definition;

        // Ensure category is always an array
        const categories = Array.isArray(category) ? category : [category];

        nodesObj[id] = {
          name,
          description,
          categories,
          tags: tags || [],
        };
      }
      return c.json({ nodes: nodesObj });
    })
  );

  // Get node by ID
  router.get(
    "/:id",
    withErrorHandling((c) => {
      const id = c.req.param("id");
      const node = context.nodes.get(id);

      if (!node) {
        return c.json({ error: `Node with ID ${id} not found` }, 404);
      }

      return c.json({ node: node.definition });
    })
  );

  // Execute node
  router.post(
    "/:id/execute",
    withErrorHandling(async (c) => {
      const id = c.req.param("id");
      const node = context.nodes.get(id);

      if (!node) {
        return c.json({ error: `Node with ID ${id} not found` }, 404);
      }

      const executor = context.executors.get(id);

      if (!executor) {
        return c.json(
          { error: `Executor for node with ID ${id} not found` },
          404
        );
      }

      const body = await c.req.json();
      const { inputs, config, auth } = body;

      // Create a simple logger
      const logger = {
        info: (message: string) => console.info(`[${id}] ${message}`),
        warn: (message: string) => console.warn(`[${id}] ${message}`),
        error: (message: string) => console.error(`[${id}] ${message}`),
        debug: (message: string) => console.debug(`[${id}] ${message}`),
      };

      // Execute the node
      const result = await executor({ inputs, config, auth, logger });

      // Add additional fields for compatibility
      return c.json({
        ...result,
        delay: 0,
        reExecute: false,
        tryAgain: false,
        retryDelay: 0,
      });
    })
  );

  return router;
}
