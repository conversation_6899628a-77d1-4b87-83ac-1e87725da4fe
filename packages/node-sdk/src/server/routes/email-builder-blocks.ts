/**
 * @file Email builder block routes
 */

import { Hono } from 'hono';
import { ServerContext, requirePermission, withErrorHandling } from './middleware';

/**
 * Create email builder block routes
 * 
 * @param context Server context
 * @returns Hono router with email builder block routes
 */
export default function createEmailBuilderBlockRoutes(context: ServerContext): Hono {
  const router = new Hono();

  // Get all email builder blocks
  router.get('/',
    requirePermission(context, 'read'),
    withErrorHandling((c) => {
      const blocksObj: Record<string, any> = {};

      for (const [id, block] of context.emailBuilderBlocks.entries()) {
        const { name, version, categories, tags, visibility, blockType } = block;

        blocksObj[id] = {
          name,
          version,
          categories,
          tags,
          visibility,
          blockType,
        };
      }

      return c.json({ emailBuilderBlocks: blocksObj });
    })
  );

  // Get email builder block by ID
  router.get('/:id',
    requirePermission(context, 'read'),
    withErrorHandling((c) => {
      const id = c.req.param('id');
      const block = context.emailBuilderBlocks.get(id);

      if (!block) {
        return c.json(
          { error: `Email builder block with ID ${id} not found` },
          404
        );
      }

      return c.json({ emailBuilderBlock: block });
    })
  );

  return router;
}
