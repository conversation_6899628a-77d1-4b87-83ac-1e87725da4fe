/**
 * @file Modal routes
 */

import { Hono } from 'hono';
import { ServerContext, requirePermission, withErrorHandling } from './middleware';

/**
 * Create modal routes
 * 
 * @param context Server context
 * @returns Hono router with modal routes
 */
export default function createModalRoutes(context: ServerContext): Hono {
  const router = new Hono();

  // Get all modals
  router.get('/',
    requirePermission(context, 'read'),
    withErrorHandling((c) => {
      const modalsObj: Record<string, any> = {};

      for (const [id, modal] of context.modals.entries()) {
        const { name, version, categories, tags, visibility, modalSize } = modal;

        modalsObj[id] = {
          name,
          version,
          categories,
          tags,
          visibility,
          modalSize,
        };
      }

      return c.json({ modals: modalsObj });
    })
  );

  // Get modal by ID
  router.get('/:id',
    requirePermission(context, 'read'),
    withErrorHandling((c) => {
      const id = c.req.param('id');
      const modal = context.modals.get(id);

      if (!modal) {
        return c.json({ error: `Modal with ID ${id} not found` }, 404);
      }

      return c.json({ modal });
    })
  );

  return router;
}
