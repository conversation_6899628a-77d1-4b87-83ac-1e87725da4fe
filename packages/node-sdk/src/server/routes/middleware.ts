/**
 * @file Shared middleware and utility functions for routes
 */

import { Context, Next } from 'hono';

/**
 * Server context interface containing all the data and utilities needed by routes
 */
export interface ServerContext {
  nodes: Map<string, any>;
  executors: Map<string, any>;
  triggers: Map<string, any>;
  pages: Map<string, any>;
  modals: Map<string, any>;
  dashboardWidgets: Map<string, any>;
  drawers: Map<string, any>;
  emailBuilderBlocks: Map<string, any>;
  funnelsBuilderBlocks: Map<string, any>;
  apiKey?: string;
  permissions: string[];
  options: any;
}

/**
 * Check if the current request has the required permission
 * 
 * @param context Server context
 * @param permission The permission to check
 * @returns True if the permission is granted, false otherwise
 */
export function hasPermission(context: ServerContext, permission: string): boolean {
  // If no permissions are specified, all operations are allowed
  if (context.permissions.length === 0) {
    return true;
  }

  // 'admin' permission grants access to all operations
  if (context.permissions.includes('admin')) {
    return true;
  }

  // Check if the specific permission is granted
  return context.permissions.includes(permission);
}

/**
 * Middleware to check permissions for a route
 * 
 * @param context Server context
 * @param requiredPermission The permission required for this route
 * @returns Middleware function
 */
export function requirePermission(context: ServerContext, requiredPermission: string) {
  return async (c: Context, next: Next) => {
    if (!hasPermission(context, requiredPermission)) {
      return c.json({ error: 'Permission denied' }, 403);
    }
    await next();
  };
}

/**
 * Middleware to handle common error responses
 * 
 * @param handler Route handler function
 * @returns Wrapped handler with error handling
 */
export function withErrorHandling(handler: (c: Context) => Promise<Response> | Response) {
  return async (c: Context) => {
    try {
      return await handler(c);
    } catch (error) {
      console.error('Route error:', error);
      return c.json({ 
        error: error instanceof Error ? error.message : 'Internal server error' 
      }, 500);
    }
  };
}
