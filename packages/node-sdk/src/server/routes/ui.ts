/**
 * @file Main UI router that combines all UI component route modules
 *
 * This file provides the main /ui router that mounts all individual UI component routes
 * under their respective sub-paths while maintaining the unified /ui base route structure.
 */

import { Hono } from "hono";
import { ServerContext } from "./middleware";
import createDashboardWidgetRoutes from "./dashboard-widgets";
import createPageRoutes from "./pages";
import createModalRoutes from "./modals";
import createDrawerRoutes from "./drawers";
import createEmailBuilderBlockRoutes from "./email-builder-blocks";
import createFunnelBuilderBlockRoutes from "./funnel-builder-blocks";

/**
 * Create main UI router with all UI component route modules
 *
 * @param context Server context
 * @returns Hono router with all UI component routes mounted
 */
export default function createUiRoutes(context: ServerContext): Hono {
  const router = new Hono();

  // Mount individual UI component route modules
  router.route("/dashboard-widgets", createDashboardWidgetRoutes(context));
  router.route("/pages", createPageRoutes(context));
  router.route("/modals", createModalRoutes(context));
  router.route("/drawers", createDrawerRoutes(context));
  router.route("/email-builder-blocks", createEmailBuilderBlockRoutes(context));
  router.route(
    "/funnel-builder-blocks",
    createFunnelBuilderBlockRoutes(context)
  );

  return router;
}
